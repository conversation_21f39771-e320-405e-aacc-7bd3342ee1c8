# MemoCare Public Website

This is the public marketing website for MemoCare - your personal companion for sharing photos, telling stories, and celebrating life's precious moments with family.

## 🌟 Features

- **Modern Design**: Dignified, empowering design with warm color palette
- **Interactive Demo**: One-click demo access with patient/caregiver experiences
- **Performance Optimized**: Fast loading with optimized images and code
- **SEO Ready**: Proper meta tags, structured data, and semantic HTML
- **Accessibility First**: WCAG 2.1 AA compliant with screen reader support
- **Mobile Responsive**: Perfect experience on all screen sizes
- **Analytics Ready**: Google Analytics integration ready
- **PWA Support**: Service worker for offline functionality

## 📁 File Structure

```
website/
├── index.html              # Main landing page
├── styles/
│   └── main.css            # Main stylesheet
├── scripts/
│   └── main.js             # Main JavaScript
├── images/                 # Image assets (to be added)
│   ├── logo.svg
│   ├── hero-app-screenshot.png
│   ├── step-1-upload.png
│   ├── step-2-chat.png
│   ├── step-3-family.png
│   ├── testimonial-1.jpg
│   ├── testimonial-2.jpg
│   ├── testimonial-3.jpg
│   ├── hipaa-compliant.svg
│   ├── gdpr-compliant.svg
│   ├── soc2-certified.svg
│   └── ada-accessible.svg
├── favicon.ico
├── apple-touch-icon.png
├── favicon-32x32.png
├── favicon-16x16.png
└── README.md
```

## 🎨 Design System

### Colors

- **Primary**: Indigo (#6366F1, #4F46E5, #4338CA) - matches app design
- **Warm**: Orange (#F7B955, #F59E0B, #D97706) - accent colors
- **Gray Scale**: From #f9fafb to #111827
- **Success**: #22C55E
- **Warning**: #F59E0B
- **Error**: #EF4444

### Typography

- **Font**: Inter (Google Fonts)
- **Sizes**: 0.75rem to 3.75rem
- **Weights**: 300, 400, 500, 600, 700

### Spacing

- **Scale**: 0.25rem to 6rem
- **Grid**: CSS Grid with responsive breakpoints

## 🚀 Getting Started

### Local Development

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd website
   ```

2. **Serve locally**

   ```bash
   # Using Python
   python -m http.server 8000

   # Using Node.js
   npx serve .

   # Using PHP
   php -S localhost:8000
   ```

3. **Open in browser**
   ```
   http://localhost:8000
   ```

### Production Deployment

The website is static and can be deployed to any web server or CDN:

- **Netlify**: Drag and drop the website folder
- **Vercel**: Connect GitHub repository
- **AWS S3**: Upload files to S3 bucket with static hosting
- **GitHub Pages**: Push to gh-pages branch
- **Traditional Hosting**: Upload via FTP/SFTP

## 🎮 Interactive Demo

The website features an interactive demo section that allows visitors to experience MemoCare immediately:

### Demo Accounts

- **Patient Experience**: `<EMAIL>` / `demodemo`
- **Family Experience**: `<EMAIL>` / `demodemo`

### Demo Features

- One-click access to live app demo
- Pre-populated with sample data
- Safe sandbox environment
- Automatic login handling
- Analytics tracking for demo usage

### Demo URLs

- Patient demo: `../index.html?demo=patient`
- Caregiver demo: `../index.html?demo=caregiver`

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## ♿ Accessibility Features

- **Semantic HTML**: Proper heading hierarchy and landmarks
- **ARIA Labels**: Screen reader support
- **Keyboard Navigation**: Full keyboard accessibility
- **Focus Management**: Visible focus indicators
- **Color Contrast**: WCAG AA compliant contrast ratios
- **Reduced Motion**: Respects user motion preferences
- **High Contrast**: Supports high contrast mode

## 🔧 Customization

### Adding New Sections

1. **HTML Structure**

   ```html
   <section class="new-section">
     <div class="container">
       <div class="section-header">
         <h2 class="section-title">Section Title</h2>
         <p class="section-subtitle">Section description</p>
       </div>
       <!-- Section content -->
     </div>
   </section>
   ```

2. **CSS Styling**
   ```css
   .new-section {
     padding: var(--spacing-20) 0;
     /* Add custom styles */
   }
   ```

### Updating Colors

Update CSS custom properties in `:root`:

```css
:root {
  --primary-600: #your-color;
  /* Update other color variables */
}
```

### Adding Analytics

Add Google Analytics to the `<head>` section:

```html
<!-- Google Analytics -->
<script
  async
  src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"
></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag() {
    dataLayer.push(arguments);
  }
  gtag("js", new Date());
  gtag("config", "GA_MEASUREMENT_ID");
</script>
```

## 📊 Performance Optimization

### Images

- Use WebP format for better compression
- Implement lazy loading for below-fold images
- Provide multiple sizes for responsive images

### CSS

- Critical CSS is inlined
- Non-critical CSS is loaded asynchronously
- CSS is minified for production

### JavaScript

- Minimal JavaScript for core functionality
- Modules are loaded asynchronously
- Code is minified for production

## 🧪 Testing

### Manual Testing Checklist

- [ ] All links work correctly
- [ ] Forms submit properly
- [ ] Mobile responsive design
- [ ] Cross-browser compatibility
- [ ] Accessibility with screen reader
- [ ] Performance (< 3s load time)
- [ ] SEO meta tags present

### Automated Testing

```bash
# Lighthouse audit
npx lighthouse http://localhost:8000 --output html

# HTML validation
npx html-validate index.html

# CSS validation
npx stylelint styles/main.css

# Accessibility testing
npx axe-cli http://localhost:8000
```

## 🔒 Security

- **Content Security Policy**: Implement CSP headers
- **HTTPS Only**: Ensure all resources use HTTPS
- **No Inline Scripts**: All JavaScript is in external files
- **Sanitized Inputs**: All form inputs are validated

## 📈 SEO Optimization

- **Meta Tags**: Title, description, keywords
- **Open Graph**: Social media sharing
- **Structured Data**: JSON-LD for rich snippets
- **Sitemap**: XML sitemap for search engines
- **Robots.txt**: Search engine crawling instructions

## 🌍 Internationalization

The website is ready for internationalization:

- **Language Attributes**: HTML lang attribute
- **Text Direction**: RTL support ready
- **Font Support**: Unicode font stack
- **Content Structure**: Semantic HTML for translation

## 📞 Support

For questions about the website:

- **Technical Issues**: Create an issue in the repository
- **Design Questions**: Contact the design team
- **Content Updates**: Contact the marketing team

## 📄 License

This website is proprietary to MemoCare. All rights reserved.

---

**MemoCare** - AI-Powered Memory Companion for Seniors
© 2024 MemoCare. All rights reserved.
