<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MemoCare for Senior Living Communities</title>
    <meta
      name="description"
      content="Transform resident engagement with MemoCare's AI-powered memory companion designed specifically for senior living communities and memory care facilities."
    />
    <meta
      name="keywords"
      content="senior living technology, memory care software, resident engagement, family connection, assisted living apps"
    />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://memocare.app/facilities" />
    <meta
      property="og:title"
      content="MemoCare for Senior Living Communities"
    />
    <meta
      property="og:description"
      content="Transform resident engagement with AI-powered memory sharing and family connection tools."
    />
    <meta
      property="og:image"
      content="https://memocare.app/images/facilities-og-image.jpg"
    />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://memocare.app/facilities" />
    <meta
      property="twitter:title"
      content="MemoCare for Senior Living Communities"
    />
    <meta
      property="twitter:description"
      content="Transform resident engagement with AI-powered memory sharing and family connection tools."
    />
    <meta
      property="twitter:image"
      content="https://memocare.app/images/facilities-twitter-image.jpg"
    />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Styles -->
    <link rel="stylesheet" href="styles/main.css" />
    <link rel="stylesheet" href="styles/facilities.css" />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="container">
        <div class="nav-brand">
          <img src="images/logo.svg" alt="MemoCare" class="logo" />
          <span class="brand-name">MemoCare</span>
        </div>

        <div class="nav-menu" id="nav-menu">
          <a href="index.html" class="nav-link">Home</a>
          <a href="#features" class="nav-link">Features</a>
          <a href="#pricing" class="nav-link">Pricing</a>
          <a href="#demo" class="nav-link">Demo</a>
          <a href="#contact" class="nav-link">Contact</a>
          <a href="http://localhost:5173" class="btn btn-primary"
            >Request Demo</a
          >
        </div>

        <div class="nav-toggle" id="nav-toggle">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero facilities-hero">
      <div class="container">
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">
              Transform Resident Engagement with
              <span class="gradient-text">Meaningful Connections</span>
            </h1>
            <p class="hero-subtitle">
              MemoCare helps senior living communities create deeper family
              connections, enhance resident wellbeing, and improve satisfaction
              scores through AI-powered memory sharing and conversation.
            </p>
            <div class="hero-buttons">
              <a href="#demo" class="btn btn-primary btn-large">
                Schedule Demo
                <svg
                  class="btn-icon"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                  ></path>
                </svg>
              </a>
              <a href="#pricing" class="btn btn-outline btn-large">
                View Pricing
                <svg
                  class="btn-icon"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                  ></path>
                </svg>
              </a>
            </div>
            <div class="hero-stats">
              <div class="stat">
                <div class="stat-number">94%</div>
                <div class="stat-label">Family Satisfaction</div>
              </div>
              <div class="stat">
                <div class="stat-number">67%</div>
                <div class="stat-label">Increased Engagement</div>
              </div>
              <div class="stat">
                <div class="stat-number">45%</div>
                <div class="stat-label">More Family Visits</div>
              </div>
            </div>
          </div>
          <div class="hero-image">
            <img
              src="images/facilities-hero-dashboard.png"
              alt="MemoCare Facility Dashboard"
              class="app-screenshot"
            />
            <div class="floating-card card-1">
              <div class="card-icon">👥</div>
              <div class="card-text">15 families connected today</div>
            </div>
            <div class="floating-card card-2">
              <div class="card-icon">📊</div>
              <div class="card-text">Engagement up 67%</div>
            </div>
            <div class="floating-card card-3">
              <div class="card-icon">❤️</div>
              <div class="card-text">Resident satisfaction: 4.8/5</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Benefits Section -->
    <section class="benefits-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">
            Why Senior Living Communities Choose MemoCare
          </h2>
          <p class="section-subtitle">
            Proven results that matter to residents, families, and your bottom
            line
          </p>
        </div>

        <div class="benefits-grid">
          <div class="benefit-card">
            <div class="benefit-icon">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                ></path>
              </svg>
            </div>
            <h3 class="benefit-title">Increase Family Satisfaction</h3>
            <p class="benefit-description">
              94% of families report feeling more connected to their loved ones,
              leading to higher satisfaction scores and positive reviews.
            </p>
            <div class="benefit-metric">+94% satisfaction</div>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                ></path>
              </svg>
            </div>
            <h3 class="benefit-title">Boost Resident Engagement</h3>
            <p class="benefit-description">
              Residents spend 67% more time in meaningful activities and
              conversations, improving cognitive stimulation and overall
              wellbeing.
            </p>
            <div class="benefit-metric">+67% engagement</div>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
            </div>
            <h3 class="benefit-title">Reduce Staff Workload</h3>
            <p class="benefit-description">
              Automated family updates and engagement tracking reduce
              administrative burden, letting staff focus on direct resident
              care.
            </p>
            <div class="benefit-metric">-30% admin time</div>
          </div>

          <div class="benefit-card">
            <div class="benefit-icon">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                ></path>
              </svg>
            </div>
            <h3 class="benefit-title">Improve Retention Rates</h3>
            <p class="benefit-description">
              Families are 45% more likely to visit and stay engaged, reducing
              turnover and improving long-term resident retention.
            </p>
            <div class="benefit-metric">+45% retention</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Enterprise Features for Senior Living</h2>
          <p class="section-subtitle">
            Comprehensive tools designed specifically for senior living
            communities
          </p>
        </div>

        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                ></path>
              </svg>
            </div>
            <h3 class="feature-title">Facility-Wide Dashboard</h3>
            <p class="feature-description">
              Monitor resident engagement, family connections, and satisfaction
              metrics across your entire community with real-time analytics.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                ></path>
              </svg>
            </div>
            <h3 class="feature-title">Staff Management Tools</h3>
            <p class="feature-description">
              Empower your care team with resident insights, family
              communication tools, and engagement tracking to enhance
              personalized care.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                ></path>
              </svg>
            </div>
            <h3 class="feature-title">HIPAA Compliance</h3>
            <p class="feature-description">
              Enterprise-grade security with full HIPAA compliance, audit logs,
              and data protection measures that meet healthcare standards.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M8 9l3 3-3 3m13 0h-6m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20h6m-6-4h6m4 0l4-4-4-4m5 4h2a1 1 0 001-1V9a1 1 0 00-1-1h-2m-6 8h6m-6-4h6"
                ></path>
              </svg>
            </div>
            <h3 class="feature-title">System Integration</h3>
            <p class="feature-description">
              Seamlessly integrate with your existing EHR, resident management
              systems, and communication platforms through our API.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z"
                ></path>
              </svg>
            </div>
            <h3 class="feature-title">24/7 Support</h3>
            <p class="feature-description">
              Dedicated support team with healthcare expertise, training
              programs, and ongoing assistance to ensure successful
              implementation.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                ></path>
              </svg>
            </div>
            <h3 class="feature-title">Custom Reporting</h3>
            <p class="feature-description">
              Generate detailed reports on resident engagement, family
              satisfaction, and outcomes to support quality improvement
              initiatives.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Calculator Section -->
    <section id="pricing" class="pricing-calculator-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Smart Pricing Calculator</h2>
          <p class="section-subtitle">
            Get an instant quote based on your community size and needs
          </p>
        </div>

        <div class="calculator-container">
          <div class="calculator-inputs">
            <div class="input-group">
              <label for="residents">Number of Residents</label>
              <input
                type="range"
                id="residents"
                min="10"
                max="500"
                value="50"
                class="slider"
              />
              <div class="slider-value">
                <span id="residents-value">50</span> residents
              </div>
            </div>

            <div class="input-group">
              <label for="engagement">Target Engagement Level</label>
              <select id="engagement" class="select-input">
                <option value="basic">Basic (20% of residents)</option>
                <option value="standard" selected>
                  Standard (50% of residents)
                </option>
                <option value="premium">Premium (80% of residents)</option>
              </select>
            </div>

            <div class="input-group">
              <label for="features">Additional Features</label>
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input type="checkbox" id="integration" value="integration" />
                  <span class="checkmark"></span>
                  EHR Integration (+$200/month)
                </label>
                <label class="checkbox-label">
                  <input type="checkbox" id="training" value="training" />
                  <span class="checkmark"></span>
                  Staff Training Program (+$500 one-time)
                </label>
                <label class="checkbox-label">
                  <input type="checkbox" id="support" value="support" />
                  <span class="checkmark"></span>
                  Dedicated Support (+$300/month)
                </label>
              </div>
            </div>
          </div>

          <div class="calculator-results">
            <div class="pricing-card calculator-card">
              <h3 class="pricing-title">Your Custom Quote</h3>
              <div class="pricing-breakdown">
                <div class="breakdown-item">
                  <span class="breakdown-label">Base Platform</span>
                  <span class="breakdown-value" id="base-price">$400</span>
                </div>
                <div class="breakdown-item">
                  <span class="breakdown-label">Per Resident</span>
                  <span class="breakdown-value" id="per-resident-price"
                    >$250</span
                  >
                </div>
                <div
                  class="breakdown-item"
                  id="integration-cost"
                  style="display: none"
                >
                  <span class="breakdown-label">EHR Integration</span>
                  <span class="breakdown-value">$200</span>
                </div>
                <div
                  class="breakdown-item"
                  id="support-cost"
                  style="display: none"
                >
                  <span class="breakdown-label">Dedicated Support</span>
                  <span class="breakdown-value">$300</span>
                </div>
                <div class="breakdown-divider"></div>
                <div class="breakdown-total">
                  <span class="breakdown-label">Monthly Total</span>
                  <span class="breakdown-value total-price" id="total-price"
                    >$650</span
                  >
                </div>
                <div
                  class="breakdown-item"
                  id="training-cost"
                  style="display: none"
                >
                  <span class="breakdown-label">One-time Setup</span>
                  <span class="breakdown-value">$500</span>
                </div>
              </div>

              <div class="pricing-features">
                <h4>Included in Your Plan:</h4>
                <ul class="feature-list">
                  <li>✓ Unlimited family accounts</li>
                  <li>✓ AI-powered conversations</li>
                  <li>✓ Photo sharing & organization</li>
                  <li>✓ Facility dashboard & analytics</li>
                  <li>✓ Staff management tools</li>
                  <li>✓ HIPAA compliance & security</li>
                  <li>✓ Mobile apps for families</li>
                  <li>✓ Basic support & training</li>
                </ul>
              </div>

              <div class="pricing-actions">
                <button
                  class="btn btn-primary btn-large"
                  onclick="requestQuote()"
                >
                  Request Official Quote
                  <svg
                    class="btn-icon"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    ></path>
                  </svg>
                </button>
                <button
                  class="btn btn-outline btn-large"
                  onclick="scheduleDemo()"
                >
                  Schedule Demo
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="pricing-note">
          <p>
            <strong>💡 Volume Discounts Available:</strong> Communities with
            100+ residents receive additional discounts. Multi-location
            operators get special enterprise pricing.
          </p>
        </div>
      </div>
    </section>

    <!-- Demo Section -->
    <section id="demo" class="demo-section facilities-demo">
      <div class="container">
        <div class="demo-content">
          <div class="demo-header">
            <h2 class="demo-title">Experience MemoCare for Facilities</h2>
            <p class="demo-subtitle">
              See how MemoCare transforms resident engagement and family
              satisfaction with our interactive facility demo accounts.
            </p>
          </div>

          <div class="demo-cards">
            <div class="demo-card">
              <div class="demo-card-header">
                <div class="demo-icon facility-admin-icon">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                    ></path>
                  </svg>
                </div>
                <h3 class="demo-card-title">Facility Administrator</h3>
                <p class="demo-card-description">
                  Experience the comprehensive facility dashboard with resident
                  analytics, family engagement metrics, and staff management
                  tools.
                </p>
              </div>
              <div class="demo-features">
                <ul>
                  <li>📊 Facility-wide analytics dashboard</li>
                  <li>👥 Resident engagement tracking</li>
                  <li>📈 Family satisfaction metrics</li>
                  <li>🛠️ Staff management tools</li>
                  <li>📋 Custom reporting features</li>
                </ul>
              </div>
              <div class="demo-card-footer">
                <a
                  href="http://localhost:5173?demo=facility-admin"
                  class="btn btn-primary btn-large demo-btn"
                >
                  Try Admin Demo
                  <svg
                    class="btn-icon"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 7l5 5m0 0l-5 5m5-5H6"
                    ></path>
                  </svg>
                </a>
                <p class="demo-credentials">
                  <small>Demo login: <EMAIL> / demodemo</small>
                </p>
              </div>
            </div>

            <div class="demo-card">
              <div class="demo-card-header">
                <div class="demo-icon care-staff-icon">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    ></path>
                  </svg>
                </div>
                <h3 class="demo-card-title">Care Staff Member</h3>
                <p class="demo-card-description">
                  See how care staff use MemoCare to enhance resident
                  interactions, coordinate with families, and track engagement
                  activities.
                </p>
              </div>
              <div class="demo-features">
                <ul>
                  <li>👤 Individual resident profiles</li>
                  <li>💬 Family communication tools</li>
                  <li>📝 Activity tracking & notes</li>
                  <li>🔔 Engagement alerts & reminders</li>
                  <li>📊 Resident progress insights</li>
                </ul>
              </div>
              <div class="demo-card-footer">
                <a
                  href="http://localhost:5173?demo=care-staff"
                  class="btn btn-outline btn-large demo-btn"
                >
                  Try Staff Demo
                  <svg
                    class="btn-icon"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 7l5 5m0 0l-5 5m5-5H6"
                    ></path>
                  </svg>
                </a>
                <p class="demo-credentials">
                  <small>Demo login: <EMAIL> / demodemo</small>
                </p>
              </div>
            </div>
          </div>

          <div class="demo-note">
            <p>
              <strong>🏢 Want a Custom Demo?</strong>
              Schedule a personalized demonstration with your facility's
              specific needs and resident count. Our team will show you exactly
              how MemoCare can transform your community.
            </p>
            <button class="btn btn-primary" onclick="scheduleDemo()">
              Schedule Custom Demo
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
      <div class="container">
        <div class="contact-content">
          <div class="contact-info">
            <h2 class="contact-title">Ready to Transform Your Community?</h2>
            <p class="contact-subtitle">
              Join leading senior living communities that are already using
              MemoCare to enhance resident wellbeing and family satisfaction.
            </p>

            <div class="contact-methods">
              <div class="contact-method">
                <div class="contact-icon">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    ></path>
                  </svg>
                </div>
                <div class="contact-details">
                  <h4>Phone</h4>
                  <p>1-800-MEMOCARE<br />(**************)</p>
                </div>
              </div>

              <div class="contact-method">
                <div class="contact-icon">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    ></path>
                  </svg>
                </div>
                <div class="contact-details">
                  <h4>Email</h4>
                  <p><EMAIL><br /><EMAIL></p>
                </div>
              </div>

              <div class="contact-method">
                <div class="contact-icon">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                    ></path>
                  </svg>
                </div>
                <div class="contact-details">
                  <h4>Schedule</h4>
                  <p>Book a personalized demo<br />at your convenience</p>
                </div>
              </div>
            </div>
          </div>

          <div class="contact-form">
            <form class="demo-request-form" onsubmit="submitDemoRequest(event)">
              <h3>Request Information</h3>

              <div class="form-group">
                <label for="facility-name">Facility Name *</label>
                <input
                  type="text"
                  id="facility-name"
                  name="facilityName"
                  required
                />
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="contact-name">Your Name *</label>
                  <input
                    type="text"
                    id="contact-name"
                    name="contactName"
                    required
                  />
                </div>
                <div class="form-group">
                  <label for="contact-title">Title</label>
                  <input
                    type="text"
                    id="contact-title"
                    name="contactTitle"
                    placeholder="e.g., Administrator, Director"
                  />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="contact-email">Email *</label>
                  <input
                    type="email"
                    id="contact-email"
                    name="contactEmail"
                    required
                  />
                </div>
                <div class="form-group">
                  <label for="contact-phone">Phone</label>
                  <input type="tel" id="contact-phone" name="contactPhone" />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="resident-count">Number of Residents</label>
                  <select id="resident-count" name="residentCount">
                    <option value="">Select range</option>
                    <option value="10-25">10-25 residents</option>
                    <option value="26-50">26-50 residents</option>
                    <option value="51-100">51-100 residents</option>
                    <option value="101-200">101-200 residents</option>
                    <option value="200+">200+ residents</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="facility-type">Facility Type</label>
                  <select id="facility-type" name="facilityType">
                    <option value="">Select type</option>
                    <option value="assisted-living">Assisted Living</option>
                    <option value="memory-care">Memory Care</option>
                    <option value="independent-living">
                      Independent Living
                    </option>
                    <option value="skilled-nursing">Skilled Nursing</option>
                    <option value="ccrc">CCRC</option>
                  </select>
                </div>
              </div>

              <div class="form-group">
                <label for="message">Tell us about your needs</label>
                <textarea
                  id="message"
                  name="message"
                  rows="4"
                  placeholder="What challenges are you looking to solve? What features are most important to you?"
                ></textarea>
              </div>

              <div class="form-group">
                <label for="timeline">Implementation Timeline</label>
                <select id="timeline" name="timeline">
                  <option value="">Select timeline</option>
                  <option value="immediate">Immediate (within 30 days)</option>
                  <option value="1-3-months">1-3 months</option>
                  <option value="3-6-months">3-6 months</option>
                  <option value="6-12-months">6-12 months</option>
                  <option value="exploring">Just exploring options</option>
                </select>
              </div>

              <button type="submit" class="btn btn-primary btn-large">
                Send Request
                <svg
                  class="btn-icon"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                  ></path>
                </svg>
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- Scripts -->
    <script src="scripts/main.js"></script>
    <script src="scripts/facilities.js"></script>
  </body>
</html>
