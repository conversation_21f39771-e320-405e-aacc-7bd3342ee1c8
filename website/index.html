<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MemoCare - Your Personal Memory Companion</title>
    <meta
      name="description"
      content="MemoCare helps you preserve and share precious memories through photos and conversations. Stay connected with family while celebrating life's beautiful moments."
    />
    <meta
      name="keywords"
      content="memory companion, photo sharing, family connection, life stories, digital memories, senior technology"
    />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://memocare.app/" />
    <meta
      property="og:title"
      content="MemoCare - Your Personal Memory Companion"
    />
    <meta
      property="og:description"
      content="Share photos, tell stories, and celebrate precious memories with family through meaningful conversations."
    />
    <meta
      property="og:image"
      content="https://memocare.app/images/og-image.jpg"
    />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://memocare.app/" />
    <meta
      property="twitter:title"
      content="MemoCare - Your Personal Memory Companion"
    />
    <meta
      property="twitter:description"
      content="Share photos, tell stories, and celebrate precious memories with family through meaningful conversations."
    />
    <meta
      property="twitter:image"
      content="https://memocare.app/images/twitter-image.jpg"
    />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Styles -->
    <link rel="stylesheet" href="styles/main.css" />

    <!-- Analytics -->
    <!-- Google Analytics will be added here -->
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="container">
        <div class="nav-brand">
          <img src="images/logo.svg" alt="MemoCare" class="logo" />
          <span class="brand-name">MemoCare</span>
        </div>

        <div class="nav-menu" id="nav-menu">
          <a href="#demo" class="nav-link">Try Demo</a>
          <a href="#features" class="nav-link">Features</a>
          <a href="#how-it-works" class="nav-link">How It Works</a>
          <a href="#pricing" class="nav-link">Pricing</a>
          <a href="#contact" class="nav-link">Contact</a>
          <a href="../index.html" class="btn btn-primary">Start Free</a>
        </div>

        <div class="nav-toggle" id="nav-toggle">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
      <div class="container">
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">
              Celebrate Life's
              <span class="gradient-text">Beautiful Moments</span>
            </h1>
            <p class="hero-subtitle">
              Share photos, tell stories, and stay connected with family through
              meaningful conversations. Your personal companion for preserving
              and celebrating precious memories.
            </p>
            <div class="hero-buttons">
              <a href="../index.html" class="btn btn-primary btn-large">
                Start Free Trial
                <svg
                  class="btn-icon"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 7l5 5m0 0l-5 5m5-5H6"
                  ></path>
                </svg>
              </a>
              <a href="#demo" class="btn btn-outline btn-large">
                Watch Demo
                <svg
                  class="btn-icon"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
              </a>
            </div>
            <div class="hero-stats">
              <div class="stat">
                <div class="stat-number">50M+</div>
                <div class="stat-label">Memories Preserved</div>
              </div>
              <div class="stat">
                <div class="stat-number">10,000+</div>
                <div class="stat-label">Families Connected</div>
              </div>
              <div class="stat">
                <div class="stat-number">4.9★</div>
                <div class="stat-label">User Rating</div>
              </div>
            </div>
          </div>
          <div class="hero-image">
            <img
              src="images/hero-app-screenshot.png"
              alt="MemoCare App Interface"
              class="app-screenshot"
            />
            <div class="floating-card card-1">
              <div class="card-icon">💊</div>
              <div class="card-text">Medication reminder sent</div>
            </div>
            <div class="floating-card card-2">
              <div class="card-icon">📸</div>
              <div class="card-text">New photo shared by Sarah</div>
            </div>
            <div class="floating-card card-3">
              <div class="card-icon">🤖</div>
              <div class="card-text">"Tell me about this photo..."</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Demo Section -->
    <section id="demo" class="demo-section">
      <div class="container">
        <div class="demo-content">
          <div class="demo-header">
            <h2 class="demo-title">Try MemoCare Right Now</h2>
            <p class="demo-subtitle">
              Experience the magic of memory sharing with our interactive demo
              accounts. No signup required - just click and explore!
            </p>
          </div>

          <div class="demo-cards">
            <div class="demo-card">
              <div class="demo-card-header">
                <div class="demo-icon patient-icon">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    ></path>
                  </svg>
                </div>
                <h3 class="demo-card-title">Experience as a User</h3>
                <p class="demo-card-description">
                  Share photos, have conversations, and explore memories in a
                  warm, welcoming environment designed for you.
                </p>
              </div>
              <div class="demo-features">
                <ul>
                  <li>✨ Share and explore photos</li>
                  <li>💬 Have meaningful conversations</li>
                  <li>🔔 Gentle daily reminders</li>
                  <li>❤️ Connect with family</li>
                </ul>
              </div>
              <div class="demo-card-footer">
                <a
                  href="../index.html?demo=patient"
                  class="btn btn-primary btn-large demo-btn"
                >
                  Try as User
                  <svg
                    class="btn-icon"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 7l5 5m0 0l-5 5m5-5H6"
                    ></path>
                  </svg>
                </a>
                <p class="demo-credentials">
                  <small>Demo login: <EMAIL> / demodemo</small>
                </p>
              </div>
            </div>

            <div class="demo-card">
              <div class="demo-card-header">
                <div class="demo-icon caregiver-icon">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    ></path>
                  </svg>
                </div>
                <h3 class="demo-card-title">Experience as Family</h3>
                <p class="demo-card-description">
                  See how family members stay connected, share insights, and
                  support their loved ones through the family dashboard.
                </p>
              </div>
              <div class="demo-features">
                <ul>
                  <li>📊 Family connection insights</li>
                  <li>📸 Shared photo albums</li>
                  <li>⚙️ Gentle reminder settings</li>
                  <li>🤝 Support coordination</li>
                </ul>
              </div>
              <div class="demo-card-footer">
                <a
                  href="../index.html?demo=caregiver"
                  class="btn btn-outline btn-large demo-btn"
                >
                  Try as Family
                  <svg
                    class="btn-icon"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 7l5 5m0 0l-5 5m5-5H6"
                    ></path>
                  </svg>
                </a>
                <p class="demo-credentials">
                  <small>Demo login: <EMAIL> / demodemo</small>
                </p>
              </div>
            </div>
          </div>

          <div class="demo-note">
            <p>
              <strong>💡 Pro tip:</strong> Try both experiences to see how
              MemoCare brings families together. All demo data is safe and
              resets automatically.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Trust Indicators -->
    <section class="trust-section">
      <div class="container">
        <p class="trust-text">
          Trusted by families and professionals worldwide
        </p>
        <div class="trust-logos">
          <img
            src="images/privacy-secure.svg"
            alt="Privacy Secure"
            class="trust-logo"
          />
          <img
            src="images/data-protected.svg"
            alt="Data Protected"
            class="trust-logo"
          />
          <img
            src="images/family-approved.svg"
            alt="Family Approved"
            class="trust-logo"
          />
          <img
            src="images/accessible-design.svg"
            alt="Accessible Design"
            class="trust-logo"
          />
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Designed for Life's Beautiful Moments</h2>
          <p class="section-subtitle">
            Thoughtful features that help you share stories, stay connected, and
            celebrate precious memories together
          </p>
        </div>

        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                ></path>
              </svg>
            </div>
            <h3 class="feature-title">Your Personal Story Companion</h3>
            <p class="feature-description">
              A thoughtful companion that helps you explore photos and share
              meaningful stories through natural, engaging conversations.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                ></path>
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                ></path>
              </svg>
            </div>
            <h3 class="feature-title">Effortless Memory Sharing</h3>
            <p class="feature-description">
              Share photos instantly with smart organization and thoughtful
              conversation prompts that bring memories to life.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"
                ></path>
              </svg>
            </div>
            <h3 class="feature-title">Gentle Daily Reminders</h3>
            <p class="feature-description">
              Thoughtful reminders for daily routines with visual cues and
              family notifications to support your wellness journey.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                ></path>
              </svg>
            </div>
            <h3 class="feature-title">Stay Close to Family</h3>
            <p class="feature-description">
              Bring families together with shared albums, meaningful insights,
              and beautiful ways to stay connected across any distance.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                ></path>
              </svg>
            </div>
            <h3 class="feature-title">Privacy & Security First</h3>
            <p class="feature-description">
              Your memories are protected with enterprise-grade security,
              encryption, and complete privacy controls you can trust.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h8a2 2 0 002-2V6a2 2 0 00-2-2M9 12h6m-6 4h6"
                ></path>
              </svg>
            </div>
            <h3 class="feature-title">Designed for Everyone</h3>
            <p class="feature-description">
              Beautiful, intuitive design with large text, clear navigation, and
              thoughtful features that make technology feel natural.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="how-it-works-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Start Your Memory Journey</h2>
          <p class="section-subtitle">
            Three simple steps to begin sharing and celebrating life's beautiful
            moments
          </p>
        </div>

        <div class="steps-container">
          <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h3 class="step-title">Share Your Photos</h3>
              <p class="step-description">
                Simply add photos from your phone or computer. They're
                automatically organized and ready for meaningful conversations.
              </p>
            </div>
            <div class="step-image">
              <img src="images/step-1-upload.png" alt="Upload Photos" />
            </div>
          </div>

          <div class="step step-reverse">
            <div class="step-number">2</div>
            <div class="step-content">
              <h3 class="step-title">Tell Your Stories</h3>
              <p class="step-description">
                Your companion asks thoughtful questions about photos, helping
                you share wonderful stories and relive precious moments.
              </p>
            </div>
            <div class="step-image">
              <img src="images/step-2-chat.png" alt="AI Conversations" />
            </div>
          </div>

          <div class="step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h3 class="step-title">Connect with Family</h3>
              <p class="step-description">
                Family members stay close with shared memories and gentle
                reminders that support your daily wellness routine.
              </p>
            </div>
            <div class="step-image">
              <img src="images/step-3-family.png" alt="Family Connection" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Stories from Our Community</h2>
          <p class="section-subtitle">
            Hear how families are celebrating memories together
          </p>
        </div>

        <div class="testimonials-grid">
          <div class="testimonial-card">
            <div class="testimonial-content">
              <p class="testimonial-text">
                "MemoCare has brought such joy to our family. Mom lights up when
                sharing stories about her photos. It's like having a wonderful
                friend who's always ready to listen and remember."
              </p>
            </div>
            <div class="testimonial-author">
              <img
                src="images/testimonial-1.jpg"
                alt="Sarah Johnson"
                class="author-avatar"
              />
              <div class="author-info">
                <div class="author-name">Sarah Johnson</div>
                <div class="author-role">Daughter & Family Coordinator</div>
              </div>
            </div>
          </div>

          <div class="testimonial-card">
            <div class="testimonial-content">
              <p class="testimonial-text">
                "I recommend MemoCare to families who want to stay connected.
                The gentle reminders and family insights help everyone feel more
                involved and supportive."
              </p>
            </div>
            <div class="testimonial-author">
              <img
                src="images/testimonial-2.jpg"
                alt="Dr. Michael Chen"
                class="author-avatar"
              />
              <div class="author-info">
                <div class="author-name">Dr. Michael Chen</div>
                <div class="author-role">Family Wellness Specialist</div>
              </div>
            </div>
          </div>

          <div class="testimonial-card">
            <div class="testimonial-content">
              <p class="testimonial-text">
                "The app feels so natural to use. I love seeing the photos my
                grandchildren share and having wonderful conversations about all
                our beautiful memories together."
              </p>
            </div>
            <div class="testimonial-author">
              <img
                src="images/testimonial-3.jpg"
                alt="Eleanor Martinez"
                class="author-avatar"
              />
              <div class="author-info">
                <div class="author-name">Eleanor Martinez</div>
                <div class="author-role">MemoCare User, Age 78</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">Plans That Grow With Your Family</h2>
          <p class="section-subtitle">
            Flexible options designed to bring families closer together
          </p>
        </div>

        <div class="pricing-grid">
          <div class="pricing-card">
            <div class="pricing-header">
              <h3 class="pricing-title">Family</h3>
              <p class="pricing-description">Perfect for close-knit families</p>
              <div class="pricing-price">
                <span class="price-amount">$29</span>
                <span class="price-period">/month</span>
              </div>
            </div>
            <div class="pricing-features">
              <ul class="feature-list">
                <li>Up to 5 family members</li>
                <li>Unlimited photo storage</li>
                <li>Personal story companion</li>
                <li>Gentle daily reminders</li>
                <li>Family connection insights</li>
                <li>Email support</li>
              </ul>
            </div>
            <div class="pricing-footer">
              <a
                href="../index.html?plan=family"
                class="btn btn-outline btn-large"
                >Start Free Trial</a
              >
            </div>
          </div>

          <div class="pricing-card pricing-featured">
            <div class="pricing-badge">Most Popular</div>
            <div class="pricing-header">
              <h3 class="pricing-title">Care Plus</h3>
              <p class="pricing-description">
                Enhanced features for extended families
              </p>
              <div class="pricing-price">
                <span class="price-amount">$59</span>
                <span class="price-period">/month</span>
              </div>
            </div>
            <div class="pricing-features">
              <ul class="feature-list">
                <li>Up to 15 family members</li>
                <li>Unlimited photo storage</li>
                <li>Enhanced story conversations</li>
                <li>Smart wellness reminders</li>
                <li>Detailed family insights</li>
                <li>Important notifications</li>
                <li>Priority support</li>
                <li>Professional integration</li>
              </ul>
            </div>
            <div class="pricing-footer">
              <a
                href="../index.html?plan=care-plus"
                class="btn btn-primary btn-large"
                >Start Free Trial</a
              >
            </div>
          </div>

          <div class="pricing-card">
            <div class="pricing-header">
              <h3 class="pricing-title">Facility</h3>
              <p class="pricing-description">For senior living communities</p>
              <div class="pricing-price">
                <span class="price-amount">Custom</span>
                <span class="price-period">pricing</span>
              </div>
            </div>
            <div class="pricing-features">
              <ul class="feature-list">
                <li>Unlimited residents</li>
                <li>Staff management tools</li>
                <li>Facility-wide insights</li>
                <li>Custom integrations</li>
                <li>Dedicated support</li>
                <li>Training & onboarding</li>
                <li>HIPAA compliance tools</li>
                <li>Custom reporting</li>
              </ul>
            </div>
            <div class="pricing-footer">
              <a href="#contact" class="btn btn-outline btn-large"
                >Contact Sales</a
              >
            </div>
          </div>
        </div>

        <div class="pricing-note">
          <p>
            All plans include a 30-day free trial. No credit card required.
            Cancel anytime.
          </p>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">
          <h2 class="cta-title">Ready to Celebrate Life's Moments?</h2>
          <p class="cta-subtitle">
            Join thousands of families who use MemoCare to share stories, create
            connections, and celebrate precious memories together.
          </p>
          <div class="cta-buttons">
            <a href="../index.html" class="btn btn-primary btn-large">
              Start Free Trial
              <svg
                class="btn-icon"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 7l5 5m0 0l-5 5m5-5H6"
                ></path>
              </svg>
            </a>
            <a href="#contact" class="btn btn-outline btn-large">
              Contact Sales
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <div class="footer-brand">
              <img src="images/logo.svg" alt="MemoCare" class="footer-logo" />
              <span class="footer-brand-name">MemoCare</span>
            </div>
            <p class="footer-description">
              Your personal companion for sharing photos, telling stories, and
              celebrating life's precious moments with family and friends.
            </p>
            <div class="footer-social">
              <a href="#" class="social-link" aria-label="Facebook">
                <svg fill="currentColor" viewBox="0 0 24 24">
                  <path
                    d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
                  />
                </svg>
              </a>
              <a href="#" class="social-link" aria-label="Twitter">
                <svg fill="currentColor" viewBox="0 0 24 24">
                  <path
                    d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"
                  />
                </svg>
              </a>
              <a href="#" class="social-link" aria-label="LinkedIn">
                <svg fill="currentColor" viewBox="0 0 24 24">
                  <path
                    d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"
                  />
                </svg>
              </a>
            </div>
          </div>

          <div class="footer-section">
            <h4 class="footer-title">Product</h4>
            <ul class="footer-links">
              <li><a href="#features">Features</a></li>
              <li><a href="#pricing">Pricing</a></li>
              <li><a href="../index.html">Try Free</a></li>
              <li><a href="#demo">Demo</a></li>
            </ul>
          </div>

          <div class="footer-section">
            <h4 class="footer-title">Company</h4>
            <ul class="footer-links">
              <li><a href="#about">About</a></li>
              <li><a href="/blog">Blog</a></li>
              <li><a href="/careers">Careers</a></li>
              <li><a href="#contact">Contact</a></li>
            </ul>
          </div>

          <div class="footer-section">
            <h4 class="footer-title">Support</h4>
            <ul class="footer-links">
              <li><a href="/help">Help Center</a></li>
              <li><a href="/privacy">Privacy Policy</a></li>
              <li><a href="/terms">Terms of Service</a></li>
              <li><a href="/security">Security</a></li>
            </ul>
          </div>
        </div>

        <div class="footer-bottom">
          <p class="footer-copyright">
            © 2024 MemoCare. All rights reserved. Secure, private, and designed
            with love for families.
          </p>
        </div>
      </div>
    </footer>

    <!-- Scripts -->
    <script src="scripts/main.js"></script>
  </body>
</html>
