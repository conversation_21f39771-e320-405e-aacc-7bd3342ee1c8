// Facilities Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initPricingCalculator();
    initContactForm();
});

// Pricing Calculator Logic
function initPricingCalculator() {
    const residentsSlider = document.getElementById('residents');
    const residentsValue = document.getElementById('residents-value');
    const engagementSelect = document.getElementById('engagement');
    const integrationCheckbox = document.getElementById('integration');
    const trainingCheckbox = document.getElementById('training');
    const supportCheckbox = document.getElementById('support');
    
    const basePriceElement = document.getElementById('base-price');
    const perResidentPriceElement = document.getElementById('per-resident-price');
    const totalPriceElement = document.getElementById('total-price');
    const integrationCostElement = document.getElementById('integration-cost');
    const supportCostElement = document.getElementById('support-cost');
    const trainingCostElement = document.getElementById('training-cost');

    // Pricing structure
    const pricing = {
        basePrice: 400,
        perResident: {
            basic: 3,    // $3 per resident for 20% engagement
            standard: 5, // $5 per resident for 50% engagement  
            premium: 8   // $8 per resident for 80% engagement
        },
        addOns: {
            integration: 200,
            support: 300,
            training: 500
        }
    };

    function updatePricing() {
        const residents = parseInt(residentsSlider.value);
        const engagement = engagementSelect.value;
        const hasIntegration = integrationCheckbox.checked;
        const hasSupport = supportCheckbox.checked;
        const hasTraining = trainingCheckbox.checked;

        // Update residents display
        residentsValue.textContent = residents;

        // Calculate base pricing
        const basePrice = pricing.basePrice;
        const perResidentRate = pricing.perResident[engagement];
        const perResidentTotal = residents * perResidentRate;

        // Calculate add-ons
        let monthlyAddOns = 0;
        if (hasIntegration) {
            monthlyAddOns += pricing.addOns.integration;
            integrationCostElement.style.display = 'flex';
        } else {
            integrationCostElement.style.display = 'none';
        }

        if (hasSupport) {
            monthlyAddOns += pricing.addOns.support;
            supportCostElement.style.display = 'flex';
        } else {
            supportCostElement.style.display = 'none';
        }

        if (hasTraining) {
            trainingCostElement.style.display = 'flex';
        } else {
            trainingCostElement.style.display = 'none';
        }

        // Calculate total
        const monthlyTotal = basePrice + perResidentTotal + monthlyAddOns;

        // Apply volume discounts
        let discount = 0;
        if (residents >= 200) {
            discount = 0.15; // 15% discount for 200+ residents
        } else if (residents >= 100) {
            discount = 0.10; // 10% discount for 100+ residents
        } else if (residents >= 50) {
            discount = 0.05; // 5% discount for 50+ residents
        }

        const discountedTotal = Math.round(monthlyTotal * (1 - discount));

        // Update display
        basePriceElement.textContent = `$${basePrice}`;
        perResidentPriceElement.textContent = `$${perResidentTotal}`;
        totalPriceElement.textContent = `$${discountedTotal.toLocaleString()}`;

        // Store pricing data for quote requests
        window.currentQuote = {
            residents,
            engagement,
            basePrice,
            perResidentTotal,
            monthlyAddOns,
            discount,
            monthlyTotal: discountedTotal,
            oneTimeSetup: hasTraining ? pricing.addOns.training : 0,
            features: {
                integration: hasIntegration,
                support: hasSupport,
                training: hasTraining
            }
        };
    }

    // Event listeners
    residentsSlider.addEventListener('input', updatePricing);
    engagementSelect.addEventListener('change', updatePricing);
    integrationCheckbox.addEventListener('change', updatePricing);
    trainingCheckbox.addEventListener('change', updatePricing);
    supportCheckbox.addEventListener('change', updatePricing);

    // Initial calculation
    updatePricing();
}

// Quote Request Function
function requestQuote() {
    const quote = window.currentQuote;
    
    if (!quote) {
        alert('Please configure your pricing options first.');
        return;
    }

    // Track quote request
    if (typeof gtag !== 'undefined') {
        gtag('event', 'quote_request', {
            'residents': quote.residents,
            'engagement_level': quote.engagement,
            'monthly_total': quote.monthlyTotal,
            'page_location': window.location.href
        });
    }

    // Create quote summary
    const quoteDetails = `
Facility Pricing Quote Request

Residents: ${quote.residents}
Engagement Level: ${quote.engagement}
Monthly Total: $${quote.monthlyTotal.toLocaleString()}
${quote.oneTimeSetup > 0 ? `One-time Setup: $${quote.oneTimeSetup}` : ''}

Features:
- Base Platform: $${quote.basePrice}
- Per Resident: $${quote.perResidentTotal}
${quote.features.integration ? '- EHR Integration: $200/month' : ''}
${quote.features.support ? '- Dedicated Support: $300/month' : ''}
${quote.features.training ? '- Staff Training: $500 one-time' : ''}

Generated on: ${new Date().toLocaleDateString()}
    `.trim();

    // For now, copy to clipboard and show contact form
    if (navigator.clipboard) {
        navigator.clipboard.writeText(quoteDetails).then(() => {
            alert('Quote details copied to clipboard! Please scroll down to the contact form to request your official quote.');
        });
    } else {
        alert('Please scroll down to the contact form to request your official quote.');
    }

    // Scroll to contact form
    document.getElementById('contact').scrollIntoView({ 
        behavior: 'smooth' 
    });

    // Pre-fill resident count in contact form
    const residentCountSelect = document.getElementById('resident-count');
    if (residentCountSelect && quote.residents) {
        const residents = quote.residents;
        if (residents <= 25) {
            residentCountSelect.value = '10-25';
        } else if (residents <= 50) {
            residentCountSelect.value = '26-50';
        } else if (residents <= 100) {
            residentCountSelect.value = '51-100';
        } else if (residents <= 200) {
            residentCountSelect.value = '101-200';
        } else {
            residentCountSelect.value = '200+';
        }
    }
}

// Schedule Demo Function
function scheduleDemo() {
    // Track demo request
    if (typeof gtag !== 'undefined') {
        gtag('event', 'demo_request', {
            'source': 'facilities_page',
            'page_location': window.location.href
        });
    }

    // For now, scroll to contact form
    document.getElementById('contact').scrollIntoView({ 
        behavior: 'smooth' 
    });

    // Pre-fill timeline
    const timelineSelect = document.getElementById('timeline');
    if (timelineSelect) {
        timelineSelect.value = 'immediate';
    }

    // Focus on facility name field
    setTimeout(() => {
        const facilityNameField = document.getElementById('facility-name');
        if (facilityNameField) {
            facilityNameField.focus();
        }
    }, 500);
}

// Contact Form Handling
function initContactForm() {
    const form = document.querySelector('.demo-request-form');
    if (!form) return;

    // Add real-time validation
    const requiredFields = form.querySelectorAll('input[required], select[required]');
    requiredFields.forEach(field => {
        field.addEventListener('blur', validateField);
        field.addEventListener('input', clearFieldError);
    });
}

function validateField(event) {
    const field = event.target;
    const value = field.value.trim();
    
    // Remove existing error styling
    field.classList.remove('error');
    
    // Check if required field is empty
    if (field.hasAttribute('required') && !value) {
        field.classList.add('error');
        return false;
    }
    
    // Email validation
    if (field.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            field.classList.add('error');
            return false;
        }
    }
    
    return true;
}

function clearFieldError(event) {
    event.target.classList.remove('error');
}

function submitDemoRequest(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    
    // Validate all required fields
    const requiredFields = form.querySelectorAll('input[required], select[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!validateField({ target: field })) {
            isValid = false;
        }
    });
    
    if (!isValid) {
        alert('Please fill in all required fields correctly.');
        return;
    }
    
    // Show loading state
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    submitButton.innerHTML = `
        <svg class="animate-spin w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Sending...
    `;
    submitButton.disabled = true;
    
    // Collect form data
    const requestData = {
        facilityName: formData.get('facilityName'),
        contactName: formData.get('contactName'),
        contactTitle: formData.get('contactTitle'),
        contactEmail: formData.get('contactEmail'),
        contactPhone: formData.get('contactPhone'),
        residentCount: formData.get('residentCount'),
        facilityType: formData.get('facilityType'),
        message: formData.get('message'),
        timeline: formData.get('timeline'),
        timestamp: new Date().toISOString(),
        source: 'facilities_website',
        currentQuote: window.currentQuote || null
    };
    
    // Track form submission
    if (typeof gtag !== 'undefined') {
        gtag('event', 'form_submit', {
            'form_type': 'demo_request',
            'facility_type': requestData.facilityType,
            'resident_count': requestData.residentCount,
            'timeline': requestData.timeline
        });
    }
    
    // Simulate form submission (replace with actual endpoint)
    setTimeout(() => {
        // Reset button
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
        
        // Show success message
        alert('Thank you for your interest! We\'ll contact you within 24 hours to schedule your personalized demo.');
        
        // Reset form
        form.reset();
        
        // Log to console for now (replace with actual API call)
        console.log('Demo request submitted:', requestData);
        
    }, 2000);
}

// Add error styling to CSS
const style = document.createElement('style');
style.textContent = `
    .form-group input.error,
    .form-group select.error,
    .form-group textarea.error {
        border-color: var(--error-500);
        background-color: #fef2f2;
    }
    
    .animate-spin {
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
