/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors - Matching App Design */
    --primary-50: #EEF2FF;
    --primary-100: #E0E7FF;
    --primary-200: #C7D2FE;
    --primary-300: #A5B4FC;
    --primary-400: #818CF8;
    --primary-500: #6366F1;
    --primary-600: #4F46E5;
    --primary-700: #4338CA;
    --primary-800: #3730A3;
    --primary-900: #312E81;

    --warm-50: #FFFBF5;
    --warm-100: #FEF7E6;
    --warm-200: #FDECC8;
    --warm-300: #FBD99E;
    --warm-400: #F9C474;
    --warm-500: #F7B955;
    --warm-600: #F59E0B;
    --warm-700: #D97706;
    --warm-800: #B45309;
    --warm-900: #92400E;

    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    --success-500: #22C55E;
    --warning-500: #F59E0B;
    --error-500: #EF4444;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    --spacing-24: 6rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: white;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    color: var(--gray-900);
}

.gradient-text {
    background: linear-gradient(135deg, var(--primary-600), var(--warm-500));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-600);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-600);
    border: 2px solid var(--primary-600);
}

.btn-outline:hover {
    background-color: var(--primary-600);
    color: white;
}

.btn-large {
    padding: var(--spacing-4) var(--spacing-8);
    font-size: var(--font-size-lg);
}

.btn-icon {
    width: 1.25rem;
    height: 1.25rem;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--gray-200);
    z-index: 1000;
    transition: all var(--transition-fast);
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: var(--spacing-4);
    padding-bottom: var(--spacing-4);
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-900);
    text-decoration: none;
}

.logo {
    width: 2rem;
    height: 2rem;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-8);
}

.nav-link {
    color: var(--gray-600);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
}

.nav-link:hover {
    color: var(--primary-600);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: var(--spacing-1);
    cursor: pointer;
}

.nav-toggle span {
    width: 1.5rem;
    height: 2px;
    background-color: var(--gray-600);
    transition: all var(--transition-fast);
}

/* Hero Section */
.hero {
    padding-top: calc(80px + var(--spacing-20));
    padding-bottom: var(--spacing-20);
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--warm-50) 50%, white 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, var(--primary-100) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, var(--warm-100) 0%, transparent 50%);
    opacity: 0.6;
    z-index: 0;
}

.hero .container {
    position: relative;
    z-index: 1;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    margin-bottom: var(--spacing-6);
    line-height: 1.1;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    margin-bottom: var(--spacing-6);
    line-height: 1.6;
}

.hero-offer {
    margin-bottom: var(--spacing-8);
}

.offer-badge {
    display: inline-block;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: var(--spacing-3) var(--spacing-6);
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: var(--font-size-base);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-12);
}

.hero-stats {
    display: flex;
    gap: var(--spacing-8);
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-600);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    margin-top: var(--spacing-1);
}

.hero-image {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.app-screenshot {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
}

.floating-card {
    position: absolute;
    background: white;
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    font-weight: 500;
    animation: float 3s ease-in-out infinite;
}

.card-1 {
    top: 10%;
    right: -10%;
    animation-delay: 0s;
}

.card-2 {
    top: 50%;
    left: -15%;
    animation-delay: 1s;
}

.card-3 {
    bottom: 20%;
    right: -5%;
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Demo Section */
.demo-section {
    padding: var(--spacing-20) 0;
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--warm-50) 100%);
    position: relative;
}

.demo-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, var(--primary-100) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, var(--warm-100) 0%, transparent 50%);
    opacity: 0.7;
    z-index: 0;
}

.demo-content {
    position: relative;
    z-index: 1;
}

.demo-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
}

.demo-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
    background: linear-gradient(135deg, var(--primary-600), var(--warm-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.demo-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.demo-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-12);
}

@media (min-width: 1024px) {
    .demo-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1280px) {
    .demo-cards {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-8);
    }
}

.demo-card {
    background: white;
    border-radius: var(--radius-2xl);
    padding: var(--spacing-8);
    box-shadow: var(--shadow-lg);
    border: 2px solid transparent;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.demo-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500), var(--warm-500));
}

.demo-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
}

.demo-card-header {
    margin-bottom: var(--spacing-6);
}

.demo-icon {
    width: 4rem;
    height: 4rem;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-4);
    position: relative;
}

.patient-icon {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
}

.caregiver-icon {
    background: linear-gradient(135deg, var(--warm-500), var(--warm-600));
    color: white;
}

.facility-admin-icon {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
}

.care-staff-icon {
    background: linear-gradient(135deg, var(--green-500), var(--green-600));
    color: white;
}

.super-admin-icon {
    background: linear-gradient(135deg, var(--gray-700), var(--gray-800));
    color: white;
}

.demo-icon svg {
    width: 2rem;
    height: 2rem;
}

.demo-card-title {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--spacing-3);
    color: var(--gray-900);
}

.demo-card-description {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-6);
}

.demo-features ul {
    list-style: none;
    margin-bottom: var(--spacing-8);
}

.demo-features ul li {
    margin-bottom: var(--spacing-2);
}

.demo-features li {
    padding: var(--spacing-2) 0;
    color: var(--gray-700);
    font-weight: 500;
}

.demo-card-footer {
    text-align: center;
}

.demo-btn {
    width: 100%;
    margin-bottom: var(--spacing-3);
    justify-content: center;
}

.demo-credentials {
    color: var(--gray-500);
    font-size: var(--font-size-sm);
    margin: 0;
}

.demo-note {
    text-align: center;
    background: white;
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--primary-500);
}

.demo-note p {
    color: var(--gray-700);
    margin: 0 0 var(--spacing-4) 0;
    font-size: var(--font-size-lg);
}

.demo-summary {
    display: flex;
    justify-content: center;
    gap: var(--spacing-4);
    flex-wrap: wrap;
    margin-top: var(--spacing-4);
}

@media (min-width: 768px) {
    .demo-summary {
        gap: var(--spacing-6);
    }
}

.demo-summary-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    background: var(--gray-100);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
}

.demo-emoji {
    font-size: var(--font-size-base);
}

/* Super Admin Section */
.super-admin-section {
    margin: var(--spacing-8) 0;
    padding: var(--spacing-6);
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    border: 2px dashed var(--gray-300);
}

.super-admin-card {
    max-width: 600px;
    margin: 0 auto;
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-sm);
}

.super-admin-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-4);
}

.super-admin-content {
    flex: 1;
}

.super-admin-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 var(--spacing-2) 0;
}

.super-admin-description {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

.super-admin-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-4);
}

.super-admin-footer .demo-credentials {
    margin: 0;
    font-size: var(--font-size-xs);
}

/* Trust Section */
.trust-section {
    padding: var(--spacing-12) 0;
    background-color: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    border-bottom: 1px solid var(--gray-200);
}

.trust-text {
    text-align: center;
    color: var(--gray-600);
    margin-bottom: var(--spacing-8);
    font-size: var(--font-size-lg);
}

.trust-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-12);
    flex-wrap: wrap;
}

.trust-logo {
    height: 3rem;
    opacity: 0.7;
    transition: opacity var(--transition-fast);
}

.trust-logo:hover {
    opacity: 1;
}

/* Features Section */
.features-section {
    padding: var(--spacing-20) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
}

.section-title {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-4);
}

.section-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-8);
}

.feature-card {
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    background: white;
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
}

.feature-icon {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-6);
}

.feature-icon svg {
    width: 1.5rem;
    height: 1.5rem;
    color: white;
}

.feature-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-3);
}

.feature-description {
    color: var(--gray-600);
    line-height: 1.7;
}

/* How It Works Section */
.how-it-works-section {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.steps-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-16);
}

.step {
    display: grid;
    grid-template-columns: auto 1fr 1fr;
    gap: var(--spacing-8);
    align-items: center;
}

.step-reverse {
    grid-template-columns: auto 1fr 1fr;
}

.step-reverse .step-content {
    order: 3;
}

.step-reverse .step-image {
    order: 2;
}

.step-number {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    font-weight: 700;
    box-shadow: var(--shadow-lg);
}

.step-title {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-4);
}

.step-description {
    color: var(--gray-600);
    font-size: var(--font-size-lg);
    line-height: 1.7;
}

.step-image img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

/* Testimonials Section */
.testimonials-section {
    padding: var(--spacing-20) 0;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-8);
}

.testimonial-card {
    background: white;
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.testimonial-text {
    font-size: var(--font-size-lg);
    line-height: 1.7;
    color: var(--gray-700);
    margin-bottom: var(--spacing-6);
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.author-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-full);
    object-fit: cover;
}

.author-name {
    font-weight: 600;
    color: var(--gray-900);
}

.author-role {
    color: var(--gray-500);
    font-size: var(--font-size-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-12);
        text-align: center;
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .step {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .step-reverse {
        grid-template-columns: 1fr;
    }
    
    .step-reverse .step-content,
    .step-reverse .step-image {
        order: unset;
    }
    
    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .demo-cards {
        grid-template-columns: 1fr;
    }

    .demo-title {
        font-size: var(--font-size-3xl);
    }
}

/* Animations */
@media (prefers-reduced-motion: no-preference) {
    .feature-card,
    .testimonial-card,
    .btn {
        transition: all var(--transition-normal);
    }
}

/* Focus States */
.btn:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* Pricing Section */
.pricing-section {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-12);
}

.pricing-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--spacing-8);
    border: 2px solid var(--gray-200);
    position: relative;
    transition: all var(--transition-normal);
}

.pricing-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-300);
}

.pricing-featured {
    border-color: var(--primary-500);
    transform: scale(1.05);
}

.pricing-featured:hover {
    transform: scale(1.05) translateY(-4px);
}

.pricing-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.pricing-header {
    text-align: center;
    margin-bottom: var(--spacing-8);
}

.pricing-title {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-2);
}

.pricing-description {
    color: var(--gray-600);
    margin-bottom: var(--spacing-6);
}

.pricing-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: var(--spacing-1);
}

.price-amount {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--primary-600);
}

.price-period {
    font-size: var(--font-size-lg);
    color: var(--gray-500);
}

.pricing-annual {
    margin-top: var(--spacing-2);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2);
}

.annual-note {
    font-size: var(--font-size-sm);
    color: var(--primary-600);
    font-weight: 600;
    background: var(--primary-50);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-full);
    display: inline-block;
}

.savings-badge {
    font-size: var(--font-size-xs);
    font-weight: 700;
    color: white;
    background: linear-gradient(135deg, #10b981, #059669);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-full);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.pricing-features {
    margin-bottom: var(--spacing-8);
}

.feature-list {
    list-style: none;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-2) 0;
}

.feature-list li::before {
    content: '✓';
    color: var(--success-500);
    font-weight: 700;
    font-size: var(--font-size-lg);
}

.pricing-note {
    text-align: center;
    color: var(--gray-600);
    font-size: var(--font-size-lg);
}

/* CTA Section */
.cta-section {
    padding: var(--spacing-20) 0;
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
}

.cta-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.cta-title {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-6);
    color: white;
}

.cta-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-8);
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: var(--spacing-4);
    justify-content: center;
    flex-wrap: wrap;
}

.cta-section .btn-outline {
    border-color: white;
    color: white;
}

.cta-section .btn-outline:hover {
    background-color: white;
    color: var(--primary-600);
}

/* Footer */
.footer {
    background-color: var(--gray-900);
    color: var(--gray-300);
    padding: var(--spacing-16) 0 var(--spacing-8);
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: var(--spacing-12);
    margin-bottom: var(--spacing-12);
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-4);
}

.footer-logo {
    width: 2rem;
    height: 2rem;
}

.footer-brand-name {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: white;
}

.footer-description {
    line-height: 1.7;
    margin-bottom: var(--spacing-6);
}

.footer-social {
    display: flex;
    gap: var(--spacing-4);
}

.social-link {
    width: 2.5rem;
    height: 2.5rem;
    background-color: var(--gray-800);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    transition: all var(--transition-fast);
}

.social-link:hover {
    background-color: var(--primary-600);
    color: white;
    transform: translateY(-2px);
}

.social-link svg {
    width: 1.25rem;
    height: 1.25rem;
}

.footer-title {
    color: white;
    font-weight: 600;
    margin-bottom: var(--spacing-4);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-2);
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: var(--spacing-8);
    text-align: center;
}

.footer-copyright {
    color: var(--gray-500);
}

/* Responsive Updates */
@media (max-width: 768px) {
    .pricing-grid {
        grid-template-columns: 1fr;
    }

    .pricing-featured {
        transform: none;
    }

    .pricing-featured:hover {
        transform: translateY(-4px);
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-8);
        text-align: center;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .hero-buttons,
    .floating-card,
    .footer {
        display: none;
    }
}
