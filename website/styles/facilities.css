/* Facilities Page Specific Styles */

/* Hero Section Enhancements */
.facilities-hero {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--warm-50) 30%, var(--primary-100) 100%);
}

.facilities-hero::before {
    background: radial-gradient(circle at 25% 25%, var(--primary-200) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, var(--warm-200) 0%, transparent 50%);
}

/* Benefits Section */
.benefits-section {
    padding: var(--spacing-20) 0;
    background: white;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-8);
}

.benefit-card {
    background: white;
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    border: 2px solid var(--gray-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.benefit-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500), var(--warm-500));
}

.benefit-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-300);
}

.benefit-icon {
    width: 3.5rem;
    height: 3.5rem;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-6);
}

.benefit-icon svg {
    width: 2rem;
    height: 2rem;
    color: white;
}

.benefit-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-3);
    color: var(--gray-900);
}

.benefit-description {
    color: var(--gray-600);
    line-height: 1.7;
    margin-bottom: var(--spacing-4);
}

.benefit-metric {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-600);
    background: var(--primary-50);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-full);
    display: inline-block;
}

/* Pricing Calculator Section */
.pricing-calculator-section {
    padding: var(--spacing-20) 0;
    background: var(--gray-50);
}

.calculator-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-12);
    margin-bottom: var(--spacing-12);
}

.calculator-inputs {
    background: white;
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
}

.input-group {
    margin-bottom: var(--spacing-6);
}

.input-group label {
    display: block;
    font-weight: 600;
    margin-bottom: var(--spacing-3);
    color: var(--gray-900);
}

.slider {
    width: 100%;
    height: 8px;
    border-radius: var(--radius-full);
    background: var(--gray-200);
    outline: none;
    -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    cursor: pointer;
    box-shadow: var(--shadow-md);
}

.slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow-md);
}

.slider-value {
    text-align: center;
    margin-top: var(--spacing-3);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-600);
}

.select-input {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    background: white;
    transition: border-color var(--transition-fast);
}

.select-input:focus {
    outline: none;
    border-color: var(--primary-500);
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    cursor: pointer;
    padding: var(--spacing-3);
    border-radius: var(--radius-lg);
    transition: background-color var(--transition-fast);
}

.checkbox-label:hover {
    background-color: var(--gray-50);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-500);
    border-color: var(--primary-500);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Calculator Results */
.calculator-card {
    background: white;
    border: 2px solid var(--primary-200);
    position: relative;
}

.calculator-card::before {
    background: linear-gradient(90deg, var(--primary-500), var(--warm-500));
}

.pricing-breakdown {
    margin-bottom: var(--spacing-6);
}

.breakdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3) 0;
    border-bottom: 1px solid var(--gray-100);
}

.breakdown-item:last-child {
    border-bottom: none;
}

.breakdown-label {
    color: var(--gray-600);
    font-weight: 500;
}

.breakdown-value {
    font-weight: 600;
    color: var(--gray-900);
}

.breakdown-divider {
    height: 2px;
    background: var(--gray-200);
    margin: var(--spacing-4) 0;
}

.breakdown-total {
    padding: var(--spacing-4) 0;
    border-top: 2px solid var(--primary-200);
    border-bottom: none;
}

.breakdown-total .breakdown-label {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--gray-900);
}

.total-price {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-600);
}

.pricing-features h4 {
    margin-bottom: var(--spacing-4);
    color: var(--gray-900);
}

.pricing-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    margin-top: var(--spacing-6);
}

/* Demo Section Enhancements */
.facilities-demo {
    background: linear-gradient(135deg, var(--warm-50) 0%, var(--primary-50) 100%);
}

.facility-admin-icon {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

.care-staff-icon {
    background: linear-gradient(135deg, var(--warm-500), var(--warm-600));
}

/* Contact Section */
.contact-section {
    padding: var(--spacing-20) 0;
    background: var(--gray-900);
    color: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
}

.contact-title {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-4);
    color: white;
}

.contact-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-300);
    margin-bottom: var(--spacing-8);
    line-height: 1.6;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
}

.contact-method {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.contact-icon {
    width: 3rem;
    height: 3rem;
    background: var(--primary-600);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-icon svg {
    width: 1.5rem;
    height: 1.5rem;
    color: white;
}

.contact-details h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-1);
    color: white;
}

.contact-details p {
    color: var(--gray-300);
    margin: 0;
}

/* Contact Form */
.contact-form {
    background: white;
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
}

.demo-request-form h3 {
    margin-bottom: var(--spacing-6);
    color: var(--gray-900);
    font-size: var(--font-size-2xl);
}

.form-group {
    margin-bottom: var(--spacing-5);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4);
}

.form-group label {
    display: block;
    font-weight: 600;
    margin-bottom: var(--spacing-2);
    color: var(--gray-900);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-500);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .calculator-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-8);
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-12);
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .pricing-actions {
        flex-direction: column;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
    }
}
