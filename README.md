# MemoCare - Memory Companion PWA

A compassionate AI-powered Progressive Web App designed to help people with memory challenges and their families stay connected through photos, conversations, and shared memories.

## Features

### 🤖 AI-Powered Memory Companion
- Intelligent conversations about photos and memories
- Context-aware responses with emotional intelligence
- Voice message support with text-to-speech
- Memory prompts and gentle conversation starters

### 📸 Smart Photo Management
- Advanced metadata extraction (EXIF, location, auto-tagging)
- Intelligent photo organization and search
- Face recognition and people tagging
- Photo-conversation linking for memory context

### 👥 Family Group Management
- Multi-tenant architecture for families
- Role-based permissions (<PERSON><PERSON>, <PERSON><PERSON>, Caregiver, Patient, Guest)
- Secure invitation system for family members
- Data isolation and privacy controls

### 💊 Medication Management
- Smart medication reminders with notifications
- Adherence tracking and analytics
- Caregiver monitoring and alerts
- Visual medication schedules

### 📊 Caregiver Dashboard
- Real-time patient monitoring
- Conversation insights and analytics
- Medication adherence tracking
- Activity patterns and engagement metrics

### ♿ Accessibility Features
- Senior-friendly interface design
- Voice navigation and controls
- High contrast mode support
- Large font options

## Getting Started

### Prerequisites
- Node.js 18+ and npm
- Supabase account and project
- (Optional) OpenAI API key for enhanced AI features

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd memocare
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Fill in your Supabase credentials:
   ```env
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   VITE_OPENAI_API_KEY=your_openai_api_key (optional)
   ```

4. **Set up the database**
   
   **Important:** For full functionality including family groups and multi-user features, you need to run the database migration:
   
   - Copy the SQL from `src/database/migrations/tenant_system.sql`
   - Go to your Supabase project dashboard → SQL Editor
   - Paste and run the SQL migration
   - Refresh the app
   
   See [DATABASE_SETUP.md](DATABASE_SETUP.md) for detailed instructions.

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open the app**
   Navigate to `http://localhost:5173` in your browser.

### Demo Credentials

For testing purposes, you can use these demo accounts:
- **Patient**: `<EMAIL>` / `demodemo`
- **Caregiver**: `<EMAIL>` / `demodemo`

## Architecture

### Tech Stack
- **Frontend**: React 18, TypeScript, Tailwind CSS
- **State Management**: Zustand with persistence
- **Database**: Supabase (PostgreSQL with Row Level Security)
- **AI Integration**: OpenAI GPT-4 Vision (with fallback responses)
- **PWA**: Vite PWA plugin with Workbox
- **UI Components**: Custom components with Framer Motion animations

### Key Features
- **Multi-tenant Architecture**: Secure family group isolation
- **Real-time Updates**: Supabase real-time subscriptions
- **Offline Support**: PWA with service worker caching
- **Responsive Design**: Mobile-first, senior-friendly interface
- **Security**: Row-level security, permission-based access control

## Project Structure

```
src/
├── components/          # React components
│   ├── auth/           # Authentication components
│   ├── caregiver/      # Caregiver dashboard and insights
│   ├── chat/           # AI chat interface
│   ├── photos/         # Photo management
│   ├── tenant/         # Family group management
│   └── ui/             # Reusable UI components
├── services/           # API and business logic
├── store/              # Zustand state management
├── types/              # TypeScript type definitions
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
└── database/           # Database migrations and schema
```

## Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks

### Database Management
- Database schema is in `src/database/migrations/`
- Run migrations through Supabase SQL Editor
- Use Row Level Security for data isolation

### Adding New Features
1. Create components in appropriate directories
2. Add types to `src/types/`
3. Create services for API interactions
4. Update database schema if needed
5. Add tests and documentation

## Deployment

### Production Setup
1. **Build the application**
   ```bash
   npm run build
   ```

2. **Deploy to your hosting platform**
   - Vercel, Netlify, or any static hosting
   - Configure environment variables
   - Set up custom domain (optional)

3. **Configure Supabase for production**
   - Update CORS settings
   - Configure email templates
   - Set up storage buckets
   - Enable real-time features

### Environment Variables
- `VITE_SUPABASE_URL` - Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY` - Supabase anonymous key
- `VITE_OPENAI_API_KEY` - OpenAI API key (optional)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Check the [DATABASE_SETUP.md](DATABASE_SETUP.md) for setup issues
- Review the browser console for error messages
- Ensure all environment variables are configured correctly

## Roadmap

- [ ] Video calling integration
- [ ] Advanced AI models (Claude, local LLMs)
- [ ] Mobile app versions (React Native)
- [ ] Integration with health monitoring devices
- [ ] Advanced analytics and reporting
- [ ] Multi-language support expansion
