-- Drop all tables and clean up for Memory Companion PWA
-- Run this BEFORE running the main schema.sql

-- Disable <PERSON><PERSON> first to avoid issues
ALTER TABLE IF EXISTS caregiver_insights DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS medication_reminders DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS medications DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS messages DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS conversations DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS photos DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS patient_caregivers DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS users DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS tenants DISABLE ROW LEVEL SECURITY;

-- Drop all policies first
DROP POLICY IF EXISTS "Users can manage own relationships" ON patient_caregivers;
DROP POLICY IF EXISTS "Users can manage insights" ON caregiver_insights;
DROP POLICY IF EXISTS "Users can manage own reminders" ON medication_reminders;
DROP POLICY IF EXISTS "Users can manage own medications" ON medications;
DROP POLICY IF EXISTS "Users can manage messages" ON messages;
DROP POLICY IF EXISTS "Users can manage own conversations" ON conversations;
DROP POLICY IF EXISTS "Users can delete own photos" ON photos;
DROP POLICY IF EXISTS "Users can update own photos" ON photos;
DROP POLICY IF EXISTS "Users can insert own photos" ON photos;
DROP POLICY IF EXISTS "Users can view own photos" ON photos;
DROP POLICY IF EXISTS "Users can insert own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "View patient-caregiver relationships" ON patient_caregivers;
DROP POLICY IF EXISTS "Caregivers can view patient insights" ON caregiver_insights;
DROP POLICY IF EXISTS "Caregivers can view patient reminders" ON medication_reminders;
DROP POLICY IF EXISTS "Caregivers can manage patient medications" ON medications;
DROP POLICY IF EXISTS "Users can insert messages in own conversations" ON messages;
DROP POLICY IF EXISTS "Users can view messages in own conversations" ON messages;
DROP POLICY IF EXISTS "Caregivers can view patient conversations" ON conversations;
DROP POLICY IF EXISTS "Users can insert own conversations" ON conversations;
DROP POLICY IF EXISTS "Users can view own conversations" ON conversations;
DROP POLICY IF EXISTS "Caregivers can view patient photos" ON photos;
DROP POLICY IF EXISTS "Users can view users in same tenant" ON users;

-- Drop triggers
DROP TRIGGER IF EXISTS update_reminders_updated_at ON medication_reminders;
DROP TRIGGER IF EXISTS update_medications_updated_at ON medications;
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
DROP TRIGGER IF EXISTS update_tenants_updated_at ON tenants;

-- Drop functions
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Drop indexes (they'll be dropped with tables, but just to be safe)
DROP INDEX IF EXISTS idx_insights_date;
DROP INDEX IF EXISTS idx_insights_patient_id;
DROP INDEX IF EXISTS idx_reminders_status;
DROP INDEX IF EXISTS idx_reminders_scheduled_time;
DROP INDEX IF EXISTS idx_reminders_user_id;
DROP INDEX IF EXISTS idx_reminders_medication_id;
DROP INDEX IF EXISTS idx_medications_active;
DROP INDEX IF EXISTS idx_medications_user_id;
DROP INDEX IF EXISTS idx_messages_created_at;
DROP INDEX IF EXISTS idx_messages_conversation_id;
DROP INDEX IF EXISTS idx_conversations_last_active;
DROP INDEX IF EXISTS idx_conversations_photo_id;
DROP INDEX IF EXISTS idx_conversations_user_id;
DROP INDEX IF EXISTS idx_photos_uploaded_at;
DROP INDEX IF EXISTS idx_photos_tenant_id;
DROP INDEX IF EXISTS idx_photos_user_id;
DROP INDEX IF EXISTS idx_patient_caregivers_caregiver_id;
DROP INDEX IF EXISTS idx_patient_caregivers_patient_id;
DROP INDEX IF EXISTS idx_users_role;
DROP INDEX IF EXISTS idx_users_email;
DROP INDEX IF EXISTS idx_users_tenant_id;

-- Drop tables in reverse dependency order
DROP TABLE IF EXISTS caregiver_insights CASCADE;
DROP TABLE IF EXISTS medication_reminders CASCADE;
DROP TABLE IF EXISTS medications CASCADE;
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS conversations CASCADE;
DROP TABLE IF EXISTS photos CASCADE;
DROP TABLE IF EXISTS patient_caregivers CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS tenants CASCADE;

-- Drop custom types
DROP TYPE IF EXISTS conversation_style;
DROP TYPE IF EXISTS insight_severity;
DROP TYPE IF EXISTS insight_type;
DROP TYPE IF EXISTS message_type;
DROP TYPE IF EXISTS reminder_status;
DROP TYPE IF EXISTS medication_frequency;
DROP TYPE IF EXISTS tenant_plan;
DROP TYPE IF EXISTS user_role;

-- Note: Extensions are usually kept as they don't interfere
-- DROP EXTENSION IF EXISTS "pgcrypto";
-- DROP EXTENSION IF EXISTS "uuid-ossp";

-- Success message
SELECT 'All tables, policies, and types have been dropped successfully!' as status;
