-- Memory Companion PWA Database Schema
-- This schema supports multi-tenant architecture with proper data isolation

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom types
CREATE TYPE user_role AS ENUM ('patient', 'caregiver', 'admin');
CREATE TYPE tenant_plan AS ENUM ('free', 'premium', 'professional');
CREATE TYPE medication_frequency AS ENUM ('daily', 'twice-daily', 'three-times-daily', 'as-needed');
CREATE TYPE reminder_status AS ENUM ('pending', 'taken', 'missed', 'skipped');
CREATE TYPE message_type AS ENUM ('user', 'assistant');
CREATE TYPE insight_type AS ENUM ('engagement', 'medication', 'mood', 'activity');
CREATE TYPE insight_severity AS ENUM ('low', 'medium', 'high');
CREATE TYPE conversation_style AS ENUM ('casual', 'formal', 'encouraging');

-- Tenants table (for multi-family support)
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    plan tenant_plan DEFAULT 'free',
    settings JSONB DEFAULT '{}',
    max_users INTEGER DEFAULT 5,
    allowed_languages TEXT[] DEFAULT ARRAY['en'],
    features TEXT[] DEFAULT ARRAY[]::TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Users table with enhanced profile support
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    role user_role NOT NULL,
    avatar_url TEXT,
    language VARCHAR(10) DEFAULT 'en',
    preferences JSONB DEFAULT '{
        "voiceEnabled": true,
        "fontSize": "medium",
        "highContrast": false,
        "notifications": true,
        "conversationStyle": "encouraging",
        "reminderVolume": 0.8
    }',
    is_active BOOLEAN DEFAULT true,
    last_seen_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Patient-Caregiver relationships
CREATE TABLE patient_caregivers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id UUID REFERENCES users(id) ON DELETE CASCADE,
    caregiver_id UUID REFERENCES users(id) ON DELETE CASCADE,
    relationship VARCHAR(50), -- 'spouse', 'child', 'professional', etc.
    permissions JSONB DEFAULT '{
        "viewConversations": true,
        "manageMedications": true,
        "receiveAlerts": true,
        "viewInsights": true
    }',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(patient_id, caregiver_id)
);

-- Photos table with enhanced metadata
CREATE TABLE photos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    thumbnail_url TEXT,
    file_size INTEGER,
    mime_type VARCHAR(100),
    description TEXT,
    metadata JSONB DEFAULT '{}', -- EXIF data, location, people, etc.
    tags TEXT[] DEFAULT ARRAY[]::TEXT[],
    is_favorite BOOLEAN DEFAULT false,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Conversations table
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    photo_id UUID REFERENCES photos(id) ON DELETE SET NULL,
    title VARCHAR(255),
    context JSONB DEFAULT '{}', -- Conversation context and memory
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    message_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Messages table
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
    type message_type NOT NULL,
    content TEXT NOT NULL,
    audio_url TEXT,
    metadata JSONB DEFAULT '{}', -- AI model info, confidence, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Medications table
CREATE TABLE medications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    dosage VARCHAR(100) NOT NULL,
    frequency medication_frequency NOT NULL,
    times TEXT[] NOT NULL, -- Array of time strings like ["08:00", "20:00"]
    instructions TEXT,
    photo_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Medication reminders table
CREATE TABLE medication_reminders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    medication_id UUID REFERENCES medications(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL,
    actual_time TIMESTAMP WITH TIME ZONE,
    status reminder_status DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Caregiver insights table
CREATE TABLE caregiver_insights (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id UUID REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    type insight_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    value_text VARCHAR(255),
    value_numeric DECIMAL,
    trend VARCHAR(10), -- 'up', 'down', 'stable'
    severity insight_severity,
    metadata JSONB DEFAULT '{}',
    date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_patient_caregivers_patient_id ON patient_caregivers(patient_id);
CREATE INDEX idx_patient_caregivers_caregiver_id ON patient_caregivers(caregiver_id);
CREATE INDEX idx_photos_user_id ON photos(user_id);
CREATE INDEX idx_photos_tenant_id ON photos(tenant_id);
CREATE INDEX idx_photos_uploaded_at ON photos(uploaded_at DESC);
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_photo_id ON conversations(photo_id);
CREATE INDEX idx_conversations_last_active ON conversations(last_active_at DESC);
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE INDEX idx_medications_user_id ON medications(user_id);
CREATE INDEX idx_medications_active ON medications(is_active);
CREATE INDEX idx_reminders_medication_id ON medication_reminders(medication_id);
CREATE INDEX idx_reminders_user_id ON medication_reminders(user_id);
CREATE INDEX idx_reminders_scheduled_time ON medication_reminders(scheduled_time);
CREATE INDEX idx_reminders_status ON medication_reminders(status);
CREATE INDEX idx_insights_patient_id ON caregiver_insights(patient_id);
CREATE INDEX idx_insights_date ON caregiver_insights(date DESC);

-- Enable Row Level Security (RLS)
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE patient_caregivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE medications ENABLE ROW LEVEL SECURITY;
ALTER TABLE medication_reminders ENABLE ROW LEVEL SECURITY;
ALTER TABLE caregiver_insights ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Users can only see users in their tenant
CREATE POLICY "Users can view users in same tenant" ON users
    FOR SELECT USING (tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid()));

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (id = auth.uid());

-- Patient-caregiver relationships
CREATE POLICY "View patient-caregiver relationships" ON patient_caregivers
    FOR SELECT USING (
        patient_id = auth.uid() OR
        caregiver_id = auth.uid() OR
        EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
    );

-- Photos policies
CREATE POLICY "Users can view own photos" ON photos
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert own photos" ON photos
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Caregivers can view patient photos" ON photos
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM patient_caregivers pc
            WHERE pc.patient_id = photos.user_id
            AND pc.caregiver_id = auth.uid()
            AND (pc.permissions->>'viewConversations')::boolean = true
        )
    );

-- Conversations policies
CREATE POLICY "Users can view own conversations" ON conversations
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert own conversations" ON conversations
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Caregivers can view patient conversations" ON conversations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM patient_caregivers pc
            WHERE pc.patient_id = conversations.user_id
            AND pc.caregiver_id = auth.uid()
            AND (pc.permissions->>'viewConversations')::boolean = true
        )
    );

-- Messages policies
CREATE POLICY "Users can view messages in own conversations" ON messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM conversations c
            WHERE c.id = messages.conversation_id
            AND c.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert messages in own conversations" ON messages
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM conversations c
            WHERE c.id = messages.conversation_id
            AND c.user_id = auth.uid()
        )
    );

-- Medications policies
CREATE POLICY "Users can manage own medications" ON medications
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Caregivers can manage patient medications" ON medications
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM patient_caregivers pc
            WHERE pc.patient_id = medications.user_id
            AND pc.caregiver_id = auth.uid()
            AND (pc.permissions->>'manageMedications')::boolean = true
        )
    );

-- Medication reminders policies
CREATE POLICY "Users can manage own reminders" ON medication_reminders
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Caregivers can view patient reminders" ON medication_reminders
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM patient_caregivers pc
            WHERE pc.patient_id = medication_reminders.user_id
            AND pc.caregiver_id = auth.uid()
            AND (pc.permissions->>'manageMedications')::boolean = true
        )
    );

-- Insights policies
CREATE POLICY "Caregivers can view patient insights" ON caregiver_insights
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM patient_caregivers pc
            WHERE pc.patient_id = caregiver_insights.patient_id
            AND pc.caregiver_id = auth.uid()
            AND (pc.permissions->>'viewInsights')::boolean = true
        )
    );

-- Utility functions
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_medications_updated_at BEFORE UPDATE ON medications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reminders_updated_at BEFORE UPDATE ON medication_reminders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
