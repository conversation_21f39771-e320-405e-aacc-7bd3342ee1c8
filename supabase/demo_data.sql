-- Demo Data for Memory Companion PWA
-- Run this after creating the demo users in Supabase Auth

-- Note: Replace these UUIDs with the actual UUIDs from your Supabase Auth users
-- You can find them in Supabase Dashboard > Authentication > Users

-- Demo Patient Data (replace with actual <NAME_EMAIL>)
-- Demo Caregiver Data (replace with actual <NAME_EMAIL>)

-- First, let's create demo tenants and users
-- You'll need to replace the UUIDs below with the actual ones from Supabase Auth

-- <NAME_EMAIL> (replace 'patient-uuid-here' with actual UUID)
/*
INSERT INTO tenants (id, name, plan, allowed_languages, features) VALUES 
('demo-tenant-patient', 'Demo Patient Family', 'premium', ARRAY['en'], ARRAY['ai_chat', 'photo_upload', 'medication_reminders']);

INSERT INTO users (id, tenant_id, email, name, role, language, preferences) VALUES 
('patient-uuid-here', 'demo-tenant-patient', '<EMAIL>', 'Demo Patient', 'patient', 'en', 
jsonb_build_object(
    'voiceEnabled', true,
    'fontSize', 'medium',
    'highContrast', false,
    'notifications', true,
    'conversationStyle', 'encouraging',
    'reminderVolume', 0.8
));
*/

-- <NAME_EMAIL> (replace 'caregiver-uuid-here' with actual UUID)
/*
INSERT INTO tenants (id, name, plan, allowed_languages, features) VALUES 
('demo-tenant-caregiver', 'Demo Caregiver Family', 'professional', ARRAY['en'], ARRAY['ai_chat', 'photo_upload', 'medication_reminders', 'caregiver_insights']);

INSERT INTO users (id, tenant_id, email, name, role, language, preferences) VALUES 
('caregiver-uuid-here', 'demo-tenant-caregiver', '<EMAIL>', 'Demo Caregiver', 'caregiver', 'en', 
jsonb_build_object(
    'voiceEnabled', true,
    'fontSize', 'medium',
    'highContrast', false,
    'notifications', true,
    'conversationStyle', 'encouraging',
    'reminderVolume', 0.8
));
*/

-- Demo Photos for Patient
/*
INSERT INTO photos (id, user_id, tenant_id, filename, url, description, metadata, tags, is_favorite) VALUES 
('demo-photo-1', 'patient-uuid-here', 'demo-tenant-patient', 'family_dinner.jpg', 
'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?w=800', 
'A lovely family dinner with everyone gathered around the table',
jsonb_build_object('event', 'Family Dinner', 'people', ARRAY['Family'], 'location', 'Home'),
ARRAY['family', 'dinner', 'memories'], true),

('demo-photo-2', 'patient-uuid-here', 'demo-tenant-patient', 'garden_flowers.jpg',
'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=800',
'Beautiful flowers blooming in the garden',
jsonb_build_object('event', 'Gardening', 'location', 'Garden'),
ARRAY['flowers', 'garden', 'nature'], false),

('demo-photo-3', 'patient-uuid-here', 'demo-tenant-patient', 'birthday_celebration.jpg',
'https://images.unsplash.com/photo-1464349095431-e9a21285b5f3?w=800',
'Birthday celebration with cake and candles',
jsonb_build_object('event', 'Birthday', 'people', ARRAY['Family']),
ARRAY['birthday', 'celebration', 'cake'], true);
*/

-- Demo Medications for Patient
/*
INSERT INTO medications (id, user_id, tenant_id, name, dosage, frequency, times, instructions, is_active) VALUES 
('demo-med-1', 'patient-uuid-here', 'demo-tenant-patient', 'Vitamin D', '1000 IU', 'daily', ARRAY['09:00'], 'Take with breakfast', true),
('demo-med-2', 'patient-uuid-here', 'demo-tenant-patient', 'Blood Pressure Medication', '10mg', 'twice-daily', ARRAY['08:00', '20:00'], 'Take with food', true),
('demo-med-3', 'patient-uuid-here', 'demo-tenant-patient', 'Memory Support', '500mg', 'daily', ARRAY['19:00'], 'Take with dinner', true);
*/

-- Demo Conversations for Patient
/*
INSERT INTO conversations (id, user_id, tenant_id, photo_id, title, context, started_at, last_active_at, message_count) VALUES 
('demo-conv-1', 'patient-uuid-here', 'demo-tenant-patient', 'demo-photo-1', 'Family Dinner Memories', 
jsonb_build_object('mood', 'happy', 'topics', ARRAY['family', 'food', 'togetherness']),
NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days', 6),

('demo-conv-2', 'patient-uuid-here', 'demo-tenant-patient', 'demo-photo-2', 'Garden Conversations',
jsonb_build_object('mood', 'peaceful', 'topics', ARRAY['nature', 'gardening', 'flowers']),
NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day', 4);
*/

-- Demo Messages for Conversations
/*
INSERT INTO messages (conversation_id, type, content, created_at) VALUES 
-- Family Dinner Conversation
('demo-conv-1', 'user', 'This is a photo from our family dinner last week.', NOW() - INTERVAL '2 days'),
('demo-conv-1', 'assistant', 'What a wonderful family gathering! I can see everyone looks so happy. Tell me about this special dinner - who was there with you?', NOW() - INTERVAL '2 days' + INTERVAL '30 seconds'),
('demo-conv-1', 'user', 'My children and grandchildren were all there. We had such a lovely time.', NOW() - INTERVAL '2 days' + INTERVAL '1 minute'),
('demo-conv-1', 'assistant', 'How beautiful! Family dinners create such precious memories. What was your favorite part of the evening?', NOW() - INTERVAL '2 days' + INTERVAL '90 seconds'),
('demo-conv-1', 'user', 'Seeing my grandchildren laugh and play together. They bring so much joy.', NOW() - INTERVAL '2 days' + INTERVAL '2 minutes'),
('demo-conv-1', 'assistant', 'That sounds absolutely heartwarming. The laughter of grandchildren is truly one of life''s greatest gifts. What games do they like to play?', NOW() - INTERVAL '2 days' + INTERVAL '150 seconds'),

-- Garden Conversation
('demo-conv-2', 'user', 'I love spending time in my garden.', NOW() - INTERVAL '1 day'),
('demo-conv-2', 'assistant', 'Gardening is such a peaceful and rewarding activity! What''s blooming in your garden right now?', NOW() - INTERVAL '1 day' + INTERVAL '30 seconds'),
('demo-conv-2', 'user', 'The roses are beautiful this year, and the tomatoes are growing well.', NOW() - INTERVAL '1 day' + INTERVAL '1 minute'),
('demo-conv-2', 'assistant', 'How wonderful! There''s something so satisfying about watching things grow. Do you have a favorite flower in your garden?', NOW() - INTERVAL '1 day' + INTERVAL '90 seconds');
*/

-- Demo Medication Reminders for today
/*
INSERT INTO medication_reminders (medication_id, user_id, scheduled_time, status) VALUES 
('demo-med-1', 'patient-uuid-here', CURRENT_DATE + INTERVAL '9 hours', 'taken'),
('demo-med-2', 'patient-uuid-here', CURRENT_DATE + INTERVAL '8 hours', 'taken'),
('demo-med-2', 'patient-uuid-here', CURRENT_DATE + INTERVAL '20 hours', 'pending'),
('demo-med-3', 'patient-uuid-here', CURRENT_DATE + INTERVAL '19 hours', 'pending');
*/

-- Demo Caregiver Insights
/*
INSERT INTO caregiver_insights (patient_id, tenant_id, type, title, description, value_text, trend, severity, date) VALUES 
('patient-uuid-here', 'demo-tenant-patient', 'engagement', 'Daily Conversations', 'Patient had 2 meaningful conversations today', '2 conversations', 'stable', 'low', CURRENT_DATE),
('patient-uuid-here', 'demo-tenant-patient', 'medication', 'Medication Adherence', 'All morning medications taken on time', '100% adherence', 'up', 'low', CURRENT_DATE),
('patient-uuid-here', 'demo-tenant-patient', 'mood', 'Mood Assessment', 'Patient expressed joy when discussing family', 'Positive mood', 'up', 'low', CURRENT_DATE);
*/

-- Instructions:
-- 1. Create the demo users in Supabase Auth Dashboard first
-- 2. Copy their UUIDs from the Auth dashboard
-- 3. Replace 'patient-uuid-here' and 'caregiver-uuid-here' with actual UUIDs
-- 4. Uncomment and run the relevant sections above
-- 5. The demo users will then have realistic data to explore the app
