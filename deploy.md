# 🚀 MemoCare Deployment Guide

## 📋 Pre-Deployment Checklist

### ✅ Prerequisites
- [ ] Node.js 18+ installed
- [ ] Git repository set up
- [ ] Supabase project configured
- [ ] Environment variables documented
- [ ] Domain name purchased (optional for testing)

### ✅ Environment Setup
- [ ] Production Supabase project created
- [ ] API keys generated and secured
- [ ] Database schema deployed
- [ ] Row Level Security (RLS) policies configured
- [ ] Storage buckets created with proper permissions

---

## 🏗️ Deployment Options

### Option 1: Vercel (Recommended for Production)

#### Step 1: Prepare Repository
```bash
# Ensure clean git state
git add .
git commit -m "Prepare for deployment"
git push origin main
```

#### Step 2: Deploy to Vercel
```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Deploy from project root
vercel --prod

# Follow prompts:
# - Link to existing project? No
# - Project name: memocare
# - Directory: ./
# - Override settings? No
```

#### Step 3: Configure Environment Variables
```bash
# Set production environment variables
vercel env add VITE_SUPABASE_URL production
vercel env add VITE_SUPABASE_ANON_KEY production
vercel env add VITE_OPENAI_API_KEY production
vercel env add VITE_APP_ENV production

# Redeploy with new env vars
vercel --prod
```

### Option 2: Netlify (Alternative)

#### Step 1: Build Configuration
Create `netlify.toml`:
```toml
[build]
  command = "npm run build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

#### Step 2: Deploy
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login and deploy
netlify login
netlify deploy --prod --dir=dist
```

### Option 3: Quick Mobile Testing (Fastest)

#### Using Vercel (2 minutes)
```bash
# From project root
npx vercel

# Follow prompts for preview deployment
# Get instant URL like: https://memocare-abc123.vercel.app
```

#### Using Netlify Drop (1 minute)
```bash
# Build the project
npm run build

# Go to https://app.netlify.com/drop
# Drag and drop the 'dist' folder
# Get instant URL
```

---

## 🔧 Production Configuration

### Supabase Production Setup

#### 1. Create Production Project
```sql
-- Run in Supabase SQL Editor

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create tables (copy from your development schema)
-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE medications ENABLE ROW LEVEL SECURITY;

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public) 
VALUES ('photos', 'photos', true);

INSERT INTO storage.buckets (id, name, public) 
VALUES ('avatars', 'avatars', true);
```

#### 2. Configure Storage Policies
```sql
-- Photos bucket policy
CREATE POLICY "Users can upload photos" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'photos' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view photos" ON storage.objects
FOR SELECT USING (bucket_id = 'photos');

-- Avatars bucket policy  
CREATE POLICY "Users can upload avatars" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);
```

### Environment Variables

#### Production .env
```bash
# Supabase
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# OpenAI
VITE_OPENAI_API_KEY=your-openai-key

# App Configuration
VITE_APP_ENV=production
VITE_APP_URL=https://your-domain.com

# Analytics (optional)
VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX
VITE_SENTRY_DSN=https://your-sentry-dsn
```

---

## 📱 Mobile Testing Deployment

### Fastest Method: Vercel Preview

```bash
# 1. Ensure you're in project root
cd /path/to/memocare

# 2. Quick deploy (no account needed for preview)
npx vercel

# 3. Follow prompts:
# ? Set up and deploy? Yes
# ? Which scope? Your personal account
# ? Link to existing project? No
# ? Project name: memocare-test
# ? In which directory? ./
# ? Override settings? No

# 4. Get URL like: https://memocare-test-abc123.vercel.app
# 5. Open on iPhone Safari
```

### Alternative: Surge.sh (Super Fast)

```bash
# 1. Build the project
npm run build

# 2. Install and deploy with Surge
npm install -g surge
cd dist
surge

# 3. Choose domain: memocare-test.surge.sh
# 4. Open on iPhone
```

---

## 🌐 Custom Domain Setup

### Vercel Custom Domain
```bash
# Add domain in Vercel dashboard
vercel domains add yourdomain.com

# Configure DNS:
# Type: CNAME
# Name: @
# Value: cname.vercel-dns.com
```

### SSL Certificate
- Vercel/Netlify provide automatic SSL
- Certificate auto-renews
- HTTPS enforced by default

---

## 📊 Monitoring Setup

### 1. Error Tracking (Sentry)
```bash
# Install Sentry
npm install @sentry/react @sentry/tracing

# Configure in main.tsx
import * as Sentry from "@sentry/react";

Sentry.init({
  dsn: import.meta.env.VITE_SENTRY_DSN,
  environment: import.meta.env.VITE_APP_ENV,
});
```

### 2. Analytics (Google Analytics)
```html
<!-- Add to index.html -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

### 3. Uptime Monitoring
- UptimeRobot (free tier)
- Pingdom
- StatusCake

---

## 🔒 Security Checklist

### Pre-Deployment Security
- [ ] Environment variables secured (no hardcoded secrets)
- [ ] Supabase RLS policies enabled
- [ ] CORS configured properly
- [ ] Rate limiting implemented
- [ ] Input validation on all forms
- [ ] XSS protection enabled
- [ ] CSRF protection configured

### Post-Deployment Security
- [ ] SSL certificate active
- [ ] Security headers configured
- [ ] Content Security Policy (CSP) set
- [ ] Regular dependency updates scheduled
- [ ] Backup strategy implemented

---

## 🚨 Rollback Plan

### Quick Rollback (Vercel)
```bash
# List deployments
vercel ls

# Rollback to previous deployment
vercel rollback [deployment-url]
```

### Database Rollback
```sql
-- Supabase point-in-time recovery
-- Available in dashboard under Database > Backups
-- Can restore to any point in last 7 days
```

---

## 📈 Performance Optimization

### Build Optimization
```bash
# Analyze bundle size
npm run build
npx vite-bundle-analyzer dist

# Optimize images
npm install -D vite-plugin-imagemin
```

### CDN Configuration
- Vercel Edge Network (automatic)
- Cloudflare (additional layer)
- Image optimization enabled

---

## 🎯 Deployment Checklist

### Pre-Launch
- [ ] All features tested in staging
- [ ] Database migrations completed
- [ ] Environment variables configured
- [ ] Monitoring tools active
- [ ] Backup strategy in place
- [ ] Security scan completed

### Launch Day
- [ ] Deploy to production
- [ ] Verify all functionality
- [ ] Test on multiple devices
- [ ] Monitor error rates
- [ ] Check performance metrics
- [ ] Announce to stakeholders

### Post-Launch
- [ ] Monitor for 24 hours
- [ ] Address any issues immediately
- [ ] Collect user feedback
- [ ] Plan next iteration

---

## 📞 Support Contacts

### Emergency Contacts
- **Vercel Support**: <EMAIL>
- **Supabase Support**: <EMAIL>
- **Domain Registrar**: [Your registrar support]

### Monitoring Alerts
- Set up alerts for:
  - Error rate > 1%
  - Response time > 2s
  - Uptime < 99%
  - Database connections > 80%

---

## 🎉 Success Metrics

### Technical KPIs
- **Uptime**: >99.9%
- **Load Time**: <2s
- **Error Rate**: <0.5%
- **Mobile Performance**: >90 Lighthouse score

### Business KPIs
- **User Registrations**: Track daily signups
- **Demo Conversions**: Monitor demo → signup rate
- **Feature Usage**: Track core feature adoption
- **User Satisfaction**: Monitor support tickets

---

**Ready for deployment!** 🚀

For immediate mobile testing, use the Vercel preview method - it's the fastest way to get a live URL for iPhone testing.
