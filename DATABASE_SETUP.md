# MemoCare Database Setup

## Quick Setup Instructions

To enable the full tenant management system, you need to run the database migration. Here's how:

### Option 1: Supabase Dashboard (Recommended)

1. **Go to your Supabase project dashboard**
2. **Navigate to SQL Editor**
3. **Copy and paste the contents of `src/database/migrations/tenant_system.sql`**
4. **Click "Run" to execute the migration**

### Option 2: Supabase CLI

```bash
# If you have Supabase CLI installed
supabase db reset
# Then apply the migration
supabase db push
```

### What the Migration Creates

The migration will create the following tables:

- **`tenants`** - Family groups and organizations
- **`tenant_users`** - User-tenant relationships with roles
- **`tenant_invitations`** - Email invitations to join families
- **`tenant_activities`** - Audit log of all tenant activities
- **`tenant_usage_stats`** - Analytics and usage statistics
- **`photo_conversations`** - Links photos to chat conversations

### Default Roles and Permissions

- **Owner** - Full control, billing, can delete tenant
- **Admin** - User management, settings (except billing)
- **Caregiver** - Care coordination, monitoring, medication management
- **Patient** - Personal use, emergency access
- **Guest** - Limited view-only access

### After Running the Migration

1. **Refresh the app** - The tenant selector will now work
2. **Create your first family group** - Click "Create Family Group"
3. **Invite family members** - Use the invitation system
4. **Configure settings** - Customize features for your family

### Troubleshooting

**If you see "Failed to load family groups":**
- The database tables haven't been created yet
- Run the migration SQL in your Supabase dashboard
- Refresh the app after running the migration

**If you see permission errors:**
- Make sure Row Level Security (RLS) policies are enabled
- The migration includes all necessary RLS policies

**If invitations don't work:**
- Check that the `tenant_invitations` table was created
- Verify email settings in your Supabase project

### Development vs Production

**Development:**
- The app will work without the migration (limited functionality)
- Mock tenants are created when tables don't exist
- No data persistence for tenant features

**Production:**
- Run the full migration for complete functionality
- All tenant features will work as designed
- Data is properly isolated between families

### Security Features

The migration includes:
- **Row Level Security (RLS)** on all tables
- **Data isolation** between tenants
- **Permission-based access control**
- **Audit logging** for all activities
- **Secure invitation tokens**

### Next Steps

After setting up the database:

1. **Test tenant creation** - Create a family group
2. **Test invitations** - Invite a family member
3. **Configure settings** - Customize features
4. **Upload photos** - Test photo sharing
5. **Try AI chat** - Test conversation features

For any issues, check the browser console for detailed error messages.
