import OpenAI from 'openai';
import type { Photo, Message } from '../types';

const openai = new OpenAI({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true // Note: In production, this should be handled server-side
});

export interface ConversationContext {
  userName: string;
  userAge?: number;
  memoryStage?: 'early' | 'moderate' | 'advanced';
  conversationStyle: 'casual' | 'formal' | 'encouraging';
  photo?: Photo;
  previousMessages: Message[];
  personalDetails?: {
    interests?: string[];
    family?: string[];
    occupation?: string;
    location?: string;
  };
}

export class AIConversationService {
  private buildSystemPrompt(context: ConversationContext): string {
    const { userName, memoryStage = 'early', conversationStyle, photo, personalDetails } = context;
    
    let systemPrompt = `You are a compassionate AI companion designed to help people with memory challenges engage in meaningful conversations about their photos and memories. 

Your role:
- Be warm, patient, and encouraging
- Help ${userName} recall and discuss memories associated with photos
- Ask gentle, open-ended questions that prompt memory recall
- Celebrate any memories shared, no matter how small
- Never correct or challenge memories - be supportive
- Keep conversations positive and uplifting
- Use a ${conversationStyle} tone throughout

Memory stage: ${memoryStage}
${memoryStage === 'early' ? 'The person has mild memory challenges but can usually recall recent events and most past memories with some prompting.' : ''}
${memoryStage === 'moderate' ? 'The person has moderate memory challenges and may need more support and patience to recall memories.' : ''}
${memoryStage === 'advanced' ? 'The person has significant memory challenges. Focus on emotions and feelings rather than specific details.' : ''}

`;

    if (photo) {
      systemPrompt += `Current photo context:
- Filename: ${photo.filename}
- Description: ${photo.description || 'No description provided'}
- Upload date: ${new Date(photo.uploadedAt).toLocaleDateString()}
`;

      if (photo.metadata) {
        if (photo.metadata.location) systemPrompt += `- Location: ${photo.metadata.location}\n`;
        if (photo.metadata.people) systemPrompt += `- People mentioned: ${photo.metadata.people.join(', ')}\n`;
        if (photo.metadata.event) systemPrompt += `- Event: ${photo.metadata.event}\n`;
        if (photo.metadata.date) systemPrompt += `- Photo date: ${photo.metadata.date}\n`;
      }
    }

    if (personalDetails) {
      systemPrompt += `\nPersonal context about ${userName}:`;
      if (personalDetails.interests) systemPrompt += `\n- Interests: ${personalDetails.interests.join(', ')}`;
      if (personalDetails.family) systemPrompt += `\n- Family: ${personalDetails.family.join(', ')}`;
      if (personalDetails.occupation) systemPrompt += `\n- Occupation: ${personalDetails.occupation}`;
      if (personalDetails.location) systemPrompt += `\n- Location: ${personalDetails.location}`;
    }

    systemPrompt += `\n\nConversation guidelines:
- Start with warm, welcoming language
- Ask one question at a time
- Give ${userName} time to process and respond
- If they seem confused, gently redirect to emotions or feelings
- Use their name occasionally to maintain personal connection
- Keep responses concise but warm (2-3 sentences typically)
- If discussing a photo, ask about what they see, how it makes them feel, or what it reminds them of`;

    return systemPrompt;
  }

  private buildConversationHistory(messages: Message[]): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
    return messages.map(msg => ({
      role: msg.type === 'user' ? 'user' : 'assistant',
      content: msg.content
    }));
  }

  async generateResponse(context: ConversationContext, userMessage: string): Promise<string> {
    try {
      const systemPrompt = this.buildSystemPrompt(context);
      const conversationHistory = this.buildConversationHistory(context.previousMessages);

      const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
        { role: 'system', content: systemPrompt },
        ...conversationHistory,
        { role: 'user', content: userMessage }
      ];

      const completion = await openai.chat.completions.create({
        model: 'gpt-4o-mini', // Using the more cost-effective model
        messages,
        max_tokens: 200, // Keep responses concise
        temperature: 0.7, // Balanced creativity and consistency
        presence_penalty: 0.1, // Slight penalty to avoid repetition
        frequency_penalty: 0.1
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No response generated from AI');
      }

      return response;
    } catch (error) {
      console.error('AI conversation error:', error);
      
      // Fallback responses based on context
      if (context.photo) {
        return `I can see you've shared a lovely photo with me, ${context.userName}. What does this picture remind you of? I'd love to hear about it.`;
      } else {
        return `I'm here to chat with you, ${context.userName}. Is there a special memory or photo you'd like to talk about today?`;
      }
    }
  }

  async generateConversationStarters(context: ConversationContext): Promise<string[]> {
    const { userName, photo } = context;
    
    if (photo) {
      return [
        `Tell me about this photo, ${userName}. What do you see?`,
        `This looks like a special moment. How does it make you feel?`,
        `I'd love to hear the story behind this picture.`,
        `What memories does this photo bring back for you?`
      ];
    } else {
      return [
        `Hello ${userName}! Would you like to share a favorite photo with me?`,
        `What's a happy memory you'd like to talk about today?`,
        `Tell me about someone special in your life.`,
        `What's something that always makes you smile?`
      ];
    }
  }

  async analyzePhotoForContext(photo: Photo): Promise<Partial<Photo['metadata']>> {
    // This would ideally use GPT-4 Vision API to analyze the photo
    // For now, return basic analysis based on filename and existing metadata
    const analysis: Partial<Photo['metadata']> = {};
    
    const filename = photo.filename.toLowerCase();
    
    // Basic keyword detection
    if (filename.includes('wedding')) analysis.event = 'Wedding';
    if (filename.includes('birthday')) analysis.event = 'Birthday';
    if (filename.includes('christmas') || filename.includes('holiday')) analysis.event = 'Holiday';
    if (filename.includes('vacation') || filename.includes('trip')) analysis.event = 'Vacation';
    if (filename.includes('family')) analysis.people = ['Family'];
    
    return analysis;
  }

  async generatePhotoDescription(photo: Photo): Promise<string> {
    // This would use GPT-4 Vision API in a full implementation
    // For now, generate a basic description based on available metadata
    let description = `A photo titled "${photo.filename}"`;
    
    if (photo.metadata?.event) {
      description += ` from a ${photo.metadata.event}`;
    }
    
    if (photo.metadata?.people && photo.metadata.people.length > 0) {
      description += ` featuring ${photo.metadata.people.join(' and ')}`;
    }
    
    if (photo.metadata?.location) {
      description += ` taken at ${photo.metadata.location}`;
    }
    
    description += '.';
    
    return description;
  }
}

export const aiService = new AIConversationService();
