import { createClient } from '@supabase/supabase-js';
import type { Database } from '../types/database';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// Auth helpers
export const auth = {
  signUp: async (email: string, password: string, userData: any) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData
      }
    });
    return { data, error };
  },

  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    return { data, error };
  },

  signOut: async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  },

  getCurrentUser: () => {
    return supabase.auth.getUser();
  },

  onAuthStateChange: (callback: (event: string, session: any) => void) => {
    return supabase.auth.onAuthStateChange(callback);
  }
};

// Database helpers
export const db = {
  // Users
  getUser: async (id: string) => {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();
    return { data, error };
  },

  updateUser: async (id: string, updates: any) => {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    return { data, error };
  },

  // Photos
  uploadPhoto: async (file: File, userId: string) => {
    const fileExt = file.name.split('.').pop();
    const fileName = `${userId}/${Date.now()}.${fileExt}`;
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('photos')
      .upload(fileName, file);

    if (uploadError) return { data: null, error: uploadError };

    const { data: urlData } = supabase.storage
      .from('photos')
      .getPublicUrl(fileName);

    const { data, error } = await supabase
      .from('photos')
      .insert({
        user_id: userId,
        filename: file.name,
        url: urlData.publicUrl,
        file_size: file.size,
        mime_type: file.type
      })
      .select()
      .single();

    return { data, error };
  },

  getPhotos: async (userId: string) => {
    const { data, error } = await supabase
      .from('photos')
      .select('*')
      .eq('user_id', userId)
      .order('uploaded_at', { ascending: false });
    return { data, error };
  },

  // Conversations
  createConversation: async (userId: string, photoId?: string) => {
    const { data, error } = await supabase
      .from('conversations')
      .insert({
        user_id: userId,
        photo_id: photoId
      })
      .select()
      .single();
    return { data, error };
  },

  getConversations: async (userId: string) => {
    const { data, error } = await supabase
      .from('conversations')
      .select(`
        *,
        photos (*)
      `)
      .eq('user_id', userId)
      .order('last_active_at', { ascending: false });
    return { data, error };
  },

  // Messages
  addMessage: async (conversationId: string, type: 'user' | 'assistant', content: string, audioUrl?: string) => {
    const { data, error } = await supabase
      .from('messages')
      .insert({
        conversation_id: conversationId,
        type,
        content,
        audio_url: audioUrl
      })
      .select()
      .single();

    // Update conversation last_active_at
    if (!error) {
      await supabase
        .from('conversations')
        .update({ 
          last_active_at: new Date().toISOString(),
          message_count: supabase.rpc('increment_message_count', { conversation_id: conversationId })
        })
        .eq('id', conversationId);
    }

    return { data, error };
  },

  getMessages: async (conversationId: string) => {
    const { data, error } = await supabase
      .from('messages')
      .select('*')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true });
    return { data, error };
  },

  // Medications
  addMedication: async (medication: any) => {
    const { data, error } = await supabase
      .from('medications')
      .insert(medication)
      .select()
      .single();
    return { data, error };
  },

  getMedications: async (userId: string) => {
    const { data, error } = await supabase
      .from('medications')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });
    return { data, error };
  },

  // Medication Reminders
  addReminder: async (reminder: any) => {
    const { data, error } = await supabase
      .from('medication_reminders')
      .insert(reminder)
      .select()
      .single();
    return { data, error };
  },

  updateReminder: async (id: string, updates: any) => {
    const { data, error } = await supabase
      .from('medication_reminders')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    return { data, error };
  },

  getReminders: async (userId: string, date?: string) => {
    let query = supabase
      .from('medication_reminders')
      .select(`
        *,
        medications (*)
      `)
      .eq('user_id', userId);

    if (date) {
      query = query.gte('scheduled_time', `${date}T00:00:00`)
                   .lt('scheduled_time', `${date}T23:59:59`);
    }

    const { data, error } = await query.order('scheduled_time', { ascending: true });
    return { data, error };
  }
};
