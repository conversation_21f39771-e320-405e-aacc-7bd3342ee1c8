import { supabase } from '../lib/supabase';
import type { 
  UserProfile, 
  PersonalInfo,
  ContactInfo,
  UserPreferences,
  MedicalInfo,
  UserRelationship,
  EmergencyContact,
  PrivacySettings,
  NotificationSettings,
  AccessibilitySettings,
  ProfileUpdateRequest,
  ProfileActivity,
  DEFAULT_USER_PREFERENCES,
  DEFAULT_PRIVACY_SETTINGS,
  DEFAULT_NOTIFICATION_SETTINGS,
  DEFAULT_ACCESSIBILITY_SETTINGS
} from '../types/userProfile';
import { v4 as uuidv4 } from 'uuid';

class UserProfileService {
  /**
   * Create a new user profile
   */
  async createProfile(
    userId: string,
    tenantId: string,
    initialData: Partial<UserProfile>
  ): Promise<UserProfile> {
    try {
      const now = new Date();
      
      const profile: Omit<UserProfile, 'id'> = {
        userId,
        tenantId,
        personalInfo: {
          firstName: '',
          lastName: '',
          ...initialData.personalInfo
        },
        contactInfo: {
          primaryEmail: '',
          preferredContactMethod: 'email',
          timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          language: 'en',
          ...initialData.contactInfo
        },
        preferences: { ...DEFAULT_USER_PREFERENCES, ...initialData.preferences },
        medicalInfo: initialData.medicalInfo,
        relationships: initialData.relationships || [],
        emergencyContacts: initialData.emergencyContacts || [],
        privacySettings: { ...DEFAULT_PRIVACY_SETTINGS, ...initialData.privacySettings },
        notificationSettings: { ...DEFAULT_NOTIFICATION_SETTINGS, ...initialData.notificationSettings },
        accessibilitySettings: { ...DEFAULT_ACCESSIBILITY_SETTINGS, ...initialData.accessibilitySettings },
        profileCompleteness: this.calculateCompleteness(initialData),
        lastUpdated: now,
        createdAt: now,
        isActive: true
      };

      // Check if user_profiles table exists
      const { data: tableCheck, error: tableError } = await supabase
        .from('user_profiles')
        .select('id')
        .limit(1);

      // If tables don't exist, return mock profile
      if (tableError && tableError.code === 'PGRST200') {
        console.log('User profile tables not yet created, creating mock profile');
        return {
          id: uuidv4(),
          ...profile
        };
      }

      const { data, error } = await supabase
        .from('user_profiles')
        .insert([{
          user_id: profile.userId,
          tenant_id: profile.tenantId,
          personal_info: profile.personalInfo,
          contact_info: profile.contactInfo,
          preferences: profile.preferences,
          medical_info: profile.medicalInfo,
          relationships: profile.relationships,
          emergency_contacts: profile.emergencyContacts,
          privacy_settings: profile.privacySettings,
          notification_settings: profile.notificationSettings,
          accessibility_settings: profile.accessibilitySettings,
          profile_completeness: profile.profileCompleteness,
          last_updated: profile.lastUpdated.toISOString(),
          created_at: profile.createdAt.toISOString(),
          is_active: profile.isActive
        }])
        .select()
        .single();

      if (error) throw error;

      // Log activity
      await this.logActivity(userId, 'created', undefined, { profileId: data.id });

      return this.mapDatabaseToProfile(data);
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }
  }

  /**
   * Get user profile by user ID
   */
  async getProfile(userId: string): Promise<UserProfile | null> {
    try {
      // Check if user_profiles table exists
      const { data: tableCheck, error: tableError } = await supabase
        .from('user_profiles')
        .select('id')
        .limit(1);

      // If tables don't exist, return null
      if (tableError && tableError.code === 'PGRST200') {
        console.log('User profile tables not yet created');
        return null;
      }

      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null; // Not found
        throw error;
      }

      return this.mapDatabaseToProfile(data);
    } catch (error) {
      console.error('Error getting user profile:', error);
      return null;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(
    userId: string,
    updates: Partial<UserProfile>,
    updatedBy: string
  ): Promise<UserProfile> {
    try {
      const currentProfile = await this.getProfile(userId);
      if (!currentProfile) {
        throw new Error('Profile not found');
      }

      const updatedProfile = {
        ...currentProfile,
        ...updates,
        lastUpdated: new Date(),
        profileCompleteness: this.calculateCompleteness({ ...currentProfile, ...updates })
      };

      const { data, error } = await supabase
        .from('user_profiles')
        .update({
          personal_info: updatedProfile.personalInfo,
          contact_info: updatedProfile.contactInfo,
          preferences: updatedProfile.preferences,
          medical_info: updatedProfile.medicalInfo,
          relationships: updatedProfile.relationships,
          emergency_contacts: updatedProfile.emergencyContacts,
          privacy_settings: updatedProfile.privacySettings,
          notification_settings: updatedProfile.notificationSettings,
          accessibility_settings: updatedProfile.accessibilitySettings,
          profile_completeness: updatedProfile.profileCompleteness,
          last_updated: updatedProfile.lastUpdated.toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      // Log activity
      await this.logActivity(userId, 'updated', Object.keys(updates).join(','), { 
        updatedBy,
        changes: updates 
      });

      return this.mapDatabaseToProfile(data);
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  /**
   * Add relationship between users
   */
  async addRelationship(
    userId: string,
    relatedUserId: string,
    relationshipData: Omit<UserRelationship, 'id' | 'relatedUserId' | 'establishedDate' | 'isActive'>
  ): Promise<UserRelationship> {
    try {
      const relationship: UserRelationship = {
        id: uuidv4(),
        relatedUserId,
        establishedDate: new Date(),
        isActive: true,
        ...relationshipData
      };

      const profile = await this.getProfile(userId);
      if (!profile) {
        throw new Error('Profile not found');
      }

      const updatedRelationships = [...profile.relationships, relationship];
      
      await this.updateProfile(userId, { relationships: updatedRelationships }, userId);

      // Log activity
      await this.logActivity(userId, 'updated', 'relationships', { 
        action: 'added_relationship',
        relatedUserId,
        relationshipType: relationshipData.relationshipType
      });

      return relationship;
    } catch (error) {
      console.error('Error adding relationship:', error);
      throw error;
    }
  }

  /**
   * Update relationship
   */
  async updateRelationship(
    userId: string,
    relationshipId: string,
    updates: Partial<UserRelationship>
  ): Promise<UserRelationship> {
    try {
      const profile = await this.getProfile(userId);
      if (!profile) {
        throw new Error('Profile not found');
      }

      const relationshipIndex = profile.relationships.findIndex(r => r.id === relationshipId);
      if (relationshipIndex === -1) {
        throw new Error('Relationship not found');
      }

      const updatedRelationship = { ...profile.relationships[relationshipIndex], ...updates };
      const updatedRelationships = [...profile.relationships];
      updatedRelationships[relationshipIndex] = updatedRelationship;

      await this.updateProfile(userId, { relationships: updatedRelationships }, userId);

      // Log activity
      await this.logActivity(userId, 'updated', 'relationships', { 
        action: 'updated_relationship',
        relationshipId,
        changes: updates
      });

      return updatedRelationship;
    } catch (error) {
      console.error('Error updating relationship:', error);
      throw error;
    }
  }

  /**
   * Remove relationship
   */
  async removeRelationship(userId: string, relationshipId: string): Promise<void> {
    try {
      const profile = await this.getProfile(userId);
      if (!profile) {
        throw new Error('Profile not found');
      }

      const updatedRelationships = profile.relationships.filter(r => r.id !== relationshipId);
      
      await this.updateProfile(userId, { relationships: updatedRelationships }, userId);

      // Log activity
      await this.logActivity(userId, 'updated', 'relationships', { 
        action: 'removed_relationship',
        relationshipId
      });
    } catch (error) {
      console.error('Error removing relationship:', error);
      throw error;
    }
  }

  /**
   * Get user relationships
   */
  async getUserRelationships(userId: string): Promise<UserRelationship[]> {
    try {
      const profile = await this.getProfile(userId);
      return profile?.relationships || [];
    } catch (error) {
      console.error('Error getting user relationships:', error);
      return [];
    }
  }

  /**
   * Search profiles by criteria
   */
  async searchProfiles(criteria: {
    tenantId?: string;
    name?: string;
    email?: string;
    role?: string;
    limit?: number;
  }): Promise<UserProfile[]> {
    try {
      // Check if user_profiles table exists
      const { data: tableCheck, error: tableError } = await supabase
        .from('user_profiles')
        .select('id')
        .limit(1);

      // If tables don't exist, return empty array
      if (tableError && tableError.code === 'PGRST200') {
        console.log('User profile tables not yet created');
        return [];
      }

      let query = supabase
        .from('user_profiles')
        .select('*')
        .eq('is_active', true);

      if (criteria.tenantId) {
        query = query.eq('tenant_id', criteria.tenantId);
      }

      if (criteria.limit) {
        query = query.limit(criteria.limit);
      }

      const { data, error } = await query;

      if (error) throw error;

      let profiles = (data || []).map(this.mapDatabaseToProfile);

      // Apply client-side filtering for complex criteria
      if (criteria.name) {
        const searchTerm = criteria.name.toLowerCase();
        profiles = profiles.filter(profile => 
          profile.personalInfo.firstName.toLowerCase().includes(searchTerm) ||
          profile.personalInfo.lastName.toLowerCase().includes(searchTerm) ||
          profile.personalInfo.displayName?.toLowerCase().includes(searchTerm)
        );
      }

      if (criteria.email) {
        const searchTerm = criteria.email.toLowerCase();
        profiles = profiles.filter(profile => 
          profile.contactInfo.primaryEmail.toLowerCase().includes(searchTerm) ||
          profile.contactInfo.secondaryEmail?.toLowerCase().includes(searchTerm)
        );
      }

      return profiles;
    } catch (error) {
      console.error('Error searching profiles:', error);
      return [];
    }
  }

  /**
   * Calculate profile completeness percentage
   */
  private calculateCompleteness(profile: Partial<UserProfile>): number {
    let score = 0;
    let maxScore = 0;

    // Personal info (30 points)
    maxScore += 30;
    if (profile.personalInfo?.firstName) score += 5;
    if (profile.personalInfo?.lastName) score += 5;
    if (profile.personalInfo?.dateOfBirth) score += 5;
    if (profile.personalInfo?.profilePicture) score += 5;
    if (profile.personalInfo?.bio) score += 5;
    if (profile.personalInfo?.hobbies?.length) score += 5;

    // Contact info (20 points)
    maxScore += 20;
    if (profile.contactInfo?.primaryEmail) score += 10;
    if (profile.contactInfo?.primaryPhone) score += 5;
    if (profile.contactInfo?.address) score += 5;

    // Emergency contacts (20 points)
    maxScore += 20;
    if (profile.emergencyContacts?.length) score += 20;

    // Relationships (15 points)
    maxScore += 15;
    if (profile.relationships?.length) score += 15;

    // Medical info (10 points)
    maxScore += 10;
    if (profile.medicalInfo?.primaryPhysician) score += 5;
    if (profile.medicalInfo?.medicalConditions?.length) score += 5;

    // Settings configured (5 points)
    maxScore += 5;
    if (profile.preferences) score += 5;

    return Math.round((score / maxScore) * 100);
  }

  /**
   * Map database record to UserProfile
   */
  private mapDatabaseToProfile(data: any): UserProfile {
    return {
      id: data.id,
      userId: data.user_id,
      tenantId: data.tenant_id,
      personalInfo: data.personal_info,
      contactInfo: data.contact_info,
      preferences: data.preferences,
      medicalInfo: data.medical_info,
      relationships: data.relationships || [],
      emergencyContacts: data.emergency_contacts || [],
      privacySettings: data.privacy_settings,
      notificationSettings: data.notification_settings,
      accessibilitySettings: data.accessibility_settings,
      profileCompleteness: data.profile_completeness,
      lastUpdated: new Date(data.last_updated),
      createdAt: new Date(data.created_at),
      isActive: data.is_active
    };
  }

  /**
   * Log profile activity
   */
  private async logActivity(
    userId: string,
    action: string,
    section?: string,
    details?: Record<string, any>
  ): Promise<void> {
    try {
      // Check if profile_activities table exists
      const { data: tableCheck, error: tableError } = await supabase
        .from('profile_activities')
        .select('id')
        .limit(1);

      // If tables don't exist, skip logging
      if (tableError && tableError.code === 'PGRST200') {
        return;
      }

      await supabase
        .from('profile_activities')
        .insert([{
          user_id: userId,
          action,
          section,
          details,
          performed_by: userId,
          timestamp: new Date().toISOString()
        }]);
    } catch (error) {
      console.error('Error logging profile activity:', error);
      // Don't throw - activity logging shouldn't break main functionality
    }
  }
}

export const userProfileService = new UserProfileService();
