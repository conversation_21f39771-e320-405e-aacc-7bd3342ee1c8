import { supabase } from '../lib/supabase';
import type { Photo, Medication, MedicationReminder, CaregiverInsight } from '../types';
import { startOfDay, subDays, format } from 'date-fns';

export interface InsightGenerationContext {
  userId: string;
  photos: Photo[];
  medications: Medication[];
  reminders: MedicationReminder[];
  timeRange: '7d' | '30d' | '90d';
}

export interface GeneratedInsight {
  type: 'engagement' | 'medication' | 'mood' | 'activity';
  title: string;
  description: string;
  value: string | number;
  trend?: 'up' | 'down' | 'stable';
  severity?: 'low' | 'medium' | 'high';
  metadata?: Record<string, any>;
}

class InsightsService {
  /**
   * Generate comprehensive insights for a patient
   */
  async generateInsights(context: InsightGenerationContext): Promise<GeneratedInsight[]> {
    const insights: GeneratedInsight[] = [];

    // Generate engagement insights
    const engagementInsights = this.generateEngagementInsights(context);
    insights.push(...engagementInsights);

    // Generate medication insights
    const medicationInsights = this.generateMedicationInsights(context);
    insights.push(...medicationInsights);

    // Generate mood insights
    const moodInsights = this.generateMoodInsights(context);
    insights.push(...moodInsights);

    // Generate activity insights
    const activityInsights = this.generateActivityInsights(context);
    insights.push(...activityInsights);

    return insights;
  }

  /**
   * Generate engagement-related insights
   */
  private generateEngagementInsights(context: InsightGenerationContext): GeneratedInsight[] {
    const { photos, timeRange } = context;
    const insights: GeneratedInsight[] = [];

    const daysBack = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    const startDate = subDays(new Date(), daysBack);
    const recentPhotos = photos.filter(p => p.uploadedAt >= startDate);

    // Photo sharing frequency
    const photosPerDay = recentPhotos.length / daysBack;
    const photoTrend = this.calculateTrend(photos, 'uploadedAt', daysBack);

    if (recentPhotos.length > 0) {
      insights.push({
        type: 'engagement',
        title: 'Photo Sharing Activity',
        description: `Shared ${recentPhotos.length} photos in the last ${daysBack} days`,
        value: `${recentPhotos.length} photos`,
        trend: photoTrend > 0.1 ? 'up' : photoTrend < -0.1 ? 'down' : 'stable',
        severity: recentPhotos.length < 3 ? 'medium' : 'low',
        metadata: {
          photosPerDay: Math.round(photosPerDay * 10) / 10,
          totalPhotos: recentPhotos.length
        }
      });
    }

    // Photo engagement quality
    const photosWithDescriptions = recentPhotos.filter(p => p.description && p.description.length > 10);
    const engagementRate = recentPhotos.length > 0 
      ? Math.round((photosWithDescriptions.length / recentPhotos.length) * 100)
      : 0;

    insights.push({
      type: 'engagement',
      title: 'Photo Discussion Quality',
      description: `${engagementRate}% of photos include detailed discussions`,
      value: `${engagementRate}%`,
      trend: 'stable',
      severity: engagementRate < 50 ? 'medium' : 'low',
      metadata: {
        discussedPhotos: photosWithDescriptions.length,
        totalPhotos: recentPhotos.length
      }
    });

    // Social connections in photos
    const peopleInPhotos = new Set(
      recentPhotos.flatMap(p => p.metadata?.people || [])
    );

    if (peopleInPhotos.size > 0) {
      insights.push({
        type: 'engagement',
        title: 'Social Connections',
        description: `Photos feature ${peopleInPhotos.size} different people`,
        value: `${peopleInPhotos.size} people`,
        trend: 'stable',
        severity: 'low',
        metadata: {
          uniquePeople: Array.from(peopleInPhotos)
        }
      });
    }

    return insights;
  }

  /**
   * Generate medication-related insights
   */
  private generateMedicationInsights(context: InsightGenerationContext): GeneratedInsight[] {
    const { medications, reminders, timeRange } = context;
    const insights: GeneratedInsight[] = [];

    const daysBack = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    const startDate = subDays(new Date(), daysBack);
    const recentReminders = reminders.filter(r => r.scheduledTime >= startDate);

    if (recentReminders.length === 0) {
      return insights;
    }

    // Overall adherence
    const takenReminders = recentReminders.filter(r => r.status === 'taken');
    const adherenceRate = Math.round((takenReminders.length / recentReminders.length) * 100);
    const adherenceTrend = this.calculateMedicationTrend(reminders, daysBack);

    insights.push({
      type: 'medication',
      title: 'Medication Adherence',
      description: `${adherenceRate}% adherence rate over ${daysBack} days`,
      value: `${adherenceRate}%`,
      trend: adherenceTrend > 5 ? 'up' : adherenceTrend < -5 ? 'down' : 'stable',
      severity: adherenceRate < 70 ? 'high' : adherenceRate < 85 ? 'medium' : 'low',
      metadata: {
        takenCount: takenReminders.length,
        totalCount: recentReminders.length,
        missedCount: recentReminders.filter(r => r.status === 'missed').length
      }
    });

    // Missed medications pattern
    const missedReminders = recentReminders.filter(r => r.status === 'missed');
    if (missedReminders.length > 0) {
      const missedToday = missedReminders.filter(r => 
        startOfDay(r.scheduledTime).getTime() === startOfDay(new Date()).getTime()
      );

      if (missedToday.length > 0) {
        insights.push({
          type: 'medication',
          title: 'Missed Medications Today',
          description: `${missedToday.length} medication${missedToday.length > 1 ? 's' : ''} missed today`,
          value: missedToday.length,
          severity: 'high',
          metadata: {
            missedMedications: missedToday.map(r => r.medicationId)
          }
        });
      }
    }

    // Consistency analysis
    const consistencyScore = this.calculateMedicationConsistency(recentReminders);
    if (consistencyScore < 70) {
      insights.push({
        type: 'medication',
        title: 'Medication Timing Consistency',
        description: 'Medication timing varies significantly',
        value: `${consistencyScore}% consistent`,
        severity: 'medium',
        metadata: {
          consistencyScore
        }
      });
    }

    return insights;
  }

  /**
   * Generate mood-related insights
   */
  private generateMoodInsights(context: InsightGenerationContext): GeneratedInsight[] {
    const { photos } = context;
    const insights: GeneratedInsight[] = [];

    // Analyze emotional content in photo descriptions
    const emotionalWords = {
      positive: ['happy', 'joy', 'love', 'wonderful', 'beautiful', 'amazing', 'great', 'fun', 'smile', 'laugh'],
      negative: ['sad', 'difficult', 'hard', 'miss', 'gone', 'lost', 'worry', 'upset', 'cry'],
      neutral: ['remember', 'think', 'was', 'were', 'time', 'day', 'place']
    };

    let positiveCount = 0;
    let negativeCount = 0;
    let neutralCount = 0;

    photos.forEach(photo => {
      if (photo.description) {
        const text = photo.description.toLowerCase();
        const hasPositive = emotionalWords.positive.some(word => text.includes(word));
        const hasNegative = emotionalWords.negative.some(word => text.includes(word));
        
        if (hasPositive) positiveCount++;
        else if (hasNegative) negativeCount++;
        else neutralCount++;
      }
    });

    const totalEmotionalPhotos = positiveCount + negativeCount + neutralCount;
    
    if (totalEmotionalPhotos > 0) {
      const positivePercentage = Math.round((positiveCount / totalEmotionalPhotos) * 100);
      
      let moodDescription = '';
      let severity: 'low' | 'medium' | 'high' = 'low';
      
      if (positivePercentage >= 70) {
        moodDescription = 'Predominantly positive emotional tone in conversations';
      } else if (positivePercentage >= 40) {
        moodDescription = 'Balanced emotional tone with mixed sentiments';
      } else {
        moodDescription = 'Some challenging emotions expressed in conversations';
        severity = 'medium';
      }

      insights.push({
        type: 'mood',
        title: 'Emotional Tone Analysis',
        description: moodDescription,
        value: `${positivePercentage}% positive`,
        severity,
        metadata: {
          positiveCount,
          negativeCount,
          neutralCount,
          totalAnalyzed: totalEmotionalPhotos
        }
      });
    }

    return insights;
  }

  /**
   * Generate activity-related insights
   */
  private generateActivityInsights(context: InsightGenerationContext): GeneratedInsight[] {
    const { photos, reminders, timeRange } = context;
    const insights: GeneratedInsight[] = [];

    const daysBack = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    const startDate = subDays(new Date(), daysBack);
    
    const recentPhotos = photos.filter(p => p.uploadedAt >= startDate);
    const recentReminders = reminders.filter(r => r.scheduledTime >= startDate);

    // Overall activity level
    const photoActivity = recentPhotos.length;
    const medicationActivity = recentReminders.filter(r => r.status === 'taken').length;
    const totalActivity = photoActivity + medicationActivity;

    const activityPerDay = totalActivity / daysBack;
    
    insights.push({
      type: 'activity',
      title: 'Daily Activity Level',
      description: `Average ${Math.round(activityPerDay * 10) / 10} activities per day`,
      value: `${totalActivity} activities`,
      trend: 'stable',
      severity: activityPerDay < 2 ? 'medium' : 'low',
      metadata: {
        photoActivity,
        medicationActivity,
        activityPerDay: Math.round(activityPerDay * 10) / 10
      }
    });

    // Activity patterns
    const weekdayActivity = this.analyzeWeekdayPatterns(recentPhotos, recentReminders);
    if (weekdayActivity.variance > 0.5) {
      insights.push({
        type: 'activity',
        title: 'Activity Pattern Variation',
        description: 'Activity levels vary significantly by day of week',
        value: 'Variable pattern',
        severity: 'low',
        metadata: weekdayActivity
      });
    }

    return insights;
  }

  /**
   * Calculate trend for a given metric
   */
  private calculateTrend(items: any[], dateField: string, daysBack: number): number {
    const midPoint = Math.floor(daysBack / 2);
    const midDate = subDays(new Date(), midPoint);
    
    const firstHalf = items.filter(item => item[dateField] < midDate);
    const secondHalf = items.filter(item => item[dateField] >= midDate);
    
    const firstHalfRate = firstHalf.length / midPoint;
    const secondHalfRate = secondHalf.length / midPoint;
    
    return secondHalfRate - firstHalfRate;
  }

  /**
   * Calculate medication adherence trend
   */
  private calculateMedicationTrend(reminders: MedicationReminder[], daysBack: number): number {
    const midPoint = Math.floor(daysBack / 2);
    const midDate = subDays(new Date(), midPoint);
    
    const firstHalf = reminders.filter(r => r.scheduledTime < midDate);
    const secondHalf = reminders.filter(r => r.scheduledTime >= midDate);
    
    const firstHalfAdherence = firstHalf.length > 0 
      ? (firstHalf.filter(r => r.status === 'taken').length / firstHalf.length) * 100
      : 0;
    
    const secondHalfAdherence = secondHalf.length > 0
      ? (secondHalf.filter(r => r.status === 'taken').length / secondHalf.length) * 100
      : 0;
    
    return secondHalfAdherence - firstHalfAdherence;
  }

  /**
   * Calculate medication timing consistency
   */
  private calculateMedicationConsistency(reminders: MedicationReminder[]): number {
    // Group by medication and calculate timing variance
    const medicationGroups = reminders.reduce((groups, reminder) => {
      if (!groups[reminder.medicationId]) {
        groups[reminder.medicationId] = [];
      }
      groups[reminder.medicationId].push(reminder);
      return groups;
    }, {} as Record<string, MedicationReminder[]>);

    let totalConsistency = 0;
    let medicationCount = 0;

    Object.values(medicationGroups).forEach(medReminders => {
      if (medReminders.length > 1) {
        const takenReminders = medReminders.filter(r => r.status === 'taken');
        if (takenReminders.length > 1) {
          // Calculate time variance (simplified)
          const times = takenReminders.map(r => r.scheduledTime.getHours() * 60 + r.scheduledTime.getMinutes());
          const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
          const variance = times.reduce((sum, time) => sum + Math.pow(time - avgTime, 2), 0) / times.length;
          const stdDev = Math.sqrt(variance);
          
          // Convert to consistency score (lower variance = higher consistency)
          const consistency = Math.max(0, 100 - (stdDev / 60) * 100); // Normalize by hour
          totalConsistency += consistency;
          medicationCount++;
        }
      }
    });

    return medicationCount > 0 ? Math.round(totalConsistency / medicationCount) : 100;
  }

  /**
   * Analyze weekday activity patterns
   */
  private analyzeWeekdayPatterns(photos: Photo[], reminders: MedicationReminder[]) {
    const weekdayActivity = Array(7).fill(0);
    
    photos.forEach(photo => {
      const dayOfWeek = photo.uploadedAt.getDay();
      weekdayActivity[dayOfWeek]++;
    });
    
    reminders.filter(r => r.status === 'taken').forEach(reminder => {
      const dayOfWeek = reminder.scheduledTime.getDay();
      weekdayActivity[dayOfWeek]++;
    });

    const avgActivity = weekdayActivity.reduce((sum, count) => sum + count, 0) / 7;
    const variance = weekdayActivity.reduce((sum, count) => sum + Math.pow(count - avgActivity, 2), 0) / 7;

    return {
      weekdayActivity,
      avgActivity: Math.round(avgActivity * 10) / 10,
      variance: Math.round(variance * 10) / 10
    };
  }

  /**
   * Save insights to database
   */
  async saveInsights(insights: GeneratedInsight[], patientId: string, tenantId: string): Promise<void> {
    try {
      const insightsToSave = insights.map(insight => ({
        patient_id: patientId,
        tenant_id: tenantId,
        type: insight.type,
        title: insight.title,
        description: insight.description,
        value_text: typeof insight.value === 'string' ? insight.value : null,
        value_numeric: typeof insight.value === 'number' ? insight.value : null,
        trend: insight.trend || null,
        severity: insight.severity || 'low',
        metadata: insight.metadata || {},
        date: format(new Date(), 'yyyy-MM-dd')
      }));

      const { error } = await supabase
        .from('caregiver_insights')
        .insert(insightsToSave);

      if (error) throw error;
    } catch (error) {
      console.error('Error saving insights:', error);
      throw error;
    }
  }

  /**
   * Load insights from database
   */
  async loadInsights(patientId: string, timeRange: '7d' | '30d' | '90d' = '30d'): Promise<CaregiverInsight[]> {
    try {
      const daysBack = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
      const startDate = format(subDays(new Date(), daysBack), 'yyyy-MM-dd');

      const { data, error } = await supabase
        .from('caregiver_insights')
        .select('*')
        .eq('patient_id', patientId)
        .gte('date', startDate)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return (data || []).map(insight => ({
        id: insight.id,
        type: insight.type as 'engagement' | 'medication' | 'mood' | 'activity',
        title: insight.title,
        description: insight.description,
        value: insight.value_text || insight.value_numeric || '',
        trend: insight.trend as 'up' | 'down' | 'stable' | undefined,
        date: new Date(insight.date),
        severity: insight.severity as 'low' | 'medium' | 'high' | undefined
      }));
    } catch (error) {
      console.error('Error loading insights:', error);
      return [];
    }
  }
}

export const insightsService = new InsightsService();
