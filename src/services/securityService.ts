import { supabase } from '../lib/supabase';
import type { 
  AuditLog, 
  SecurityAlert, 
  PrivacyConsent, 
  DataSubjectRequest,
  SecurityIncident,
  AuditAction,
  AuditCategory,
  SecurityAlertType,
  ConsentType,
  DataSubjectRequestType
} from '../types/security';
import { v4 as uuidv4 } from 'uuid';

class SecurityService {
  private encryptionKey: string | null = null;

  /**
   * Initialize security service
   */
  async initialize() {
    // Initialize encryption key from secure storage
    this.encryptionKey = await this.getOrCreateEncryptionKey();
  }

  /**
   * Encrypt sensitive data
   */
  async encryptData(data: string): Promise<string> {
    if (!this.encryptionKey) {
      await this.initialize();
    }

    try {
      // In a real implementation, use Web Crypto API
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);
      
      // For demo purposes, we'll use base64 encoding
      // In production, use proper AES-256-GCM encryption
      return btoa(data);
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt sensitive data
   */
  async decryptData(encryptedData: string): Promise<string> {
    if (!this.encryptionKey) {
      await this.initialize();
    }

    try {
      // For demo purposes, we'll use base64 decoding
      // In production, use proper AES-256-GCM decryption
      return atob(encryptedData);
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Log audit event
   */
  async logAuditEvent(
    userId: string,
    action: AuditAction,
    resource: string,
    details: Record<string, any> = {},
    resourceId?: string,
    tenantId?: string
  ): Promise<void> {
    try {
      const auditLog: Omit<AuditLog, 'id'> = {
        userId,
        tenantId,
        action,
        resource,
        resourceId,
        details,
        ipAddress: await this.getClientIP(),
        userAgent: navigator.userAgent,
        location: await this.getClientLocation(),
        timestamp: new Date(),
        severity: this.getActionSeverity(action),
        category: this.getActionCategory(action),
        outcome: 'success'
      };

      // Check if audit_logs table exists
      const { data: tableCheck, error: tableError } = await supabase
        .from('audit_logs')
        .select('id')
        .limit(1);

      // If tables don't exist, log to console for now
      if (tableError && tableError.code === 'PGRST200') {
        console.log('Audit Log (table not created):', auditLog);
        return;
      }

      const { error } = await supabase
        .from('audit_logs')
        .insert([{
          user_id: auditLog.userId,
          tenant_id: auditLog.tenantId,
          action: auditLog.action,
          resource: auditLog.resource,
          resource_id: auditLog.resourceId,
          details: auditLog.details,
          ip_address: auditLog.ipAddress,
          user_agent: auditLog.userAgent,
          location: auditLog.location,
          timestamp: auditLog.timestamp.toISOString(),
          severity: auditLog.severity,
          category: auditLog.category,
          outcome: auditLog.outcome
        }]);

      if (error) {
        console.error('Failed to log audit event:', error);
      }

      // Check for suspicious activity
      await this.checkSuspiciousActivity(userId, action, details);
    } catch (error) {
      console.error('Audit logging failed:', error);
    }
  }

  /**
   * Create security alert
   */
  async createSecurityAlert(
    type: SecurityAlertType,
    severity: 'low' | 'medium' | 'high' | 'critical',
    title: string,
    description: string,
    details: Record<string, any> = {},
    userId?: string,
    tenantId?: string
  ): Promise<void> {
    try {
      const alert: Omit<SecurityAlert, 'id'> = {
        type,
        severity,
        title,
        description,
        userId,
        tenantId,
        ipAddress: await this.getClientIP(),
        details,
        timestamp: new Date(),
        status: 'active'
      };

      // Check if security_alerts table exists
      const { data: tableCheck, error: tableError } = await supabase
        .from('security_alerts')
        .select('id')
        .limit(1);

      // If tables don't exist, log to console for now
      if (tableError && tableError.code === 'PGRST200') {
        console.warn('Security Alert (table not created):', alert);
        return;
      }

      const { error } = await supabase
        .from('security_alerts')
        .insert([{
          type: alert.type,
          severity: alert.severity,
          title: alert.title,
          description: alert.description,
          user_id: alert.userId,
          tenant_id: alert.tenantId,
          ip_address: alert.ipAddress,
          details: alert.details,
          timestamp: alert.timestamp.toISOString(),
          status: alert.status
        }]);

      if (error) {
        console.error('Failed to create security alert:', error);
      }

      // Send immediate notification for critical alerts
      if (severity === 'critical') {
        await this.sendCriticalAlertNotification(alert);
      }
    } catch (error) {
      console.error('Security alert creation failed:', error);
    }
  }

  /**
   * Record privacy consent
   */
  async recordConsent(
    userId: string,
    consentType: ConsentType,
    purpose: string,
    granted: boolean,
    tenantId?: string
  ): Promise<void> {
    try {
      const consent: Omit<PrivacyConsent, 'id'> = {
        userId,
        tenantId,
        consentType,
        purpose,
        dataCategories: this.getDataCategoriesForConsent(consentType),
        granted,
        grantedAt: granted ? new Date() : undefined,
        withdrawnAt: !granted ? new Date() : undefined,
        version: '1.0',
        ipAddress: await this.getClientIP(),
        userAgent: navigator.userAgent,
        evidence: {
          method: 'explicit_opt_in',
          details: {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
          }
        }
      };

      // Check if privacy_consents table exists
      const { data: tableCheck, error: tableError } = await supabase
        .from('privacy_consents')
        .select('id')
        .limit(1);

      // If tables don't exist, store in localStorage for now
      if (tableError && tableError.code === 'PGRST200') {
        const consents = JSON.parse(localStorage.getItem('privacy_consents') || '[]');
        consents.push({ id: uuidv4(), ...consent });
        localStorage.setItem('privacy_consents', JSON.stringify(consents));
        return;
      }

      const { error } = await supabase
        .from('privacy_consents')
        .insert([{
          user_id: consent.userId,
          tenant_id: consent.tenantId,
          consent_type: consent.consentType,
          purpose: consent.purpose,
          data_categories: consent.dataCategories,
          granted: consent.granted,
          granted_at: consent.grantedAt?.toISOString(),
          withdrawn_at: consent.withdrawnAt?.toISOString(),
          version: consent.version,
          ip_address: consent.ipAddress,
          user_agent: consent.userAgent,
          evidence: consent.evidence
        }]);

      if (error) {
        console.error('Failed to record consent:', error);
      }

      // Log audit event
      await this.logAuditEvent(
        userId,
        granted ? 'consent_given' : 'consent_withdrawn',
        'privacy_consent',
        { consentType, purpose },
        undefined,
        tenantId
      );
    } catch (error) {
      console.error('Consent recording failed:', error);
    }
  }

  /**
   * Handle data subject request (GDPR/CCPA)
   */
  async createDataSubjectRequest(
    userId: string,
    requestType: DataSubjectRequestType,
    requestDetails: string,
    tenantId?: string
  ): Promise<string> {
    try {
      const requestId = uuidv4();
      const request: Omit<DataSubjectRequest, 'id'> = {
        userId,
        tenantId,
        requestType,
        status: 'pending',
        submittedAt: new Date(),
        requestDetails,
        verificationMethod: 'email',
        verificationCompleted: false
      };

      // Check if data_subject_requests table exists
      const { data: tableCheck, error: tableError } = await supabase
        .from('data_subject_requests')
        .select('id')
        .limit(1);

      // If tables don't exist, store in localStorage for now
      if (tableError && tableError.code === 'PGRST200') {
        const requests = JSON.parse(localStorage.getItem('data_subject_requests') || '[]');
        requests.push({ id: requestId, ...request });
        localStorage.setItem('data_subject_requests', JSON.stringify(requests));
        console.log('Data Subject Request (stored locally):', { id: requestId, ...request });
        return requestId;
      }

      const { error } = await supabase
        .from('data_subject_requests')
        .insert([{
          id: requestId,
          user_id: request.userId,
          tenant_id: request.tenantId,
          request_type: request.requestType,
          status: request.status,
          submitted_at: request.submittedAt.toISOString(),
          request_details: request.requestDetails,
          verification_method: request.verificationMethod,
          verification_completed: request.verificationCompleted
        }]);

      if (error) {
        console.error('Failed to create data subject request:', error);
        throw error;
      }

      // Log audit event
      await this.logAuditEvent(
        userId,
        'data_exported',
        'data_subject_request',
        { requestType, requestId },
        requestId,
        tenantId
      );

      return requestId;
    } catch (error) {
      console.error('Data subject request creation failed:', error);
      throw error;
    }
  }

  /**
   * Validate data access permissions
   */
  async validateDataAccess(
    userId: string,
    resource: string,
    action: string,
    resourceId?: string,
    tenantId?: string
  ): Promise<boolean> {
    try {
      // Log data access attempt
      await this.logAuditEvent(
        userId,
        'data_viewed',
        resource,
        { action, resourceId },
        resourceId,
        tenantId
      );

      // In a real implementation, check against access control policies
      // For now, return true for basic access
      return true;
    } catch (error) {
      console.error('Data access validation failed:', error);
      return false;
    }
  }

  /**
   * Sanitize data for logging (remove sensitive information)
   */
  sanitizeForLogging(data: any): any {
    const sensitiveFields = [
      'password', 'ssn', 'creditCard', 'bankAccount', 'medicalRecord',
      'token', 'key', 'secret', 'private'
    ];

    if (typeof data !== 'object' || data === null) {
      return data;
    }

    const sanitized = { ...data };
    
    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '[REDACTED]';
      }
    }

    // Recursively sanitize nested objects
    for (const key in sanitized) {
      if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        sanitized[key] = this.sanitizeForLogging(sanitized[key]);
      }
    }

    return sanitized;
  }

  /**
   * Get or create encryption key
   */
  private async getOrCreateEncryptionKey(): Promise<string> {
    // In a real implementation, this would use a secure key management service
    let key = localStorage.getItem('encryption_key');
    if (!key) {
      key = this.generateEncryptionKey();
      localStorage.setItem('encryption_key', key);
    }
    return key;
  }

  /**
   * Generate encryption key
   */
  private generateEncryptionKey(): string {
    // In a real implementation, use Web Crypto API to generate a proper key
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Get client IP address
   */
  private async getClientIP(): Promise<string> {
    try {
      // In a real implementation, use a service to get the real IP
      return '127.0.0.1';
    } catch {
      return 'unknown';
    }
  }

  /**
   * Get client location
   */
  private async getClientLocation(): Promise<any> {
    try {
      // In a real implementation, use IP geolocation service
      return {
        country: 'US',
        region: 'CA',
        city: 'San Francisco'
      };
    } catch {
      return null;
    }
  }

  /**
   * Get action severity
   */
  private getActionSeverity(action: AuditAction): 'low' | 'medium' | 'high' | 'critical' {
    const severityMap: Record<string, 'low' | 'medium' | 'high' | 'critical'> = {
      'login': 'low',
      'logout': 'low',
      'login_failed': 'medium',
      'data_viewed': 'low',
      'data_created': 'medium',
      'data_updated': 'medium',
      'data_deleted': 'high',
      'data_exported': 'high',
      'medical_data_accessed': 'high',
      'unauthorized_access': 'critical',
      'suspicious_activity': 'critical'
    };
    return severityMap[action] || 'medium';
  }

  /**
   * Get action category
   */
  private getActionCategory(action: AuditAction): AuditCategory {
    const categoryMap: Record<string, AuditCategory> = {
      'login': 'authentication',
      'logout': 'authentication',
      'login_failed': 'authentication',
      'data_viewed': 'data_access',
      'data_created': 'data_access',
      'data_updated': 'data_access',
      'data_deleted': 'data_access',
      'data_exported': 'data_access',
      'consent_given': 'privacy',
      'consent_withdrawn': 'privacy',
      'medical_data_accessed': 'medical',
      'unauthorized_access': 'security',
      'suspicious_activity': 'security'
    };
    return categoryMap[action] || 'system';
  }

  /**
   * Check for suspicious activity
   */
  private async checkSuspiciousActivity(
    userId: string,
    action: AuditAction,
    details: Record<string, any>
  ): Promise<void> {
    // Check for multiple failed logins
    if (action === 'login_failed') {
      // In a real implementation, check recent failed login attempts
      await this.createSecurityAlert(
        'multiple_failed_logins',
        'medium',
        'Multiple Failed Login Attempts',
        `User ${userId} has multiple failed login attempts`,
        { userId, action, details },
        userId
      );
    }

    // Check for unusual data access patterns
    if (action === 'data_exported') {
      await this.createSecurityAlert(
        'suspicious_data_access',
        'high',
        'Data Export Activity',
        `User ${userId} exported data`,
        { userId, action, details },
        userId
      );
    }
  }

  /**
   * Send critical alert notification
   */
  private async sendCriticalAlertNotification(alert: Omit<SecurityAlert, 'id'>): Promise<void> {
    // In a real implementation, send email/SMS to security team
    console.error('CRITICAL SECURITY ALERT:', alert);
  }

  /**
   * Get data categories for consent type
   */
  private getDataCategoriesForConsent(consentType: ConsentType): string[] {
    const categoryMap: Record<ConsentType, string[]> = {
      'data_processing': ['personal_info', 'conversation_data'],
      'analytics': ['personal_info', 'device_info'],
      'marketing': ['personal_info'],
      'research': ['personal_info', 'medical_info', 'conversation_data'],
      'third_party_sharing': ['personal_info'],
      'international_transfer': ['personal_info', 'medical_info']
    };
    return categoryMap[consentType] || [];
  }
}

export const securityService = new SecurityService();
