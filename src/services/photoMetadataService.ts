import ExifReader from 'exifreader';
import type { Photo } from '../types';

export interface PhotoMetadata {
  // EXIF Data
  camera?: {
    make?: string;
    model?: string;
    lens?: string;
  };
  settings?: {
    aperture?: string;
    shutterSpeed?: string;
    iso?: number;
    focalLength?: string;
    flash?: boolean;
  };
  // Location Data
  location?: {
    latitude?: number;
    longitude?: number;
    altitude?: number;
    city?: string;
    state?: string;
    country?: string;
    address?: string;
  };
  // Date/Time
  dateTime?: {
    taken?: Date;
    modified?: Date;
    digitized?: Date;
    timezone?: string;
  };
  // Image Properties
  dimensions?: {
    width: number;
    height: number;
    orientation?: number;
  };
  // Auto-generated Tags
  tags?: string[];
  // People Detection (placeholder for future ML integration)
  people?: string[];
  // Scene Analysis
  scene?: {
    indoor?: boolean;
    outdoor?: boolean;
    lighting?: 'natural' | 'artificial' | 'mixed';
    timeOfDay?: 'morning' | 'afternoon' | 'evening' | 'night';
  };
  // File Information
  fileInfo?: {
    size: number;
    type: string;
    lastModified: Date;
  };
}

class PhotoMetadataService {
  private geocodingCache = new Map<string, any>();

  /**
   * Extract comprehensive metadata from a photo file
   */
  async extractMetadata(file: File): Promise<PhotoMetadata> {
    const metadata: PhotoMetadata = {};

    try {
      // Extract EXIF data
      const exifData = await this.extractExifData(file);
      if (exifData) {
        metadata.camera = exifData.camera;
        metadata.settings = exifData.settings;
        metadata.location = exifData.location;
        metadata.dateTime = exifData.dateTime;
        metadata.dimensions = exifData.dimensions;
      }

      // Add file information
      metadata.fileInfo = {
        size: file.size,
        type: file.type,
        lastModified: new Date(file.lastModified)
      };

      // Get image dimensions if not from EXIF
      if (!metadata.dimensions) {
        metadata.dimensions = await this.getImageDimensions(file);
      }

      // Reverse geocode location if available
      if (metadata.location?.latitude && metadata.location?.longitude) {
        const locationDetails = await this.reverseGeocode(
          metadata.location.latitude,
          metadata.location.longitude
        );
        metadata.location = { ...metadata.location, ...locationDetails };
      }

      // Generate automatic tags
      metadata.tags = await this.generateAutoTags(file, metadata);

      // Analyze scene (basic analysis)
      metadata.scene = await this.analyzeScene(file, metadata);

    } catch (error) {
      console.error('Error extracting metadata:', error);
    }

    return metadata;
  }

  /**
   * Extract EXIF data from image file
   */
  private async extractExifData(file: File): Promise<Partial<PhotoMetadata> | null> {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const tags = ExifReader.load(arrayBuffer);

      const metadata: Partial<PhotoMetadata> = {};

      // Camera information
      if (tags.Make || tags.Model) {
        metadata.camera = {
          make: tags.Make?.description,
          model: tags.Model?.description,
          lens: tags.LensModel?.description
        };
      }

      // Camera settings
      metadata.settings = {
        aperture: tags.FNumber?.description,
        shutterSpeed: tags.ExposureTime?.description,
        iso: tags.ISOSpeedRatings?.value?.[0],
        focalLength: tags.FocalLength?.description,
        flash: tags.Flash?.value === 1
      };

      // GPS location
      if (tags.GPSLatitude && tags.GPSLongitude) {
        const lat = this.convertDMSToDD(
          tags.GPSLatitude.value,
          tags.GPSLatitudeRef?.value
        );
        const lng = this.convertDMSToDD(
          tags.GPSLongitude.value,
          tags.GPSLongitudeRef?.value
        );

        metadata.location = {
          latitude: lat,
          longitude: lng,
          altitude: tags.GPSAltitude?.value
        };
      }

      // Date/Time information
      metadata.dateTime = {};
      if (tags.DateTime?.description) {
        metadata.dateTime.taken = this.parseExifDate(tags.DateTime.description);
      }
      if (tags.DateTimeOriginal?.description) {
        metadata.dateTime.taken = this.parseExifDate(tags.DateTimeOriginal.description);
      }
      if (tags.DateTimeDigitized?.description) {
        metadata.dateTime.digitized = this.parseExifDate(tags.DateTimeDigitized.description);
      }

      // Image dimensions
      if (tags.ImageWidth && tags.ImageHeight) {
        metadata.dimensions = {
          width: tags.ImageWidth.value,
          height: tags.ImageHeight.value,
          orientation: tags.Orientation?.value
        };
      }

      return metadata;
    } catch (error) {
      console.error('Error reading EXIF data:', error);
      return null;
    }
  }

  /**
   * Get image dimensions by loading the image
   */
  private async getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const url = URL.createObjectURL(file);

      img.onload = () => {
        URL.revokeObjectURL(url);
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight
        });
      };

      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load image'));
      };

      img.src = url;
    });
  }

  /**
   * Convert DMS (Degrees, Minutes, Seconds) to Decimal Degrees
   */
  private convertDMSToDD(dms: number[], ref: string): number {
    if (!dms || dms.length < 3) return 0;
    
    let dd = dms[0] + dms[1] / 60 + dms[2] / 3600;
    if (ref === 'S' || ref === 'W') dd = dd * -1;
    return dd;
  }

  /**
   * Parse EXIF date string to Date object
   */
  private parseExifDate(dateString: string): Date {
    // EXIF date format: "YYYY:MM:DD HH:MM:SS"
    const [datePart, timePart] = dateString.split(' ');
    const [year, month, day] = datePart.split(':').map(Number);
    const [hour, minute, second] = timePart.split(':').map(Number);
    
    return new Date(year, month - 1, day, hour, minute, second);
  }

  /**
   * Reverse geocode coordinates to get location details
   */
  private async reverseGeocode(lat: number, lng: number): Promise<Partial<PhotoMetadata['location']>> {
    const cacheKey = `${lat.toFixed(4)},${lng.toFixed(4)}`;
    
    if (this.geocodingCache.has(cacheKey)) {
      return this.geocodingCache.get(cacheKey);
    }

    try {
      // Using a free geocoding service (you might want to use Google Maps API in production)
      const response = await fetch(
        `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`
      );
      
      if (!response.ok) throw new Error('Geocoding failed');
      
      const data = await response.json();
      
      const locationDetails = {
        city: data.city || data.locality,
        state: data.principalSubdivision,
        country: data.countryName,
        address: data.localityInfo?.administrative?.[0]?.name
      };

      this.geocodingCache.set(cacheKey, locationDetails);
      return locationDetails;
    } catch (error) {
      console.error('Reverse geocoding failed:', error);
      return {};
    }
  }

  /**
   * Generate automatic tags based on metadata and basic image analysis
   */
  private async generateAutoTags(file: File, metadata: PhotoMetadata): Promise<string[]> {
    const tags: string[] = [];

    // Date-based tags
    if (metadata.dateTime?.taken) {
      const date = metadata.dateTime.taken;
      const year = date.getFullYear();
      const month = date.toLocaleString('default', { month: 'long' });
      const season = this.getSeason(date);
      
      tags.push(year.toString(), month, season);
      
      // Holiday detection (basic)
      const holidays = this.detectHolidays(date);
      tags.push(...holidays);
    }

    // Location-based tags
    if (metadata.location?.city) tags.push(metadata.location.city);
    if (metadata.location?.state) tags.push(metadata.location.state);
    if (metadata.location?.country) tags.push(metadata.location.country);

    // Camera-based tags
    if (metadata.camera?.make) tags.push(metadata.camera.make);
    if (metadata.settings?.flash) tags.push('flash');

    // Scene-based tags
    if (metadata.scene?.indoor) tags.push('indoor');
    if (metadata.scene?.outdoor) tags.push('outdoor');
    if (metadata.scene?.timeOfDay) tags.push(metadata.scene.timeOfDay);

    // Image properties
    if (metadata.dimensions) {
      const { width, height } = metadata.dimensions;
      const aspectRatio = width / height;
      
      if (aspectRatio > 1.5) tags.push('landscape');
      else if (aspectRatio < 0.75) tags.push('portrait');
      else tags.push('square');
      
      if (width > 3000 || height > 3000) tags.push('high-resolution');
    }

    // File type
    if (file.type.includes('jpeg') || file.type.includes('jpg')) tags.push('jpeg');
    if (file.type.includes('png')) tags.push('png');
    if (file.type.includes('heic')) tags.push('heic');

    return [...new Set(tags)]; // Remove duplicates
  }

  /**
   * Basic scene analysis
   */
  private async analyzeScene(file: File, metadata: PhotoMetadata): Promise<PhotoMetadata['scene']> {
    const scene: PhotoMetadata['scene'] = {};

    // Time of day analysis based on EXIF date
    if (metadata.dateTime?.taken) {
      const hour = metadata.dateTime.taken.getHours();
      if (hour >= 6 && hour < 12) scene.timeOfDay = 'morning';
      else if (hour >= 12 && hour < 17) scene.timeOfDay = 'afternoon';
      else if (hour >= 17 && hour < 21) scene.timeOfDay = 'evening';
      else scene.timeOfDay = 'night';
    }

    // Flash usage indicates indoor/artificial lighting
    if (metadata.settings?.flash) {
      scene.lighting = 'artificial';
      scene.indoor = true;
    }

    // GPS location suggests outdoor
    if (metadata.location?.latitude && metadata.location?.longitude) {
      scene.outdoor = true;
      scene.lighting = scene.lighting || 'natural';
    }

    return scene;
  }

  /**
   * Get season based on date
   */
  private getSeason(date: Date): string {
    const month = date.getMonth();
    if (month >= 2 && month <= 4) return 'spring';
    if (month >= 5 && month <= 7) return 'summer';
    if (month >= 8 && month <= 10) return 'fall';
    return 'winter';
  }

  /**
   * Detect holidays based on date (basic implementation)
   */
  private detectHolidays(date: Date): string[] {
    const holidays: string[] = [];
    const month = date.getMonth() + 1;
    const day = date.getDate();

    // Major holidays (US-centric, can be expanded)
    if (month === 1 && day === 1) holidays.push('New Year');
    if (month === 2 && day === 14) holidays.push('Valentine\'s Day');
    if (month === 7 && day === 4) holidays.push('Independence Day');
    if (month === 10 && day === 31) holidays.push('Halloween');
    if (month === 12 && day === 25) holidays.push('Christmas');
    if (month === 12 && day === 31) holidays.push('New Year\'s Eve');

    // Thanksgiving (4th Thursday of November)
    if (month === 11) {
      const firstThursday = new Date(date.getFullYear(), 10, 1);
      while (firstThursday.getDay() !== 4) {
        firstThursday.setDate(firstThursday.getDate() + 1);
      }
      const thanksgiving = new Date(firstThursday);
      thanksgiving.setDate(thanksgiving.getDate() + 21); // 4th Thursday
      
      if (day === thanksgiving.getDate()) {
        holidays.push('Thanksgiving');
      }
    }

    return holidays;
  }

  /**
   * Update existing photo with extracted metadata
   */
  async updatePhotoMetadata(photo: Photo, file: File): Promise<Photo> {
    const metadata = await this.extractMetadata(file);
    
    return {
      ...photo,
      metadata: {
        ...photo.metadata,
        ...metadata,
        // Preserve existing people and custom tags
        people: photo.metadata?.people || metadata.people || [],
        tags: [
          ...(photo.metadata?.tags || []),
          ...(metadata.tags || [])
        ].filter((tag, index, arr) => arr.indexOf(tag) === index) // Remove duplicates
      }
    };
  }

  /**
   * Batch process multiple photos
   */
  async batchExtractMetadata(files: File[]): Promise<PhotoMetadata[]> {
    const results = await Promise.allSettled(
      files.map(file => this.extractMetadata(file))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        console.error(`Failed to extract metadata for file ${index}:`, result.reason);
        return {} as PhotoMetadata;
      }
    });
  }
}

export const photoMetadataService = new PhotoMetadataService();
