import { supabase } from '../lib/supabase';

export interface PushSubscription {
  id: string;
  userId: string;
  tenantId?: string;
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
  userAgent: string;
  deviceType: 'mobile' | 'desktop' | 'tablet';
  isActive: boolean;
  createdAt: Date;
  lastUsed: Date;
}

export interface NotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  image?: string;
  tag?: string;
  data?: Record<string, any>;
  actions?: NotificationAction[];
  requireInteraction?: boolean;
  silent?: boolean;
  timestamp?: number;
}

export interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

class PushNotificationService {
  private vapidPublicKey = 'BEl62iUYgUivxIkv69yViEuiBIa40HI0DLLuxazjqAKVXTJtkTXaXWXFLVQQHcB0XUYpada8ENiMw-qcnbiy6-4'; // Demo key
  private registration: ServiceWorkerRegistration | null = null;

  /**
   * Initialize push notification service
   */
  async initialize(): Promise<boolean> {
    try {
      // Check if service workers are supported
      if (!('serviceWorker' in navigator)) {
        console.warn('Service workers not supported');
        return false;
      }

      // Check if push messaging is supported
      if (!('PushManager' in window)) {
        console.warn('Push messaging not supported');
        return false;
      }

      // Register service worker
      this.registration = await navigator.serviceWorker.register('/sw.js');
      console.log('Service worker registered:', this.registration);

      // Wait for service worker to be ready
      await navigator.serviceWorker.ready;

      return true;
    } catch (error) {
      console.error('Failed to initialize push notifications:', error);
      return false;
    }
  }

  /**
   * Request notification permission
   */
  async requestPermission(): Promise<NotificationPermission> {
    try {
      const permission = await Notification.requestPermission();
      console.log('Notification permission:', permission);
      return permission;
    } catch (error) {
      console.error('Failed to request notification permission:', error);
      return 'denied';
    }
  }

  /**
   * Subscribe to push notifications
   */
  async subscribe(userId: string, tenantId?: string): Promise<PushSubscription | null> {
    try {
      if (!this.registration) {
        await this.initialize();
      }

      if (!this.registration) {
        throw new Error('Service worker not registered');
      }

      // Check permission
      const permission = await this.requestPermission();
      if (permission !== 'granted') {
        throw new Error('Notification permission denied');
      }

      // Subscribe to push notifications
      const subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(this.vapidPublicKey)
      });

      // Convert to our format
      const pushSubscription: Omit<PushSubscription, 'id'> = {
        userId,
        tenantId,
        endpoint: subscription.endpoint,
        keys: {
          p256dh: this.arrayBufferToBase64(subscription.getKey('p256dh')!),
          auth: this.arrayBufferToBase64(subscription.getKey('auth')!)
        },
        userAgent: navigator.userAgent,
        deviceType: this.getDeviceType(),
        isActive: true,
        createdAt: new Date(),
        lastUsed: new Date()
      };

      // Save subscription to database
      const savedSubscription = await this.saveSubscription(pushSubscription);
      
      console.log('Push subscription created:', savedSubscription);
      return savedSubscription;
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      return null;
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribe(userId: string): Promise<boolean> {
    try {
      if (!this.registration) {
        return false;
      }

      const subscription = await this.registration.pushManager.getSubscription();
      if (subscription) {
        await subscription.unsubscribe();
      }

      // Remove from database
      await this.removeSubscription(userId);
      
      console.log('Push subscription removed');
      return true;
    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error);
      return false;
    }
  }

  /**
   * Send local notification
   */
  async sendLocalNotification(payload: NotificationPayload): Promise<void> {
    try {
      if (!this.registration) {
        await this.initialize();
      }

      if (Notification.permission !== 'granted') {
        console.warn('Notification permission not granted');
        return;
      }

      // Send notification through service worker
      if (this.registration && this.registration.active) {
        this.registration.active.postMessage({
          type: 'SHOW_NOTIFICATION',
          payload
        });
      } else {
        // Fallback to direct notification
        new Notification(payload.title, {
          body: payload.body,
          icon: payload.icon || '/icon-192x192.png',
          badge: payload.badge || '/icon-192x192.png',
          image: payload.image,
          tag: payload.tag,
          data: payload.data,
          requireInteraction: payload.requireInteraction,
          silent: payload.silent,
          timestamp: payload.timestamp || Date.now()
        });
      }
    } catch (error) {
      console.error('Failed to send local notification:', error);
    }
  }

  /**
   * Send medication reminder notification
   */
  async sendMedicationReminder(medicationName: string, dosage: string, userId: string): Promise<void> {
    const payload: NotificationPayload = {
      title: '💊 Medication Reminder',
      body: `Time to take ${medicationName} (${dosage})`,
      icon: '/icons/medication.png',
      badge: '/icons/badge.png',
      tag: 'medication-reminder',
      data: {
        type: 'medication',
        medicationName,
        dosage,
        userId,
        timestamp: Date.now()
      },
      actions: [
        { action: 'taken', title: 'Mark as Taken', icon: '/icons/check.png' },
        { action: 'snooze', title: 'Snooze 10 min', icon: '/icons/snooze.png' }
      ],
      requireInteraction: true
    };

    await this.sendLocalNotification(payload);
  }

  /**
   * Send photo prompt notification
   */
  async sendPhotoPrompt(userId: string): Promise<void> {
    const payload: NotificationPayload = {
      title: '📸 Share a Memory',
      body: 'Would you like to share a photo and tell us about it?',
      icon: '/icons/camera.png',
      badge: '/icons/badge.png',
      tag: 'photo-prompt',
      data: {
        type: 'photo-prompt',
        userId,
        timestamp: Date.now()
      },
      actions: [
        { action: 'open-camera', title: 'Take Photo', icon: '/icons/camera.png' },
        { action: 'open-gallery', title: 'Choose Photo', icon: '/icons/gallery.png' }
      ]
    };

    await this.sendLocalNotification(payload);
  }

  /**
   * Send family update notification
   */
  async sendFamilyUpdate(message: string, fromUser: string, userId: string): Promise<void> {
    const payload: NotificationPayload = {
      title: '👥 Family Update',
      body: `${fromUser}: ${message}`,
      icon: '/icons/family.png',
      badge: '/icons/badge.png',
      tag: 'family-update',
      data: {
        type: 'family-update',
        message,
        fromUser,
        userId,
        timestamp: Date.now()
      },
      actions: [
        { action: 'view', title: 'View', icon: '/icons/view.png' },
        { action: 'reply', title: 'Reply', icon: '/icons/reply.png' }
      ]
    };

    await this.sendLocalNotification(payload);
  }

  /**
   * Send emergency alert
   */
  async sendEmergencyAlert(message: string, userId: string): Promise<void> {
    const payload: NotificationPayload = {
      title: '🚨 Emergency Alert',
      body: message,
      icon: '/icons/emergency.png',
      badge: '/icons/badge.png',
      tag: 'emergency',
      data: {
        type: 'emergency',
        message,
        userId,
        timestamp: Date.now()
      },
      requireInteraction: true,
      actions: [
        { action: 'call-emergency', title: 'Call Emergency', icon: '/icons/phone.png' },
        { action: 'contact-family', title: 'Contact Family', icon: '/icons/family.png' }
      ]
    };

    await this.sendLocalNotification(payload);
  }

  /**
   * Save subscription to database
   */
  private async saveSubscription(subscription: Omit<PushSubscription, 'id'>): Promise<PushSubscription> {
    try {
      // Check if push_subscriptions table exists
      const { data: tableCheck, error: tableError } = await supabase
        .from('push_subscriptions')
        .select('id')
        .limit(1);

      // If tables don't exist, store in localStorage
      if (tableError && tableError.code === 'PGRST200') {
        const subscriptions = JSON.parse(localStorage.getItem('push_subscriptions') || '[]');
        const newSubscription = { id: crypto.randomUUID(), ...subscription };
        subscriptions.push(newSubscription);
        localStorage.setItem('push_subscriptions', JSON.stringify(subscriptions));
        return newSubscription;
      }

      const { data, error } = await supabase
        .from('push_subscriptions')
        .insert([{
          user_id: subscription.userId,
          tenant_id: subscription.tenantId,
          endpoint: subscription.endpoint,
          keys: subscription.keys,
          user_agent: subscription.userAgent,
          device_type: subscription.deviceType,
          is_active: subscription.isActive,
          created_at: subscription.createdAt.toISOString(),
          last_used: subscription.lastUsed.toISOString()
        }])
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id,
        userId: data.user_id,
        tenantId: data.tenant_id,
        endpoint: data.endpoint,
        keys: data.keys,
        userAgent: data.user_agent,
        deviceType: data.device_type,
        isActive: data.is_active,
        createdAt: new Date(data.created_at),
        lastUsed: new Date(data.last_used)
      };
    } catch (error) {
      console.error('Failed to save push subscription:', error);
      throw error;
    }
  }

  /**
   * Remove subscription from database
   */
  private async removeSubscription(userId: string): Promise<void> {
    try {
      // Check if push_subscriptions table exists
      const { data: tableCheck, error: tableError } = await supabase
        .from('push_subscriptions')
        .select('id')
        .limit(1);

      // If tables don't exist, remove from localStorage
      if (tableError && tableError.code === 'PGRST200') {
        const subscriptions = JSON.parse(localStorage.getItem('push_subscriptions') || '[]');
        const filtered = subscriptions.filter((sub: any) => sub.userId !== userId);
        localStorage.setItem('push_subscriptions', JSON.stringify(filtered));
        return;
      }

      const { error } = await supabase
        .from('push_subscriptions')
        .delete()
        .eq('user_id', userId);

      if (error) throw error;
    } catch (error) {
      console.error('Failed to remove push subscription:', error);
    }
  }

  /**
   * Utility functions
   */
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary);
  }

  private getDeviceType(): 'mobile' | 'desktop' | 'tablet' {
    const userAgent = navigator.userAgent.toLowerCase();
    if (/mobile|android|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent)) {
      return 'mobile';
    } else if (/tablet|ipad/i.test(userAgent)) {
      return 'tablet';
    }
    return 'desktop';
  }
}

export const pushNotificationService = new PushNotificationService();
