import type { Photo } from '../types';

export interface AIMessage {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  photos?: Photo[];
  metadata?: {
    emotion?: string;
    confidence?: number;
    context?: string[];
    responseTime?: number;
  };
}

export interface ConversationContext {
  userId: string;
  userProfile?: {
    name: string;
    age?: number;
    preferences?: string[];
    memoryStage?: 'early' | 'moderate' | 'advanced';
  };
  recentPhotos: Photo[];
  conversationHistory: AIMessage[];
  currentSession: {
    startTime: Date;
    photoCount: number;
    topics: string[];
  };
}

export interface AIResponse {
  content: string;
  emotion: string;
  confidence: number;
  suggestedActions?: string[];
  followUpQuestions?: string[];
}

class AIService {
  private apiKey: string | null = null;
  private baseUrl: string = 'https://api.openai.com/v1';
  private model: string = 'gpt-4-vision-preview';

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENAI_API_KEY || null;
  }

  /**
   * Generate AI response based on user message and context
   */
  async generateResponse(
    userMessage: string,
    photos: Photo[] = [],
    context: Partial<ConversationContext> = {}
  ): Promise<AIResponse> {
    // If no API key, use fallback responses
    if (!this.apiKey) {
      return this.generateFallbackResponse(userMessage, photos, context);
    }

    try {
      const prompt = this.buildPrompt(userMessage, photos, context);
      const response = await this.callOpenAI(prompt, photos);
      
      return {
        content: response.content,
        emotion: this.detectEmotion(response.content),
        confidence: 0.85,
        suggestedActions: this.generateSuggestedActions(userMessage, photos),
        followUpQuestions: this.generateFollowUpQuestions(userMessage, photos)
      };
    } catch (error) {
      console.error('AI Service Error:', error);
      return this.generateFallbackResponse(userMessage, photos, context);
    }
  }

  /**
   * Build conversation prompt for AI
   */
  private buildPrompt(
    userMessage: string,
    photos: Photo[],
    context: Partial<ConversationContext>
  ): string {
    const systemPrompt = `You are a compassionate AI companion designed to help people with memory challenges explore and discuss their photos and memories. Your role is to:

1. Be warm, patient, and encouraging
2. Ask gentle, open-ended questions about photos and memories
3. Help users recall details and share stories
4. Provide emotional support and validation
5. Keep conversations focused on positive memories
6. Use simple, clear language
7. Show genuine interest in their experiences

Guidelines:
- Always respond with empathy and warmth
- Ask one question at a time to avoid overwhelming
- Celebrate shared memories and stories
- If someone seems confused, gently redirect to the photos
- Focus on emotions and feelings, not just facts
- Use the person's name when known
- Keep responses conversational and natural`;

    let prompt = systemPrompt + '\n\n';

    // Add user context
    if (context.userProfile?.name) {
      prompt += `User's name: ${context.userProfile.name}\n`;
    }
    
    if (context.userProfile?.memoryStage) {
      prompt += `Memory stage: ${context.userProfile.memoryStage}\n`;
    }

    // Add photo context
    if (photos.length > 0) {
      prompt += '\nPhotos shared:\n';
      photos.forEach((photo, index) => {
        prompt += `${index + 1}. ${photo.filename}`;
        if (photo.description) {
          prompt += ` - ${photo.description}`;
        }
        if (photo.metadata?.location) {
          prompt += ` (Location: ${photo.metadata.location.city || 'Unknown'})`;
        }
        if (photo.metadata?.people && photo.metadata.people.length > 0) {
          prompt += ` (People: ${photo.metadata.people.join(', ')})`;
        }
        prompt += `\n`;
      });
    }

    // Add conversation history (last 5 messages)
    if (context.conversationHistory && context.conversationHistory.length > 0) {
      prompt += '\nRecent conversation:\n';
      const recentMessages = context.conversationHistory.slice(-5);
      recentMessages.forEach(msg => {
        prompt += `${msg.sender === 'user' ? 'User' : 'AI'}: ${msg.content}\n`;
      });
    }

    prompt += `\nUser's current message: "${userMessage}"\n\nPlease respond warmly and helpfully:`;

    return prompt;
  }

  /**
   * Call OpenAI API
   */
  private async callOpenAI(prompt: string, photos: Photo[]): Promise<{ content: string }> {
    const messages = [
      {
        role: 'user',
        content: [
          { type: 'text', text: prompt },
          // Add photo analysis if photos are provided
          ...photos.slice(0, 3).map(photo => ({
            type: 'image_url',
            image_url: { url: photo.url }
          }))
        ]
      }
    ];

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: this.model,
        messages,
        max_tokens: 300,
        temperature: 0.7
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    return {
      content: data.choices[0]?.message?.content || 'I apologize, but I had trouble responding. Could you try again?'
    };
  }

  /**
   * Generate fallback response when AI API is not available
   */
  private generateFallbackResponse(
    userMessage: string,
    photos: Photo[],
    context: Partial<ConversationContext>
  ): AIResponse {
    const userName = context.userProfile?.name || 'friend';
    let response = '';

    // Photo-based responses
    if (photos.length > 0) {
      const photo = photos[0];
      const responses = [
        `What a wonderful photo, ${userName}! I can see this is "${photo.filename}". Tell me more about this special moment.`,
        `This looks like a precious memory! What was happening when this photo was taken?`,
        `I love seeing your photos! Who was with you in this picture?`,
        `This brings back memories, doesn't it? What do you remember most about this day?`
      ];
      
      if (photo.metadata?.location?.city) {
        responses.push(`I see this was taken in ${photo.metadata.location.city}. What was special about being there?`);
      }
      
      response = responses[Math.floor(Math.random() * responses.length)];
    }
    // Greeting responses
    else if (userMessage.toLowerCase().includes('hello') || userMessage.toLowerCase().includes('hi')) {
      const greetings = [
        `Hello ${userName}! I'm so happy to chat with you today. Would you like to share a photo and tell me about it?`,
        `Hi there! It's wonderful to see you. Do you have any special photos you'd like to talk about?`,
        `Hello! I'd love to hear about your memories today. Shall we look at some photos together?`
      ];
      response = greetings[Math.floor(Math.random() * greetings.length)];
    }
    // Memory-related responses
    else if (userMessage.toLowerCase().includes('remember') || userMessage.toLowerCase().includes('memory')) {
      const memoryResponses = [
        `Memories are such treasures! Photos often help us remember wonderful details. Would you like to share one?`,
        `I love talking about memories with you. Do you have a favorite photo that brings back special feelings?`,
        `Remembering is so important. Sometimes looking at photos helps us recall even more details. Shall we try that?`
      ];
      response = memoryResponses[Math.floor(Math.random() * memoryResponses.length)];
    }
    // Family responses
    else if (userMessage.toLowerCase().includes('family')) {
      const familyResponses = [
        `Family is so precious! I'd love to see some family photos if you have them.`,
        `Family memories are often the most special ones. Do you have photos of family gatherings or special moments?`,
        `Tell me about your family! I bet you have some wonderful photos of them.`
      ];
      response = familyResponses[Math.floor(Math.random() * familyResponses.length)];
    }
    // General responses
    else {
      const generalResponses = [
        `That's interesting! I'd love to learn more. Do you have any photos related to what you're telling me?`,
        `Thank you for sharing that with me. Photos often help us remember more details - would you like to look at some together?`,
        `I enjoy our conversations so much. Would you like to share a photo that's meaningful to you?`
      ];
      response = generalResponses[Math.floor(Math.random() * generalResponses.length)];
    }

    return {
      content: response,
      emotion: 'caring',
      confidence: 0.7,
      suggestedActions: this.generateSuggestedActions(userMessage, photos),
      followUpQuestions: this.generateFollowUpQuestions(userMessage, photos)
    };
  }

  /**
   * Detect emotion from AI response
   */
  private detectEmotion(content: string): string {
    const emotions = {
      'happy': ['wonderful', 'amazing', 'beautiful', 'joy', 'smile', 'laugh'],
      'caring': ['love', 'precious', 'special', 'treasure', 'dear'],
      'excited': ['exciting', 'fantastic', 'incredible', 'wow'],
      'gentle': ['gentle', 'soft', 'calm', 'peaceful', 'quiet']
    };

    const lowerContent = content.toLowerCase();
    
    for (const [emotion, keywords] of Object.entries(emotions)) {
      if (keywords.some(keyword => lowerContent.includes(keyword))) {
        return emotion;
      }
    }
    
    return 'caring'; // Default emotion
  }

  /**
   * Generate suggested actions
   */
  private generateSuggestedActions(userMessage: string, photos: Photo[]): string[] {
    const actions = [];
    
    if (photos.length === 0) {
      actions.push('Add a photo to share');
    }
    
    if (photos.length > 0) {
      actions.push('Tell me more about this photo');
      actions.push('Share another memory');
    }
    
    actions.push('Ask about family');
    actions.push('Share a favorite memory');
    
    return actions.slice(0, 3);
  }

  /**
   * Generate follow-up questions
   */
  private generateFollowUpQuestions(userMessage: string, photos: Photo[]): string[] {
    const questions = [];
    
    if (photos.length > 0) {
      questions.push('Who was with you in this photo?');
      questions.push('What was your favorite part of this day?');
      questions.push('Does this photo remind you of other special times?');
    } else {
      questions.push('Would you like to share a photo with me?');
      questions.push('What\'s your favorite type of memory to talk about?');
      questions.push('Do you have photos of family or friends?');
    }
    
    return questions.slice(0, 2);
  }

  /**
   * Save conversation to database
   */
  async saveConversation(messages: AIMessage[], userId: string): Promise<void> {
    try {
      // In a real implementation, save to Supabase
      console.log('Saving conversation for user:', userId, messages);
    } catch (error) {
      console.error('Error saving conversation:', error);
    }
  }

  /**
   * Load conversation history
   */
  async loadConversationHistory(userId: string, limit: number = 50): Promise<AIMessage[]> {
    try {
      // In a real implementation, load from Supabase
      console.log('Loading conversation history for user:', userId);
      return [];
    } catch (error) {
      console.error('Error loading conversation history:', error);
      return [];
    }
  }
}

export const aiService = new AIService();
