import { supabase } from '../lib/supabase';
import type { Photo } from '../types';

export interface ConversationLink {
  id: string;
  photoId: string;
  conversationId: string;
  messageId?: string;
  timestamp: Date;
  context: string;
  sentiment?: 'positive' | 'neutral' | 'negative';
  topics?: string[];
  memories?: string[];
}

export interface PhotoConversationInsight {
  photoId: string;
  totalConversations: number;
  lastDiscussed: Date;
  averageSentiment: number;
  popularTopics: string[];
  memoriesShared: string[];
  engagementScore: number;
}

class PhotoConversationService {
  /**
   * Link a photo to a conversation
   */
  async linkPhotoToConversation(
    photoId: string,
    conversationId: string,
    messageId: string,
    context: string,
    sentiment?: 'positive' | 'neutral' | 'negative',
    topics?: string[]
  ): Promise<ConversationLink> {
    try {
      const link: Omit<ConversationLink, 'id'> = {
        photoId,
        conversationId,
        messageId,
        timestamp: new Date(),
        context,
        sentiment,
        topics
      };

      const { data, error } = await supabase
        .from('photo_conversations')
        .insert([{
          photo_id: link.photoId,
          conversation_id: link.conversationId,
          message_id: link.messageId,
          timestamp: link.timestamp.toISOString(),
          context: link.context,
          sentiment: link.sentiment,
          topics: link.topics || [],
          metadata: {}
        }])
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id,
        photoId: data.photo_id,
        conversationId: data.conversation_id,
        messageId: data.message_id,
        timestamp: new Date(data.timestamp),
        context: data.context,
        sentiment: data.sentiment,
        topics: data.topics
      };
    } catch (error) {
      console.error('Error linking photo to conversation:', error);
      throw error;
    }
  }

  /**
   * Get conversation links for a photo
   */
  async getPhotoConversations(photoId: string): Promise<ConversationLink[]> {
    try {
      const { data, error } = await supabase
        .from('photo_conversations')
        .select('*')
        .eq('photo_id', photoId)
        .order('timestamp', { ascending: false });

      if (error) throw error;

      return (data || []).map(item => ({
        id: item.id,
        photoId: item.photo_id,
        conversationId: item.conversation_id,
        messageId: item.message_id,
        timestamp: new Date(item.timestamp),
        context: item.context,
        sentiment: item.sentiment,
        topics: item.topics || []
      }));
    } catch (error) {
      console.error('Error getting photo conversations:', error);
      return [];
    }
  }

  /**
   * Get photos discussed in a conversation
   */
  async getConversationPhotos(conversationId: string): Promise<ConversationLink[]> {
    try {
      const { data, error } = await supabase
        .from('photo_conversations')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('timestamp', { ascending: true });

      if (error) throw error;

      return (data || []).map(item => ({
        id: item.id,
        photoId: item.photo_id,
        conversationId: item.conversation_id,
        messageId: item.message_id,
        timestamp: new Date(item.timestamp),
        context: item.context,
        sentiment: item.sentiment,
        topics: item.topics || []
      }));
    } catch (error) {
      console.error('Error getting conversation photos:', error);
      return [];
    }
  }

  /**
   * Generate insights for a photo based on conversation history
   */
  async generatePhotoInsights(photoId: string): Promise<PhotoConversationInsight> {
    try {
      const conversations = await this.getPhotoConversations(photoId);
      
      if (conversations.length === 0) {
        return {
          photoId,
          totalConversations: 0,
          lastDiscussed: new Date(0),
          averageSentiment: 0,
          popularTopics: [],
          memoriesShared: [],
          engagementScore: 0
        };
      }

      // Calculate metrics
      const totalConversations = conversations.length;
      const lastDiscussed = conversations[0].timestamp; // Already sorted desc
      
      // Calculate average sentiment
      const sentimentScores = conversations
        .filter(c => c.sentiment)
        .map(c => {
          switch (c.sentiment) {
            case 'positive': return 1;
            case 'neutral': return 0;
            case 'negative': return -1;
            default: return 0;
          }
        });
      
      const averageSentiment = sentimentScores.length > 0
        ? sentimentScores.reduce((sum, score) => sum + score, 0) / sentimentScores.length
        : 0;

      // Extract popular topics
      const topicCounts = new Map<string, number>();
      conversations.forEach(conv => {
        conv.topics?.forEach(topic => {
          topicCounts.set(topic, (topicCounts.get(topic) || 0) + 1);
        });
      });

      const popularTopics = Array.from(topicCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([topic]) => topic);

      // Extract memories (from context)
      const memoriesShared = conversations
        .map(conv => conv.context)
        .filter(context => context.length > 20) // Filter out short contexts
        .slice(0, 10); // Limit to recent memories

      // Calculate engagement score (0-100)
      const daysSinceLastDiscussion = Math.floor(
        (Date.now() - lastDiscussed.getTime()) / (1000 * 60 * 60 * 24)
      );
      
      const recencyScore = Math.max(0, 100 - daysSinceLastDiscussion * 2); // Decay over time
      const frequencyScore = Math.min(100, totalConversations * 10); // More conversations = higher score
      const sentimentScore = (averageSentiment + 1) * 50; // Convert -1,1 to 0,100
      
      const engagementScore = Math.round(
        (recencyScore * 0.3 + frequencyScore * 0.4 + sentimentScore * 0.3)
      );

      return {
        photoId,
        totalConversations,
        lastDiscussed,
        averageSentiment,
        popularTopics,
        memoriesShared,
        engagementScore
      };
    } catch (error) {
      console.error('Error generating photo insights:', error);
      return {
        photoId,
        totalConversations: 0,
        lastDiscussed: new Date(0),
        averageSentiment: 0,
        popularTopics: [],
        memoriesShared: [],
        engagementScore: 0
      };
    }
  }

  /**
   * Find photos similar to current conversation context
   */
  async findRelatedPhotos(
    topics: string[],
    sentiment: 'positive' | 'neutral' | 'negative',
    limit: number = 5
  ): Promise<string[]> {
    try {
      if (topics.length === 0) return [];

      const { data, error } = await supabase
        .from('photo_conversations')
        .select('photo_id, topics, sentiment')
        .overlaps('topics', topics)
        .limit(limit * 2); // Get more to filter by sentiment

      if (error) throw error;

      // Score photos by topic overlap and sentiment match
      const photoScores = new Map<string, number>();
      
      (data || []).forEach(item => {
        const topicOverlap = (item.topics || []).filter(topic => 
          topics.includes(topic)
        ).length;
        
        const sentimentMatch = item.sentiment === sentiment ? 1 : 0;
        const score = topicOverlap * 2 + sentimentMatch;
        
        photoScores.set(item.photo_id, Math.max(
          photoScores.get(item.photo_id) || 0,
          score
        ));
      });

      // Return top scored photos
      return Array.from(photoScores.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, limit)
        .map(([photoId]) => photoId);
    } catch (error) {
      console.error('Error finding related photos:', error);
      return [];
    }
  }

  /**
   * Get conversation statistics for a user
   */
  async getUserConversationStats(userId: string): Promise<{
    totalPhotoConversations: number;
    averageConversationsPerPhoto: number;
    mostDiscussedPhotos: string[];
    conversationTrends: { date: string; count: number }[];
  }> {
    try {
      // Get all photo conversations for user's photos
      const { data: userPhotos, error: photosError } = await supabase
        .from('photos')
        .select('id')
        .eq('user_id', userId);

      if (photosError) throw photosError;

      const photoIds = (userPhotos || []).map(p => p.id);
      
      if (photoIds.length === 0) {
        return {
          totalPhotoConversations: 0,
          averageConversationsPerPhoto: 0,
          mostDiscussedPhotos: [],
          conversationTrends: []
        };
      }

      const { data: conversations, error: conversationsError } = await supabase
        .from('photo_conversations')
        .select('photo_id, timestamp')
        .in('photo_id', photoIds);

      if (conversationsError) throw conversationsError;

      const totalPhotoConversations = conversations?.length || 0;
      const averageConversationsPerPhoto = totalPhotoConversations / photoIds.length;

      // Find most discussed photos
      const photoConversationCounts = new Map<string, number>();
      (conversations || []).forEach(conv => {
        photoConversationCounts.set(
          conv.photo_id,
          (photoConversationCounts.get(conv.photo_id) || 0) + 1
        );
      });

      const mostDiscussedPhotos = Array.from(photoConversationCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([photoId]) => photoId);

      // Generate conversation trends (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const recentConversations = (conversations || [])
        .filter(conv => new Date(conv.timestamp) >= thirtyDaysAgo);

      const dailyCounts = new Map<string, number>();
      recentConversations.forEach(conv => {
        const date = new Date(conv.timestamp).toISOString().split('T')[0];
        dailyCounts.set(date, (dailyCounts.get(date) || 0) + 1);
      });

      const conversationTrends = Array.from(dailyCounts.entries())
        .map(([date, count]) => ({ date, count }))
        .sort((a, b) => a.date.localeCompare(b.date));

      return {
        totalPhotoConversations,
        averageConversationsPerPhoto: Math.round(averageConversationsPerPhoto * 10) / 10,
        mostDiscussedPhotos,
        conversationTrends
      };
    } catch (error) {
      console.error('Error getting user conversation stats:', error);
      return {
        totalPhotoConversations: 0,
        averageConversationsPerPhoto: 0,
        mostDiscussedPhotos: [],
        conversationTrends: []
      };
    }
  }

  /**
   * Auto-link photos mentioned in conversation messages
   */
  async autoLinkPhotosInMessage(
    messageContent: string,
    conversationId: string,
    messageId: string,
    availablePhotos: Photo[]
  ): Promise<ConversationLink[]> {
    const links: ConversationLink[] = [];
    
    try {
      // Simple keyword matching for photo filenames and descriptions
      const messageWords = messageContent.toLowerCase().split(/\s+/);
      
      for (const photo of availablePhotos) {
        const photoKeywords = [
          photo.filename.toLowerCase(),
          ...(photo.description?.toLowerCase().split(/\s+/) || []),
          ...(photo.metadata?.tags?.map(tag => tag.toLowerCase()) || []),
          ...(photo.metadata?.people?.map(person => person.toLowerCase()) || [])
        ];

        // Check for keyword matches
        const hasMatch = photoKeywords.some(keyword => 
          messageWords.some(word => 
            word.includes(keyword) || keyword.includes(word)
          )
        );

        if (hasMatch) {
          // Determine sentiment (basic)
          const sentiment = this.detectMessageSentiment(messageContent);
          
          // Extract topics (basic)
          const topics = this.extractTopicsFromMessage(messageContent);

          const link = await this.linkPhotoToConversation(
            photo.id,
            conversationId,
            messageId,
            `Auto-linked: ${messageContent.substring(0, 100)}...`,
            sentiment,
            topics
          );

          links.push(link);
        }
      }
    } catch (error) {
      console.error('Error auto-linking photos:', error);
    }

    return links;
  }

  /**
   * Basic sentiment detection
   */
  private detectMessageSentiment(message: string): 'positive' | 'neutral' | 'negative' {
    const positiveWords = ['love', 'happy', 'wonderful', 'amazing', 'beautiful', 'great', 'fun', 'joy'];
    const negativeWords = ['sad', 'miss', 'difficult', 'hard', 'worry', 'upset', 'lost'];
    
    const lowerMessage = message.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerMessage.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerMessage.includes(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  /**
   * Basic topic extraction
   */
  private extractTopicsFromMessage(message: string): string[] {
    const topicKeywords = {
      'family': ['family', 'mom', 'dad', 'sister', 'brother', 'grandmother', 'grandfather'],
      'travel': ['trip', 'vacation', 'travel', 'visit', 'journey'],
      'celebration': ['birthday', 'anniversary', 'wedding', 'party', 'celebration'],
      'holiday': ['christmas', 'thanksgiving', 'easter', 'holiday'],
      'home': ['home', 'house', 'garden', 'kitchen', 'room'],
      'friends': ['friend', 'friends', 'buddy', 'pal'],
      'work': ['work', 'job', 'office', 'career', 'colleague']
    };

    const lowerMessage = message.toLowerCase();
    const topics: string[] = [];

    Object.entries(topicKeywords).forEach(([topic, keywords]) => {
      if (keywords.some(keyword => lowerMessage.includes(keyword))) {
        topics.push(topic);
      }
    });

    return topics;
  }
}

export const photoConversationService = new PhotoConversationService();
