import { supabase } from "../lib/supabase";
import type {
  Tenant,
  TenantUser,
  TenantInvitation,
  TenantActivity,
  TenantSettings,
  TenantUserPermissions,
  DEFAULT_TENANT_SETTINGS,
  DEFAULT_PERMISSIONS,
} from "../types/tenant";
import { v4 as uuidv4 } from "uuid";

class TenantService {
  /**
   * Create a new tenant (family group)
   */
  async createTenant(
    name: string,
    type: "family" | "facility" | "organization",
    ownerId: string,
    settings?: Partial<TenantSettings>
  ): Promise<Tenant> {
    try {
      // Check if tenant tables exist first
      const { data: tableCheck, error: tableError } = await supabase
        .from("tenants")
        .select("id")
        .limit(1);

      // If tables don't exist, create a mock tenant for now
      if (tableError && tableError.code === "PGRST200") {
        console.log("Tenant tables not yet created, creating mock tenant");
        const mockTenant: Tenant = {
          id: uuidv4(),
          name,
          type,
          status: "active",
          settings: { ...DEFAULT_TENANT_SETTINGS, ...settings },
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {
            familySize: 1,
            primaryContact: ownerId,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            language: "en",
          },
        };
        return mockTenant;
      }

      const tenantId = uuidv4();
      const now = new Date();

      const tenant: Omit<Tenant, "id"> = {
        name,
        type,
        status: "active",
        settings: { ...DEFAULT_TENANT_SETTINGS, ...settings },
        createdAt: now,
        updatedAt: now,
        metadata: {
          familySize: 1,
          primaryContact: ownerId,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          language: "en",
        },
      };

      // Insert tenant
      const { data: tenantData, error: tenantError } = await supabase
        .from("tenants")
        .insert([
          {
            id: tenantId,
            name: tenant.name,
            type: tenant.type,
            status: tenant.status,
            settings: tenant.settings,
            created_at: tenant.createdAt.toISOString(),
            updated_at: tenant.updatedAt.toISOString(),
            metadata: tenant.metadata,
          },
        ])
        .select()
        .single();

      if (tenantError) throw tenantError;

      // Add owner as first tenant user
      await this.addUserToTenant(tenantId, ownerId, "owner");

      // Log activity
      await this.logActivity(
        tenantId,
        ownerId,
        "tenant_created",
        "tenant",
        tenantId,
        {
          tenantName: name,
          tenantType: type,
        }
      );

      return {
        id: tenantData.id,
        name: tenantData.name,
        type: tenantData.type,
        status: tenantData.status,
        settings: tenantData.settings,
        createdAt: new Date(tenantData.created_at),
        updatedAt: new Date(tenantData.updated_at),
        metadata: tenantData.metadata,
      };
    } catch (error) {
      console.error("Error creating tenant:", error);
      throw error;
    }
  }

  /**
   * Get tenant by ID
   */
  async getTenant(tenantId: string): Promise<Tenant | null> {
    try {
      const { data, error } = await supabase
        .from("tenants")
        .select("*")
        .eq("id", tenantId)
        .single();

      if (error) {
        if (error.code === "PGRST116") return null; // Not found
        throw error;
      }

      return {
        id: data.id,
        name: data.name,
        type: data.type,
        status: data.status,
        settings: data.settings,
        subscription: data.subscription,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
        metadata: data.metadata,
      };
    } catch (error) {
      console.error("Error getting tenant:", error);
      throw error;
    }
  }

  /**
   * Get user's tenants
   */
  async getUserTenants(userId: string): Promise<Tenant[]> {
    try {
      // Check if tenant tables exist first
      const { data: tableCheck, error: tableError } = await supabase
        .from("tenant_users")
        .select("id")
        .limit(1);

      // If tables don't exist, return empty array for now
      if (tableError && tableError.code === "PGRST200") {
        console.log("Tenant tables not yet created, returning empty array");
        return [];
      }

      const { data, error } = await supabase
        .from("tenant_users")
        .select(
          `
          tenant_id,
          role,
          status,
          tenants (*)
        `
        )
        .eq("user_id", userId)
        .eq("status", "active");

      if (error) throw error;

      return (data || [])
        .filter((item) => item.tenants)
        .map((item) => ({
          id: item.tenants.id,
          name: item.tenants.name,
          type: item.tenants.type,
          status: item.tenants.status,
          settings: item.tenants.settings,
          subscription: item.tenants.subscription,
          createdAt: new Date(item.tenants.created_at),
          updatedAt: new Date(item.tenants.updated_at),
          metadata: item.tenants.metadata,
        }));
    } catch (error) {
      console.error("Error getting user tenants:", error);
      // Return empty array instead of throwing to prevent app crash
      return [];
    }
  }

  /**
   * Add user to tenant
   */
  async addUserToTenant(
    tenantId: string,
    userId: string,
    role: "owner" | "admin" | "caregiver" | "patient" | "guest",
    invitedBy?: string,
    metadata?: any
  ): Promise<TenantUser> {
    try {
      const now = new Date();
      const permissions = DEFAULT_PERMISSIONS[role];

      const tenantUser: Omit<TenantUser, "id"> = {
        tenantId,
        userId,
        role,
        status: "active",
        permissions,
        invitedBy,
        joinedAt: now,
        lastActiveAt: now,
        metadata,
      };

      const { data, error } = await supabase
        .from("tenant_users")
        .insert([
          {
            tenant_id: tenantUser.tenantId,
            user_id: tenantUser.userId,
            role: tenantUser.role,
            status: tenantUser.status,
            permissions: tenantUser.permissions,
            invited_by: tenantUser.invitedBy,
            joined_at: tenantUser.joinedAt?.toISOString(),
            last_active_at: tenantUser.lastActiveAt?.toISOString(),
            metadata: tenantUser.metadata,
          },
        ])
        .select()
        .single();

      if (error) throw error;

      // Update tenant family size
      await this.updateTenantMetadata(tenantId, {
        familySize: await this.getTenantUserCount(tenantId),
      });

      // Log activity
      await this.logActivity(
        tenantId,
        invitedBy || userId,
        "user_added",
        "user",
        userId,
        {
          role,
          userEmail: await this.getUserEmail(userId),
        }
      );

      return {
        id: data.id,
        tenantId: data.tenant_id,
        userId: data.user_id,
        role: data.role,
        status: data.status,
        permissions: data.permissions,
        invitedBy: data.invited_by,
        invitedAt: data.invited_at ? new Date(data.invited_at) : undefined,
        joinedAt: data.joined_at ? new Date(data.joined_at) : undefined,
        lastActiveAt: data.last_active_at
          ? new Date(data.last_active_at)
          : undefined,
        metadata: data.metadata,
      };
    } catch (error) {
      console.error("Error adding user to tenant:", error);
      throw error;
    }
  }

  /**
   * Get tenant users
   */
  async getTenantUsers(tenantId: string): Promise<TenantUser[]> {
    try {
      const { data, error } = await supabase
        .from("tenant_users")
        .select(
          `
          *,
          users (id, email, name, avatar_url)
        `
        )
        .eq("tenant_id", tenantId)
        .order("joined_at", { ascending: true });

      if (error) throw error;

      return (data || []).map((item) => ({
        id: item.id,
        tenantId: item.tenant_id,
        userId: item.user_id,
        role: item.role,
        status: item.status,
        permissions: item.permissions,
        invitedBy: item.invited_by,
        invitedAt: item.invited_at ? new Date(item.invited_at) : undefined,
        joinedAt: item.joined_at ? new Date(item.joined_at) : undefined,
        lastActiveAt: item.last_active_at
          ? new Date(item.last_active_at)
          : undefined,
        metadata: item.metadata,
        user: item.users
          ? {
              id: item.users.id,
              email: item.users.email,
              name: item.users.name,
              avatarUrl: item.users.avatar_url,
            }
          : undefined,
      }));
    } catch (error) {
      console.error("Error getting tenant users:", error);
      throw error;
    }
  }

  /**
   * Create invitation
   */
  async createInvitation(
    tenantId: string,
    email: string,
    role: "admin" | "caregiver" | "patient" | "guest",
    invitedBy: string,
    metadata?: any
  ): Promise<TenantInvitation> {
    try {
      const now = new Date();
      const expiresAt = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days
      const token = uuidv4();
      const permissions = DEFAULT_PERMISSIONS[role];

      const invitation: Omit<TenantInvitation, "id"> = {
        tenantId,
        email,
        role,
        permissions,
        invitedBy,
        invitedAt: now,
        expiresAt,
        status: "pending",
        token,
        metadata,
      };

      const { data, error } = await supabase
        .from("tenant_invitations")
        .insert([
          {
            tenant_id: invitation.tenantId,
            email: invitation.email,
            role: invitation.role,
            permissions: invitation.permissions,
            invited_by: invitation.invitedBy,
            invited_at: invitation.invitedAt.toISOString(),
            expires_at: invitation.expiresAt.toISOString(),
            status: invitation.status,
            token: invitation.token,
            metadata: invitation.metadata,
          },
        ])
        .select()
        .single();

      if (error) throw error;

      // Log activity
      await this.logActivity(
        tenantId,
        invitedBy,
        "invitation_created",
        "invitation",
        data.id,
        {
          email,
          role,
        }
      );

      return {
        id: data.id,
        tenantId: data.tenant_id,
        email: data.email,
        role: data.role,
        permissions: data.permissions,
        invitedBy: data.invited_by,
        invitedAt: new Date(data.invited_at),
        expiresAt: new Date(data.expires_at),
        status: data.status,
        token: data.token,
        metadata: data.metadata,
      };
    } catch (error) {
      console.error("Error creating invitation:", error);
      throw error;
    }
  }

  /**
   * Accept invitation
   */
  async acceptInvitation(token: string, userId: string): Promise<TenantUser> {
    try {
      // Get invitation
      const { data: invitationData, error: invitationError } = await supabase
        .from("tenant_invitations")
        .select("*")
        .eq("token", token)
        .eq("status", "pending")
        .single();

      if (invitationError || !invitationData) {
        throw new Error("Invalid or expired invitation");
      }

      const invitation = invitationData;

      // Check if invitation is expired
      if (new Date() > new Date(invitation.expires_at)) {
        throw new Error("Invitation has expired");
      }

      // Add user to tenant
      const tenantUser = await this.addUserToTenant(
        invitation.tenant_id,
        userId,
        invitation.role,
        invitation.invited_by,
        invitation.metadata
      );

      // Update invitation status
      await supabase
        .from("tenant_invitations")
        .update({ status: "accepted" })
        .eq("id", invitation.id);

      // Log activity
      await this.logActivity(
        invitation.tenant_id,
        userId,
        "invitation_accepted",
        "invitation",
        invitation.id,
        {
          role: invitation.role,
        }
      );

      return tenantUser;
    } catch (error) {
      console.error("Error accepting invitation:", error);
      throw error;
    }
  }

  /**
   * Update tenant settings
   */
  async updateTenantSettings(
    tenantId: string,
    settings: Partial<TenantSettings>,
    updatedBy: string
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from("tenants")
        .update({
          settings,
          updated_at: new Date().toISOString(),
        })
        .eq("id", tenantId);

      if (error) throw error;

      // Log activity
      await this.logActivity(
        tenantId,
        updatedBy,
        "settings_updated",
        "tenant",
        tenantId,
        {
          updatedSettings: Object.keys(settings),
        }
      );
    } catch (error) {
      console.error("Error updating tenant settings:", error);
      throw error;
    }
  }

  /**
   * Check user permissions
   */
  async checkPermission(
    tenantId: string,
    userId: string,
    permission: keyof TenantUserPermissions
  ): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from("tenant_users")
        .select("permissions")
        .eq("tenant_id", tenantId)
        .eq("user_id", userId)
        .eq("status", "active")
        .single();

      if (error || !data) return false;

      return data.permissions[permission] === true;
    } catch (error) {
      console.error("Error checking permission:", error);
      return false;
    }
  }

  /**
   * Log tenant activity
   */
  async logActivity(
    tenantId: string,
    userId: string,
    action: string,
    resource: string,
    resourceId?: string,
    details?: Record<string, any>
  ): Promise<void> {
    try {
      await supabase.from("tenant_activities").insert([
        {
          tenant_id: tenantId,
          user_id: userId,
          action,
          resource,
          resource_id: resourceId,
          details,
          timestamp: new Date().toISOString(),
        },
      ]);
    } catch (error) {
      console.error("Error logging activity:", error);
      // Don't throw - activity logging shouldn't break main functionality
    }
  }

  /**
   * Helper methods
   */
  private async getTenantUserCount(tenantId: string): Promise<number> {
    const { count, error } = await supabase
      .from("tenant_users")
      .select("*", { count: "exact", head: true })
      .eq("tenant_id", tenantId)
      .eq("status", "active");

    return count || 0;
  }

  private async getUserEmail(userId: string): Promise<string> {
    const { data, error } = await supabase
      .from("users")
      .select("email")
      .eq("id", userId)
      .single();

    return data?.email || "";
  }

  private async updateTenantMetadata(
    tenantId: string,
    metadata: any
  ): Promise<void> {
    const { error } = await supabase
      .from("tenants")
      .update({
        metadata,
        updated_at: new Date().toISOString(),
      })
      .eq("id", tenantId);

    if (error) {
      console.error("Error updating tenant metadata:", error);
    }
  }
}

export const tenantService = new TenantService();
