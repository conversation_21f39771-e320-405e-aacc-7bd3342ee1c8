export interface SecuritySettings {
  // Authentication Security
  requireTwoFactor: boolean;
  sessionTimeout: number; // minutes
  maxLoginAttempts: number;
  passwordMinLength: number;
  passwordRequireSpecialChars: boolean;
  passwordRequireNumbers: boolean;
  passwordRequireUppercase: boolean;
  
  // Data Encryption
  encryptionEnabled: boolean;
  encryptionAlgorithm: 'AES-256' | 'ChaCha20-Poly1305';
  keyRotationInterval: number; // days
  
  // Privacy Controls
  dataRetentionPeriod: number; // days
  allowDataExport: boolean;
  allowDataDeletion: boolean;
  requireConsentForAnalytics: boolean;
  requireConsentForMarketing: boolean;
  
  // Audit & Monitoring
  auditLoggingEnabled: boolean;
  securityMonitoringEnabled: boolean;
  suspiciousActivityAlerts: boolean;
  dataAccessLogging: boolean;
  
  // HIPAA Compliance
  hipaaComplianceMode: boolean;
  businessAssociateAgreement: boolean;
  minimumNecessaryAccess: boolean;
  patientRightsNotification: boolean;
}

export interface AuditLog {
  id: string;
  userId: string;
  tenantId?: string;
  action: AuditAction;
  resource: string;
  resourceId?: string;
  details: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  location?: {
    country: string;
    region: string;
    city: string;
  };
  timestamp: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: AuditCategory;
  outcome: 'success' | 'failure' | 'warning';
}

export type AuditAction = 
  // Authentication
  | 'login' | 'logout' | 'login_failed' | 'password_reset' | 'account_locked'
  // Data Access
  | 'data_viewed' | 'data_created' | 'data_updated' | 'data_deleted' | 'data_exported'
  // Privacy
  | 'consent_given' | 'consent_withdrawn' | 'privacy_settings_changed'
  // Security
  | 'security_settings_changed' | 'suspicious_activity' | 'unauthorized_access'
  // Medical
  | 'medical_data_accessed' | 'medication_viewed' | 'health_record_updated'
  // System
  | 'system_error' | 'backup_created' | 'data_migration';

export type AuditCategory = 
  | 'authentication' | 'authorization' | 'data_access' | 'privacy' 
  | 'security' | 'medical' | 'system' | 'compliance';

export interface SecurityAlert {
  id: string;
  type: SecurityAlertType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  userId?: string;
  tenantId?: string;
  ipAddress?: string;
  details: Record<string, any>;
  timestamp: Date;
  status: 'active' | 'investigating' | 'resolved' | 'false_positive';
  assignedTo?: string;
  resolvedAt?: Date;
  resolution?: string;
}

export type SecurityAlertType = 
  | 'multiple_failed_logins' | 'unusual_login_location' | 'suspicious_data_access'
  | 'unauthorized_export' | 'data_breach_attempt' | 'malware_detected'
  | 'privilege_escalation' | 'account_takeover' | 'data_exfiltration';

export interface DataClassification {
  level: 'public' | 'internal' | 'confidential' | 'restricted' | 'phi'; // PHI = Protected Health Information
  categories: DataCategory[];
  retentionPeriod: number; // days
  encryptionRequired: boolean;
  accessControls: AccessControl[];
  auditRequired: boolean;
}

export type DataCategory = 
  | 'personal_info' | 'medical_info' | 'financial_info' | 'biometric_info'
  | 'conversation_data' | 'photo_metadata' | 'location_data' | 'device_info';

export interface AccessControl {
  role: string;
  permissions: Permission[];
  conditions?: AccessCondition[];
}

export type Permission = 
  | 'read' | 'write' | 'delete' | 'export' | 'share' | 'admin';

export interface AccessCondition {
  type: 'time_based' | 'location_based' | 'device_based' | 'approval_required';
  parameters: Record<string, any>;
}

export interface PrivacyConsent {
  id: string;
  userId: string;
  tenantId?: string;
  consentType: ConsentType;
  purpose: string;
  dataCategories: DataCategory[];
  granted: boolean;
  grantedAt?: Date;
  withdrawnAt?: Date;
  expiresAt?: Date;
  version: string;
  ipAddress: string;
  userAgent: string;
  evidence: ConsentEvidence;
}

export type ConsentType = 
  | 'data_processing' | 'analytics' | 'marketing' | 'research' 
  | 'third_party_sharing' | 'international_transfer';

export interface ConsentEvidence {
  method: 'explicit_opt_in' | 'checkbox' | 'digital_signature' | 'verbal_recorded';
  details: Record<string, any>;
  witnesses?: string[];
}

export interface DataSubjectRequest {
  id: string;
  userId: string;
  tenantId?: string;
  requestType: DataSubjectRequestType;
  status: 'pending' | 'processing' | 'completed' | 'rejected';
  submittedAt: Date;
  completedAt?: Date;
  requestDetails: string;
  responseData?: any;
  rejectionReason?: string;
  processedBy?: string;
  verificationMethod: 'email' | 'phone' | 'in_person' | 'government_id';
  verificationCompleted: boolean;
}

export type DataSubjectRequestType = 
  | 'access' | 'rectification' | 'erasure' | 'portability' 
  | 'restriction' | 'objection' | 'automated_decision_opt_out';

export interface EncryptionKey {
  id: string;
  algorithm: string;
  keySize: number;
  purpose: 'data_encryption' | 'communication' | 'backup' | 'archive';
  status: 'active' | 'rotating' | 'deprecated' | 'revoked';
  createdAt: Date;
  expiresAt?: Date;
  rotatedAt?: Date;
  usageCount: number;
  lastUsedAt?: Date;
}

export interface SecurityIncident {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: IncidentCategory;
  status: 'open' | 'investigating' | 'contained' | 'resolved' | 'closed';
  reportedBy: string;
  assignedTo?: string;
  affectedUsers: string[];
  affectedData: DataCategory[];
  timeline: IncidentTimelineEntry[];
  containmentActions: string[];
  rootCause?: string;
  lessonsLearned?: string;
  reportedAt: Date;
  resolvedAt?: Date;
}

export type IncidentCategory = 
  | 'data_breach' | 'unauthorized_access' | 'malware' | 'phishing'
  | 'insider_threat' | 'system_compromise' | 'data_loss' | 'service_disruption';

export interface IncidentTimelineEntry {
  timestamp: Date;
  action: string;
  performedBy: string;
  details: string;
  evidence?: string[];
}

export interface ComplianceReport {
  id: string;
  reportType: 'hipaa' | 'gdpr' | 'ccpa' | 'sox' | 'internal';
  period: {
    startDate: Date;
    endDate: Date;
  };
  generatedAt: Date;
  generatedBy: string;
  status: 'draft' | 'review' | 'approved' | 'submitted';
  findings: ComplianceFinding[];
  recommendations: string[];
  riskScore: number; // 0-100
  summary: string;
  attachments: string[];
}

export interface ComplianceFinding {
  id: string;
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  evidence: string[];
  recommendation: string;
  status: 'open' | 'in_progress' | 'resolved' | 'accepted_risk';
  dueDate?: Date;
  assignedTo?: string;
}

// Default security settings
export const DEFAULT_SECURITY_SETTINGS: SecuritySettings = {
  // Authentication Security
  requireTwoFactor: false,
  sessionTimeout: 480, // 8 hours
  maxLoginAttempts: 5,
  passwordMinLength: 8,
  passwordRequireSpecialChars: true,
  passwordRequireNumbers: true,
  passwordRequireUppercase: true,
  
  // Data Encryption
  encryptionEnabled: true,
  encryptionAlgorithm: 'AES-256',
  keyRotationInterval: 90,
  
  // Privacy Controls
  dataRetentionPeriod: 2555, // 7 years for HIPAA
  allowDataExport: true,
  allowDataDeletion: true,
  requireConsentForAnalytics: true,
  requireConsentForMarketing: true,
  
  // Audit & Monitoring
  auditLoggingEnabled: true,
  securityMonitoringEnabled: true,
  suspiciousActivityAlerts: true,
  dataAccessLogging: true,
  
  // HIPAA Compliance
  hipaaComplianceMode: true,
  businessAssociateAgreement: false,
  minimumNecessaryAccess: true,
  patientRightsNotification: true,
};

// Data classification for different types of information
export const DATA_CLASSIFICATIONS: Record<string, DataClassification> = {
  'medical_records': {
    level: 'phi',
    categories: ['medical_info', 'personal_info'],
    retentionPeriod: 2555, // 7 years
    encryptionRequired: true,
    accessControls: [
      { role: 'patient', permissions: ['read'] },
      { role: 'caregiver', permissions: ['read'], conditions: [{ type: 'approval_required', parameters: {} }] },
      { role: 'healthcare_provider', permissions: ['read', 'write'] }
    ],
    auditRequired: true
  },
  'conversation_data': {
    level: 'confidential',
    categories: ['conversation_data', 'personal_info'],
    retentionPeriod: 1095, // 3 years
    encryptionRequired: true,
    accessControls: [
      { role: 'patient', permissions: ['read', 'delete'] },
      { role: 'caregiver', permissions: ['read'], conditions: [{ type: 'approval_required', parameters: {} }] }
    ],
    auditRequired: true
  },
  'photos': {
    level: 'confidential',
    categories: ['personal_info', 'biometric_info'],
    retentionPeriod: 1095, // 3 years
    encryptionRequired: true,
    accessControls: [
      { role: 'patient', permissions: ['read', 'write', 'delete'] },
      { role: 'caregiver', permissions: ['read', 'write'] }
    ],
    auditRequired: true
  },
  'profile_data': {
    level: 'confidential',
    categories: ['personal_info'],
    retentionPeriod: 2555, // 7 years
    encryptionRequired: true,
    accessControls: [
      { role: 'patient', permissions: ['read', 'write'] },
      { role: 'caregiver', permissions: ['read'] }
    ],
    auditRequired: true
  }
};
