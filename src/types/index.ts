export interface Photo {
  id: string;
  url: string;
  filename: string;
  uploadedAt: Date;
  description?: string;
  metadata?: {
    location?: string;
    people?: string[];
    date?: string;
    event?: string;
  };
}

export interface Conversation {
  id: string;
  photoId: string;
  messages: Message[];
  startedAt: Date;
  lastActiveAt: Date;
}

export interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  audioUrl?: string;
}

export interface Medication {
  id: string;
  name: string;
  dosage: string;
  frequency: 'daily' | 'twice-daily' | 'three-times-daily' | 'as-needed';
  times: string[]; // Array of time strings like "08:00", "12:00", "18:00"
  instructions?: string;
  photoUrl?: string;
  isActive: boolean;
  createdAt: Date;
}

export interface MedicationReminder {
  id: string;
  medicationId: string;
  scheduledTime: Date;
  actualTime?: Date;
  status: 'pending' | 'taken' | 'missed' | 'skipped';
  notes?: string;
}

export interface UserProfile {
  id: string;
  name: string;
  relationship: 'self' | 'spouse' | 'child' | 'caregiver' | 'other';
  photoUrl?: string;
  preferences: {
    voiceEnabled: boolean;
    reminderVolume: number;
    conversationStyle: 'casual' | 'formal' | 'encouraging';
    language: 'en' | 'fr' | 'es';
  };
  createdAt: Date;
}

export interface CaregiverInsight {
  id: string;
  type: 'engagement' | 'medication' | 'mood' | 'activity';
  title: string;
  description: string;
  value: string | number;
  trend?: 'up' | 'down' | 'stable';
  date: Date;
  severity?: 'low' | 'medium' | 'high';
}