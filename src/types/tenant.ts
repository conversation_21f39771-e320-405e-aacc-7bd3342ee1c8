export interface Tenant {
  id: string;
  name: string;
  type: 'family' | 'facility' | 'organization';
  status: 'active' | 'inactive' | 'suspended';
  settings: TenantSettings;
  subscription?: TenantSubscription;
  createdAt: Date;
  updatedAt: Date;
  metadata?: {
    familySize?: number;
    primaryContact?: string;
    emergencyContact?: string;
    timezone?: string;
    language?: string;
    notes?: string;
  };
}

export interface TenantSettings {
  // Privacy & Security
  dataRetentionDays: number;
  allowDataExport: boolean;
  requireTwoFactor: boolean;
  allowGuestAccess: boolean;
  
  // Feature Permissions
  features: {
    aiChat: boolean;
    photoSharing: boolean;
    medicationReminders: boolean;
    caregiverDashboard: boolean;
    voiceMessages: boolean;
    videoChat: boolean;
  };
  
  // AI & Chat Settings
  aiSettings: {
    enabled: boolean;
    model: 'gpt-4' | 'claude-3' | 'local';
    maxTokens: number;
    temperature: number;
    safetyFilters: boolean;
    topicsToAvoid: string[];
    responseStyle: 'formal' | 'casual' | 'caring';
  };
  
  // Notification Settings
  notifications: {
    emailEnabled: boolean;
    smsEnabled: boolean;
    pushEnabled: boolean;
    medicationReminders: boolean;
    lowEngagementAlerts: boolean;
    emergencyAlerts: boolean;
    weeklyReports: boolean;
  };
  
  // Photo & Media Settings
  mediaSettings: {
    maxPhotoSize: number; // in MB
    allowedFormats: string[];
    autoBackup: boolean;
    faceRecognition: boolean;
    geotagging: boolean;
    qualityCompression: 'none' | 'low' | 'medium' | 'high';
  };
  
  // Caregiver Settings
  caregiverSettings: {
    allowMultipleCaregivers: boolean;
    requireApproval: boolean;
    accessLevel: 'full' | 'limited' | 'view-only';
    canInviteOthers: boolean;
    canModifySettings: boolean;
  };
}

export interface TenantSubscription {
  plan: 'free' | 'basic' | 'premium' | 'enterprise';
  status: 'active' | 'past_due' | 'canceled' | 'trialing';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  trialEnd?: Date;
  limits: {
    maxUsers: number;
    maxPhotos: number;
    maxStorageGB: number;
    aiMessagesPerMonth: number;
    supportLevel: 'community' | 'email' | 'priority' | 'dedicated';
  };
}

export interface TenantUser {
  id: string;
  tenantId: string;
  userId: string;
  role: 'owner' | 'admin' | 'caregiver' | 'patient' | 'guest';
  status: 'active' | 'invited' | 'suspended';
  permissions: TenantUserPermissions;
  invitedBy?: string;
  invitedAt?: Date;
  joinedAt?: Date;
  lastActiveAt?: Date;
  metadata?: {
    relationship?: string; // 'spouse', 'child', 'parent', 'sibling', 'friend', 'professional'
    emergencyContact?: boolean;
    primaryCaregiver?: boolean;
    notes?: string;
  };
}

export interface TenantUserPermissions {
  // Data Access
  canViewPhotos: boolean;
  canUploadPhotos: boolean;
  canDeletePhotos: boolean;
  canViewConversations: boolean;
  canParticipateInChat: boolean;
  canViewMedications: boolean;
  canManageMedications: boolean;
  
  // User Management
  canInviteUsers: boolean;
  canRemoveUsers: boolean;
  canModifyUserRoles: boolean;
  canViewUserActivity: boolean;
  
  // Settings & Configuration
  canModifyTenantSettings: boolean;
  canViewAnalytics: boolean;
  canExportData: boolean;
  canManageSubscription: boolean;
  
  // Emergency & Safety
  canReceiveEmergencyAlerts: boolean;
  canTriggerEmergencyAlerts: boolean;
  canAccessEmergencyContacts: boolean;
}

export interface TenantInvitation {
  id: string;
  tenantId: string;
  email: string;
  role: 'admin' | 'caregiver' | 'patient' | 'guest';
  permissions: TenantUserPermissions;
  invitedBy: string;
  invitedAt: Date;
  expiresAt: Date;
  status: 'pending' | 'accepted' | 'expired' | 'revoked';
  token: string;
  metadata?: {
    relationship?: string;
    personalMessage?: string;
    expectedName?: string;
  };
}

export interface TenantActivity {
  id: string;
  tenantId: string;
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  metadata?: {
    severity?: 'low' | 'medium' | 'high' | 'critical';
    category?: 'auth' | 'data' | 'settings' | 'user_management' | 'emergency';
    automated?: boolean;
  };
}

export interface TenantUsageStats {
  tenantId: string;
  period: 'daily' | 'weekly' | 'monthly';
  date: Date;
  stats: {
    activeUsers: number;
    totalLogins: number;
    photosUploaded: number;
    aiMessagesExchanged: number;
    medicationReminders: number;
    storageUsedMB: number;
    averageSessionDuration: number;
    engagementScore: number;
  };
}

// Default tenant settings
export const DEFAULT_TENANT_SETTINGS: TenantSettings = {
  dataRetentionDays: 365,
  allowDataExport: true,
  requireTwoFactor: false,
  allowGuestAccess: true,
  
  features: {
    aiChat: true,
    photoSharing: true,
    medicationReminders: true,
    caregiverDashboard: true,
    voiceMessages: true,
    videoChat: false,
  },
  
  aiSettings: {
    enabled: true,
    model: 'gpt-4',
    maxTokens: 500,
    temperature: 0.7,
    safetyFilters: true,
    topicsToAvoid: ['medical_advice', 'financial_advice'],
    responseStyle: 'caring',
  },
  
  notifications: {
    emailEnabled: true,
    smsEnabled: false,
    pushEnabled: true,
    medicationReminders: true,
    lowEngagementAlerts: true,
    emergencyAlerts: true,
    weeklyReports: true,
  },
  
  mediaSettings: {
    maxPhotoSize: 10,
    allowedFormats: ['jpg', 'jpeg', 'png', 'heic'],
    autoBackup: true,
    faceRecognition: true,
    geotagging: true,
    qualityCompression: 'medium',
  },
  
  caregiverSettings: {
    allowMultipleCaregivers: true,
    requireApproval: true,
    accessLevel: 'full',
    canInviteOthers: true,
    canModifySettings: false,
  },
};

// Default permissions by role
export const DEFAULT_PERMISSIONS: Record<string, TenantUserPermissions> = {
  owner: {
    canViewPhotos: true,
    canUploadPhotos: true,
    canDeletePhotos: true,
    canViewConversations: true,
    canParticipateInChat: true,
    canViewMedications: true,
    canManageMedications: true,
    canInviteUsers: true,
    canRemoveUsers: true,
    canModifyUserRoles: true,
    canViewUserActivity: true,
    canModifyTenantSettings: true,
    canViewAnalytics: true,
    canExportData: true,
    canManageSubscription: true,
    canReceiveEmergencyAlerts: true,
    canTriggerEmergencyAlerts: true,
    canAccessEmergencyContacts: true,
  },
  
  admin: {
    canViewPhotos: true,
    canUploadPhotos: true,
    canDeletePhotos: true,
    canViewConversations: true,
    canParticipateInChat: true,
    canViewMedications: true,
    canManageMedications: true,
    canInviteUsers: true,
    canRemoveUsers: false,
    canModifyUserRoles: false,
    canViewUserActivity: true,
    canModifyTenantSettings: false,
    canViewAnalytics: true,
    canExportData: true,
    canManageSubscription: false,
    canReceiveEmergencyAlerts: true,
    canTriggerEmergencyAlerts: true,
    canAccessEmergencyContacts: true,
  },
  
  caregiver: {
    canViewPhotos: true,
    canUploadPhotos: true,
    canDeletePhotos: false,
    canViewConversations: true,
    canParticipateInChat: true,
    canViewMedications: true,
    canManageMedications: true,
    canInviteUsers: false,
    canRemoveUsers: false,
    canModifyUserRoles: false,
    canViewUserActivity: true,
    canModifyTenantSettings: false,
    canViewAnalytics: true,
    canExportData: false,
    canManageSubscription: false,
    canReceiveEmergencyAlerts: true,
    canTriggerEmergencyAlerts: true,
    canAccessEmergencyContacts: true,
  },
  
  patient: {
    canViewPhotos: true,
    canUploadPhotos: true,
    canDeletePhotos: false,
    canViewConversations: true,
    canParticipateInChat: true,
    canViewMedications: true,
    canManageMedications: false,
    canInviteUsers: false,
    canRemoveUsers: false,
    canModifyUserRoles: false,
    canViewUserActivity: false,
    canModifyTenantSettings: false,
    canViewAnalytics: false,
    canExportData: false,
    canManageSubscription: false,
    canReceiveEmergencyAlerts: false,
    canTriggerEmergencyAlerts: true,
    canAccessEmergencyContacts: true,
  },
  
  guest: {
    canViewPhotos: true,
    canUploadPhotos: false,
    canDeletePhotos: false,
    canViewConversations: false,
    canParticipateInChat: false,
    canViewMedications: false,
    canManageMedications: false,
    canInviteUsers: false,
    canRemoveUsers: false,
    canModifyUserRoles: false,
    canViewUserActivity: false,
    canModifyTenantSettings: false,
    canViewAnalytics: false,
    canExportData: false,
    canManageSubscription: false,
    canReceiveEmergencyAlerts: false,
    canTriggerEmergencyAlerts: false,
    canAccessEmergencyContacts: false,
  },
};
