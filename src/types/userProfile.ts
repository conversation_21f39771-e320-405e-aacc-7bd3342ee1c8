export interface UserProfile {
  id: string;
  userId: string;
  tenantId?: string;
  
  // Basic Information
  personalInfo: PersonalInfo;
  contactInfo: ContactInfo;
  preferences: UserPreferences;
  medicalInfo?: MedicalInfo;
  
  // Relationships
  relationships: UserRelationship[];
  emergencyContacts: EmergencyContact[];
  
  // Settings
  privacySettings: PrivacySettings;
  notificationSettings: NotificationSettings;
  accessibilitySettings: AccessibilitySettings;
  
  // Metadata
  profileCompleteness: number; // 0-100%
  lastUpdated: Date;
  createdAt: Date;
  isActive: boolean;
}

export interface PersonalInfo {
  firstName: string;
  lastName: string;
  displayName?: string;
  dateOfBirth?: Date;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  profilePicture?: string;
  bio?: string;
  
  // Memory-specific information
  memoryStage?: 'mild' | 'moderate' | 'severe' | 'normal';
  diagnosisDate?: Date;
  favoriteMemories?: string[];
  importantPeople?: string[];
  hobbies?: string[];
  occupation?: string;
  hometown?: string;
}

export interface ContactInfo {
  primaryEmail: string;
  secondaryEmail?: string;
  primaryPhone?: string;
  secondaryPhone?: string;
  
  // Address
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  
  // Preferred contact methods
  preferredContactMethod: 'email' | 'phone' | 'sms' | 'app';
  timeZone: string;
  language: string;
}

export interface UserPreferences {
  // Interface preferences
  theme: 'light' | 'dark' | 'auto';
  fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  highContrast: boolean;
  reducedMotion: boolean;
  
  // AI conversation preferences
  conversationStyle: 'formal' | 'casual' | 'caring' | 'playful';
  topicsOfInterest: string[];
  topicsToAvoid: string[];
  reminderFrequency: 'low' | 'medium' | 'high';
  
  // Photo preferences
  autoTagging: boolean;
  faceRecognition: boolean;
  locationTagging: boolean;
  photoQuality: 'original' | 'high' | 'medium' | 'low';
  
  // Medication preferences
  medicationReminders: boolean;
  reminderSound: string;
  snoozeTime: number; // minutes
  
  // Privacy preferences
  shareDataWithFamily: boolean;
  allowAnalytics: boolean;
  shareLocationWithCaregivers: boolean;
}

export interface MedicalInfo {
  // Basic medical information
  primaryPhysician?: string;
  primaryPhysicianPhone?: string;
  medicalConditions: string[];
  allergies: string[];
  currentMedications: string[];
  
  // Memory-specific information
  memoryAssessmentScore?: number;
  lastAssessmentDate?: Date;
  cognitiveLevel: 'independent' | 'mild_assistance' | 'moderate_assistance' | 'full_assistance';
  
  // Care information
  careLevel: 'independent' | 'assisted_living' | 'memory_care' | 'skilled_nursing';
  careProvider?: string;
  insuranceInfo?: {
    provider: string;
    policyNumber: string;
    groupNumber?: string;
  };
  
  // Emergency medical information
  medicalDirectives?: string;
  emergencyMedicalInfo?: string;
}

export interface UserRelationship {
  id: string;
  relatedUserId: string;
  relationshipType: 'spouse' | 'child' | 'parent' | 'sibling' | 'friend' | 'caregiver' | 'healthcare_provider' | 'other';
  relationshipLabel?: string; // Custom label like "Daughter", "Son-in-law"
  isPrimaryCaregiver: boolean;
  canViewMedicalInfo: boolean;
  canReceiveAlerts: boolean;
  canManageMedications: boolean;
  notes?: string;
  establishedDate: Date;
  isActive: boolean;
}

export interface EmergencyContact {
  id: string;
  name: string;
  relationship: string;
  primaryPhone: string;
  secondaryPhone?: string;
  email?: string;
  address?: string;
  isPrimary: boolean;
  canMakeMedicalDecisions: boolean;
  notes?: string;
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'family_only' | 'caregivers_only' | 'private';
  sharePhotos: boolean;
  shareConversations: boolean;
  shareMedicalInfo: boolean;
  shareLocation: boolean;
  allowDataExport: boolean;
  dataRetentionPeriod: number; // days
  
  // Specific sharing permissions
  shareWithResearchers: boolean;
  shareAnonymizedData: boolean;
  allowMarketingCommunications: boolean;
}

export interface NotificationSettings {
  // App notifications
  pushNotifications: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  
  // Notification types
  medicationReminders: boolean;
  appointmentReminders: boolean;
  photoPrompts: boolean;
  conversationPrompts: boolean;
  familyUpdates: boolean;
  emergencyAlerts: boolean;
  
  // Timing preferences
  quietHoursStart?: string; // HH:MM format
  quietHoursEnd?: string;
  weekendNotifications: boolean;
  
  // Frequency settings
  dailyDigest: boolean;
  weeklyReport: boolean;
  monthlyReport: boolean;
}

export interface AccessibilitySettings {
  // Visual accessibility
  fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  highContrast: boolean;
  colorBlindSupport: boolean;
  reducedMotion: boolean;
  
  // Audio accessibility
  voiceNavigation: boolean;
  screenReader: boolean;
  audioDescriptions: boolean;
  
  // Motor accessibility
  largeButtons: boolean;
  gestureAlternatives: boolean;
  voiceControl: boolean;
  
  // Cognitive accessibility
  simplifiedInterface: boolean;
  extraConfirmations: boolean;
  slowAnimations: boolean;
  clearInstructions: boolean;
}

export interface ProfileUpdateRequest {
  section: 'personal' | 'contact' | 'preferences' | 'medical' | 'relationships' | 'privacy' | 'notifications' | 'accessibility';
  data: Partial<UserProfile>;
  updatedBy: string;
  reason?: string;
}

export interface ProfileActivity {
  id: string;
  userId: string;
  action: 'created' | 'updated' | 'viewed' | 'shared' | 'exported';
  section?: string;
  details?: Record<string, any>;
  performedBy: string;
  timestamp: Date;
  ipAddress?: string;
}

// Default settings
export const DEFAULT_USER_PREFERENCES: UserPreferences = {
  theme: 'auto',
  fontSize: 'medium',
  highContrast: false,
  reducedMotion: false,
  conversationStyle: 'caring',
  topicsOfInterest: [],
  topicsToAvoid: [],
  reminderFrequency: 'medium',
  autoTagging: true,
  faceRecognition: true,
  locationTagging: true,
  photoQuality: 'high',
  medicationReminders: true,
  reminderSound: 'gentle',
  snoozeTime: 10,
  shareDataWithFamily: true,
  allowAnalytics: true,
  shareLocationWithCaregivers: true,
};

export const DEFAULT_PRIVACY_SETTINGS: PrivacySettings = {
  profileVisibility: 'family_only',
  sharePhotos: true,
  shareConversations: false,
  shareMedicalInfo: false,
  shareLocation: false,
  allowDataExport: true,
  dataRetentionPeriod: 365,
  shareWithResearchers: false,
  shareAnonymizedData: false,
  allowMarketingCommunications: false,
};

export const DEFAULT_NOTIFICATION_SETTINGS: NotificationSettings = {
  pushNotifications: true,
  emailNotifications: true,
  smsNotifications: false,
  medicationReminders: true,
  appointmentReminders: true,
  photoPrompts: true,
  conversationPrompts: true,
  familyUpdates: true,
  emergencyAlerts: true,
  quietHoursStart: '22:00',
  quietHoursEnd: '08:00',
  weekendNotifications: true,
  dailyDigest: false,
  weeklyReport: true,
  monthlyReport: false,
};

export const DEFAULT_ACCESSIBILITY_SETTINGS: AccessibilitySettings = {
  fontSize: 'medium',
  highContrast: false,
  colorBlindSupport: false,
  reducedMotion: false,
  voiceNavigation: false,
  screenReader: false,
  audioDescriptions: false,
  largeButtons: false,
  gestureAlternatives: false,
  voiceControl: false,
  simplifiedInterface: false,
  extraConfirmations: false,
  slowAnimations: false,
  clearInstructions: false,
};
