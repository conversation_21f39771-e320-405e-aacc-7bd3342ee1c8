export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      tenants: {
        Row: {
          id: string
          name: string
          plan: 'free' | 'premium' | 'professional'
          settings: Json
          max_users: number
          allowed_languages: string[]
          features: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          plan?: 'free' | 'premium' | 'professional'
          settings?: Json
          max_users?: number
          allowed_languages?: string[]
          features?: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          plan?: 'free' | 'premium' | 'professional'
          settings?: Json
          max_users?: number
          allowed_languages?: string[]
          features?: string[]
          created_at?: string
          updated_at?: string
        }
      }
      users: {
        Row: {
          id: string
          tenant_id: string
          email: string
          name: string
          role: 'patient' | 'caregiver' | 'admin'
          avatar_url: string | null
          language: string
          preferences: Json
          is_active: boolean
          last_seen_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          tenant_id: string
          email: string
          name: string
          role: 'patient' | 'caregiver' | 'admin'
          avatar_url?: string | null
          language?: string
          preferences?: Json
          is_active?: boolean
          last_seen_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          tenant_id?: string
          email?: string
          name?: string
          role?: 'patient' | 'caregiver' | 'admin'
          avatar_url?: string | null
          language?: string
          preferences?: Json
          is_active?: boolean
          last_seen_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      patient_caregivers: {
        Row: {
          id: string
          patient_id: string
          caregiver_id: string
          relationship: string | null
          permissions: Json
          created_at: string
        }
        Insert: {
          id?: string
          patient_id: string
          caregiver_id: string
          relationship?: string | null
          permissions?: Json
          created_at?: string
        }
        Update: {
          id?: string
          patient_id?: string
          caregiver_id?: string
          relationship?: string | null
          permissions?: Json
          created_at?: string
        }
      }
      photos: {
        Row: {
          id: string
          user_id: string
          tenant_id: string
          filename: string
          url: string
          thumbnail_url: string | null
          file_size: number | null
          mime_type: string | null
          description: string | null
          metadata: Json
          tags: string[] | null
          is_favorite: boolean
          uploaded_at: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          tenant_id: string
          filename: string
          url: string
          thumbnail_url?: string | null
          file_size?: number | null
          mime_type?: string | null
          description?: string | null
          metadata?: Json
          tags?: string[] | null
          is_favorite?: boolean
          uploaded_at?: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          tenant_id?: string
          filename?: string
          url?: string
          thumbnail_url?: string | null
          file_size?: number | null
          mime_type?: string | null
          description?: string | null
          metadata?: Json
          tags?: string[] | null
          is_favorite?: boolean
          uploaded_at?: string
          created_at?: string
        }
      }
      conversations: {
        Row: {
          id: string
          user_id: string
          tenant_id: string
          photo_id: string | null
          title: string | null
          context: Json
          started_at: string
          last_active_at: string
          message_count: number
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          tenant_id: string
          photo_id?: string | null
          title?: string | null
          context?: Json
          started_at?: string
          last_active_at?: string
          message_count?: number
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          tenant_id?: string
          photo_id?: string | null
          title?: string | null
          context?: Json
          started_at?: string
          last_active_at?: string
          message_count?: number
          created_at?: string
        }
      }
      messages: {
        Row: {
          id: string
          conversation_id: string
          type: 'user' | 'assistant'
          content: string
          audio_url: string | null
          metadata: Json
          created_at: string
        }
        Insert: {
          id?: string
          conversation_id: string
          type: 'user' | 'assistant'
          content: string
          audio_url?: string | null
          metadata?: Json
          created_at?: string
        }
        Update: {
          id?: string
          conversation_id?: string
          type?: 'user' | 'assistant'
          content?: string
          audio_url?: string | null
          metadata?: Json
          created_at?: string
        }
      }
      medications: {
        Row: {
          id: string
          user_id: string
          tenant_id: string
          name: string
          dosage: string
          frequency: 'daily' | 'twice-daily' | 'three-times-daily' | 'as-needed'
          times: string[]
          instructions: string | null
          photo_url: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          tenant_id: string
          name: string
          dosage: string
          frequency: 'daily' | 'twice-daily' | 'three-times-daily' | 'as-needed'
          times: string[]
          instructions?: string | null
          photo_url?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          tenant_id?: string
          name?: string
          dosage?: string
          frequency?: 'daily' | 'twice-daily' | 'three-times-daily' | 'as-needed'
          times?: string[]
          instructions?: string | null
          photo_url?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      medication_reminders: {
        Row: {
          id: string
          medication_id: string
          user_id: string
          scheduled_time: string
          actual_time: string | null
          status: 'pending' | 'taken' | 'missed' | 'skipped'
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          medication_id: string
          user_id: string
          scheduled_time: string
          actual_time?: string | null
          status?: 'pending' | 'taken' | 'missed' | 'skipped'
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          medication_id?: string
          user_id?: string
          scheduled_time?: string
          actual_time?: string | null
          status?: 'pending' | 'taken' | 'missed' | 'skipped'
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      caregiver_insights: {
        Row: {
          id: string
          patient_id: string
          tenant_id: string
          type: 'engagement' | 'medication' | 'mood' | 'activity'
          title: string
          description: string
          value_text: string | null
          value_numeric: number | null
          trend: string | null
          severity: 'low' | 'medium' | 'high' | null
          metadata: Json
          date: string
          created_at: string
        }
        Insert: {
          id?: string
          patient_id: string
          tenant_id: string
          type: 'engagement' | 'medication' | 'mood' | 'activity'
          title: string
          description: string
          value_text?: string | null
          value_numeric?: number | null
          trend?: string | null
          severity?: 'low' | 'medium' | 'high' | null
          metadata?: Json
          date: string
          created_at?: string
        }
        Update: {
          id?: string
          patient_id?: string
          tenant_id?: string
          type?: 'engagement' | 'medication' | 'mood' | 'activity'
          title?: string
          description?: string
          value_text?: string | null
          value_numeric?: number | null
          trend?: string | null
          severity?: 'low' | 'medium' | 'high' | null
          metadata?: Json
          date?: string
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'patient' | 'caregiver' | 'admin'
      tenant_plan: 'free' | 'premium' | 'professional'
      medication_frequency: 'daily' | 'twice-daily' | 'three-times-daily' | 'as-needed'
      reminder_status: 'pending' | 'taken' | 'missed' | 'skipped'
      message_type: 'user' | 'assistant'
      insight_type: 'engagement' | 'medication' | 'mood' | 'activity'
      insight_severity: 'low' | 'medium' | 'high'
      conversation_style: 'casual' | 'formal' | 'encouraging'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
