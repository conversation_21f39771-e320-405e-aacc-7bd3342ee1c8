import React, { useState, useEffect } from "react";
import { Toaster } from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { Header } from "./components/layout/Header";
import { Navigation } from "./components/layout/Navigation";
import { WelcomeScreen } from "./components/home/<USER>";
import { PhotoManager } from "./components/photos/PhotoManager";
import { CaregiverPhotoManager } from "./components/caregiver/CaregiverPhotoManager";
import { CaregiverHomepage } from "./components/caregiver/CaregiverHomepage";
import { ChatConfiguration } from "./components/caregiver/ChatConfiguration";
import { ModernChatInterface } from "./components/chat/ModernChatInterface";
import { MedicationManager } from "./components/medications/MedicationManager";
import { CaregiverDashboard } from "./components/insights/CaregiverDashboard";
import { NotificationManager } from "./components/pwa/NotificationManager";
import { PhotoPromptNotifications } from "./components/notifications/PhotoPromptNotifications";
import { SuperAdminRoute } from "./components/admin/SuperAdminAuth";
import { SuperAdminDashboard } from "./components/admin/SuperAdminDashboard";
import { LanguageSelector } from "./components/ui/LanguageSelector";
import { AuthForm } from "./components/auth/AuthForm";
import { useLocalStorage } from "./hooks/useLocalStorage";
import { useAuthStore } from "./store/authStore";
import { useNotificationStore } from "./store/notificationStore";
import { db } from "./lib/supabase";
import type {
  Photo,
  Medication,
  MedicationReminder,
  CaregiverInsight,
} from "./types";

function App() {
  const { t } = useTranslation();
  const { user, isAuthenticated, isLoading, initialize } = useAuthStore();
  const { scheduleReminder } = useNotificationStore();
  const [activeTab, setActiveTab] = useState("home");

  // Check if user is super admin
  const isSuperAdmin = user?.role === "super_admin";
  const [hasSeenWelcome, setHasSeenWelcome] = useLocalStorage(
    "hasSeenWelcome",
    false
  );
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | undefined>();
  const [medications, setMedications] = useState<Medication[]>([]);
  const [reminders, setReminders] = useState<MedicationReminder[]>([]);
  const [insights] = useState<CaregiverInsight[]>([]);

  // Initialize auth on app start
  useEffect(() => {
    initialize();
  }, [initialize]);

  // Load user data when authenticated
  useEffect(() => {
    const loadUserData = async () => {
      if (!user) return;

      try {
        // Load photos
        const { data: photosData, error: photosError } = await db.getPhotos(
          user.id
        );
        if (!photosError && photosData) {
          const formattedPhotos: Photo[] = photosData.map((photo) => ({
            id: photo.id,
            url: photo.url,
            filename: photo.filename,
            uploadedAt: new Date(photo.uploaded_at),
            description: photo.description || undefined,
            metadata: photo.metadata as Photo["metadata"],
          }));
          setPhotos(formattedPhotos);
        }

        // Load medications
        const { data: medicationsData, error: medicationsError } =
          await db.getMedications(user.id);
        if (!medicationsError && medicationsData) {
          const formattedMedications: Medication[] = medicationsData.map(
            (med) => ({
              id: med.id,
              name: med.name,
              dosage: med.dosage,
              frequency: med.frequency as Medication["frequency"],
              times: med.times,
              instructions: med.instructions || undefined,
              photoUrl: med.photo_url || undefined,
              isActive: med.is_active,
              createdAt: new Date(med.created_at),
            })
          );
          setMedications(formattedMedications);
        }

        // Load today's reminders
        const today = new Date().toISOString().split("T")[0];
        const { data: remindersData, error: remindersError } =
          await db.getReminders(user.id, today);
        if (!remindersError && remindersData) {
          const formattedReminders: MedicationReminder[] = remindersData.map(
            (reminder) => ({
              id: reminder.id,
              medicationId: reminder.medication_id,
              scheduledTime: new Date(reminder.scheduled_time),
              actualTime: reminder.actual_time
                ? new Date(reminder.actual_time)
                : undefined,
              status: reminder.status as MedicationReminder["status"],
              notes: reminder.notes || undefined,
            })
          );
          setReminders(formattedReminders);
        }
      } catch (error) {
        console.error("Failed to load user data:", error);
      }
    };

    if (isAuthenticated && user) {
      loadUserData();
    }
  }, [isAuthenticated, user]);

  // Generate medication reminders for active medications
  useEffect(() => {
    const generateReminders = () => {
      const today = new Date();
      const newReminders: MedicationReminder[] = [];

      medications.forEach((medication) => {
        if (!medication.isActive) return;

        medication.times.forEach((timeString) => {
          const [hours, minutes] = timeString.split(":").map(Number);
          const reminderTime = new Date(today);
          reminderTime.setHours(hours, minutes, 0, 0);

          // Create reminders for today and next 7 days
          for (let day = 0; day < 7; day++) {
            const scheduleDate = new Date(reminderTime);
            scheduleDate.setDate(scheduleDate.getDate() + day);

            const existingReminder = reminders.find(
              (r) =>
                r.medicationId === medication.id &&
                r.scheduledTime.getTime() === scheduleDate.getTime()
            );

            if (!existingReminder) {
              const reminder: MedicationReminder = {
                id: `reminder-${medication.id}-${scheduleDate.getTime()}`,
                medicationId: medication.id,
                scheduledTime: scheduleDate,
                status: scheduleDate < today ? "missed" : "pending",
              };

              newReminders.push(reminder);

              // Schedule notification
              if (reminder.status === "pending") {
                scheduleReminder(
                  `Time for ${medication.name}`,
                  `Don't forget to take your ${medication.dosage}`,
                  scheduleDate
                );
              }
            }
          }
        });
      });

      if (newReminders.length > 0) {
        setReminders((prev) => [...prev, ...newReminders]);
      }
    };

    if (medications.length > 0) {
      generateReminders();
    }
  }, [medications, setReminders, reminders, scheduleReminder]);

  // Listen for navigation events from photo cards
  useEffect(() => {
    const handleNavigateToChat = (event: CustomEvent) => {
      const { photoId, photoUrl } = event.detail;
      console.log("Navigating to chat with photo:", { photoId, photoUrl });
      setActiveTab("chat");
      // Find and set the selected photo
      const photo = photos.find((p) => p.id === photoId);
      if (photo) {
        setSelectedPhoto(photo);
      }
    };

    window.addEventListener(
      "navigate-to-chat",
      handleNavigateToChat as EventListener
    );

    return () => {
      window.removeEventListener(
        "navigate-to-chat",
        handleNavigateToChat as EventListener
      );
    };
  }, [photos]);

  // Listen for tab change events from notifications
  useEffect(() => {
    const handleChangeTab = (event: CustomEvent) => {
      const tab = event.detail;
      console.log("Changing tab to:", tab);
      setActiveTab(tab);
    };

    window.addEventListener("change-tab", handleChangeTab as EventListener);

    return () => {
      window.removeEventListener(
        "change-tab",
        handleChangeTab as EventListener
      );
    };
  }, []);

  const handleGetStarted = () => {
    setHasSeenWelcome(true);
    setActiveTab("photos");
  };

  const handlePhotosUploaded = (newPhotos: Photo[]) => {
    setPhotos(newPhotos);
  };

  const handleAddMedication = (medication: Medication) => {
    setMedications((prev) => [...prev, medication]);
  };

  const handleUpdateReminder = (updatedReminder: MedicationReminder) => {
    setReminders((prev) =>
      prev.map((r) => (r.id === updatedReminder.id ? updatedReminder : r))
    );
  };

  const renderContent = () => {
    switch (activeTab) {
      case "home":
        if (user?.role === "caregiver") {
          return (
            <CaregiverHomepage
              photos={photos}
              medications={medications}
              reminders={reminders}
              insights={insights}
              patientName={user?.name || "Patient"}
              onNavigate={setActiveTab}
            />
          );
        }

        if (!hasSeenWelcome) {
          return (
            <WelcomeScreen
              userName={user?.name || "Friend"}
              onGetStarted={handleGetStarted}
              onNavigate={setActiveTab}
            />
          );
        }
        return (
          <WelcomeScreen
            userName={user?.name || "Friend"}
            onGetStarted={() => setActiveTab("photos")}
            onNavigate={setActiveTab}
          />
        );
      case "photos":
        return user?.role === "caregiver" ? (
          <CaregiverPhotoManager
            photos={photos}
            onPhotosUploaded={handlePhotosUploaded}
            insights={insights}
            patientName={user?.name || "Patient"}
          />
        ) : (
          <div className="p-4 pb-24">
            <PhotoManager
              photos={photos}
              onPhotosUploaded={handlePhotosUploaded}
              userRole={user?.role || "patient"}
              showUpload={true}
            />
          </div>
        );
      case "chat":
        return user?.role === "caregiver" ? (
          <div className="p-4 pb-24">
            <ChatConfiguration patientName={user?.name || "Patient"} />
          </div>
        ) : (
          <div className="pb-20 h-screen">
            <ModernChatInterface
              selectedPhoto={selectedPhoto}
              photos={photos}
              onPhotoSelect={setSelectedPhoto}
            />
          </div>
        );
      case "medications":
        return (
          <div className="p-4 pb-24">
            <MedicationManager
              medications={medications}
              reminders={reminders}
              onAddMedication={handleAddMedication}
              onUpdateReminder={handleUpdateReminder}
            />
          </div>
        );
      case "insights":
        return (
          <div className="p-4 pb-24">
            <CaregiverDashboard insights={insights} />
          </div>
        );
      default:
        return null;
    }
  };

  // Show loading screen while initializing
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show auth form if not authenticated
  if (!isAuthenticated || !user) {
    return <AuthForm onSuccess={() => setHasSeenWelcome(true)} />;
  }

  // Show super admin portal if user is super admin
  if (isSuperAdmin) {
    return (
      <SuperAdminRoute>
        <SuperAdminDashboard />
      </SuperAdminRoute>
    );
  }

  return (
    <div
      className="min-h-screen bg-gray-50"
      dir={user?.language === "ar" ? "rtl" : "ltr"}
    >
      <NotificationManager />
      <PhotoPromptNotifications photos={photos} />
      <Toaster
        position="top-center"
        toastOptions={{
          duration: 4000,
          style: {
            background: "#363636",
            color: "#fff",
          },
        }}
      />

      <Header
        userName={user?.name || "Friend"}
        onMenuClick={() => {}}
        onProfileClick={() => {}}
        onSettingsClick={() => {}}
      >
        <div className="flex items-center gap-2">
          <LanguageSelector />
          <button
            onClick={async () => {
              const { signOut } = useAuthStore.getState();
              await signOut();
              setHasSeenWelcome(false);
            }}
            className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
          >
            Logout
          </button>
        </div>
      </Header>

      <main className="pt-0">{renderContent()}</main>

      <Navigation activeTab={activeTab} onTabChange={setActiveTab} />
    </div>
  );
}

export default App;
