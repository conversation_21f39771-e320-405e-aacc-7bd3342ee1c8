import React, { useState, useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { Header } from './components/layout/Header';
import { Navigation } from './components/layout/Navigation';
import { WelcomeScreen } from './components/home/<USER>';
import { PhotoUpload } from './components/photos/PhotoUpload';
import { ModernChatInterface } from './components/chat/ModernChatInterface';
import { MedicationManager } from './components/medications/MedicationManager';
import { CaregiverDashboard } from './components/insights/CaregiverDashboard';
import { NotificationManager } from './components/pwa/NotificationManager';
import { LanguageSelector } from './components/ui/LanguageSelector';
import { useLocalStorage } from './hooks/useLocalStorage';
import { useAuthStore } from './store/authStore';
import { useNotificationStore } from './store/notificationStore';
import type { Photo, Medication, MedicationReminder, CaregiverInsight } from './types';

function App() {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const { scheduleReminder } = useNotificationStore();
  const [activeTab, setActiveTab] = useState('home');
  const [hasSeenWelcome, setHasSeenWelcome] = useLocalStorage('hasSeenWelcome', false);
  const [photos, setPhotos] = useLocalStorage<Photo[]>('photos', []);
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | undefined>();
  const [medications, setMedications] = useLocalStorage<Medication[]>('medications', []);
  const [reminders, setReminders] = useLocalStorage<MedicationReminder[]>('reminders', []);
  const [insights] = useState<CaregiverInsight[]>([]);

  // Generate medication reminders for active medications
  useEffect(() => {
    const generateReminders = () => {
      const today = new Date();
      const newReminders: MedicationReminder[] = [];

      medications.forEach(medication => {
        if (!medication.isActive) return;

        medication.times.forEach(timeString => {
          const [hours, minutes] = timeString.split(':').map(Number);
          const reminderTime = new Date(today);
          reminderTime.setHours(hours, minutes, 0, 0);

          // Create reminders for today and next 7 days
          for (let day = 0; day < 7; day++) {
            const scheduleDate = new Date(reminderTime);
            scheduleDate.setDate(scheduleDate.getDate() + day);

            const existingReminder = reminders.find(r => 
              r.medicationId === medication.id &&
              r.scheduledTime.getTime() === scheduleDate.getTime()
            );

            if (!existingReminder) {
              const reminder: MedicationReminder = {
                id: `reminder-${medication.id}-${scheduleDate.getTime()}`,
                medicationId: medication.id,
                scheduledTime: scheduleDate,
                status: scheduleDate < today ? 'missed' : 'pending',
              };
              
              newReminders.push(reminder);
              
              // Schedule notification
              if (reminder.status === 'pending') {
                scheduleReminder(
                  `Time for ${medication.name}`,
                  `Don't forget to take your ${medication.dosage}`,
                  scheduleDate
                );
              }
            }
          }
        });
      });

      if (newReminders.length > 0) {
        setReminders(prev => [...prev, ...newReminders]);
      }
    };

    if (medications.length > 0) {
      generateReminders();
    }
  }, [medications, setReminders, reminders, scheduleReminder]);

  const handleGetStarted = () => {
    setHasSeenWelcome(true);
    setActiveTab('photos');
  };

  const handlePhotosUploaded = (newPhotos: Photo[]) => {
    setPhotos(newPhotos);
  };

  const handleAddMedication = (medication: Medication) => {
    setMedications(prev => [...prev, medication]);
  };

  const handleUpdateReminder = (updatedReminder: MedicationReminder) => {
    setReminders(prev => 
      prev.map(r => r.id === updatedReminder.id ? updatedReminder : r)
    );
  };

  const renderContent = () => {
    if (!hasSeenWelcome) {
      return (
        <WelcomeScreen
          userName={user?.name || "Friend"}
          onGetStarted={handleGetStarted}
          onNavigate={setActiveTab}
        />
      );
    }

    switch (activeTab) {
      case 'home':
        return (
          <WelcomeScreen
            userName={user?.name || "Friend"}
            onGetStarted={() => setActiveTab('photos')}
            onNavigate={setActiveTab}
          />
        );
      case 'photos':
        return (
          <div className="p-4 pb-24">
            <PhotoUpload
              onPhotosUploaded={handlePhotosUploaded}
              existingPhotos={photos}
            />
          </div>
        );
      case 'chat':
        return (
          <div className="pb-20 h-screen">
            <ModernChatInterface
              selectedPhoto={selectedPhoto}
              photos={photos}
              onPhotoSelect={setSelectedPhoto}
            />
          </div>
        );
      case 'medications':
        return (
          <div className="p-4 pb-24">
            <MedicationManager
              medications={medications}
              reminders={reminders}
              onAddMedication={handleAddMedication}
              onUpdateReminder={handleUpdateReminder}
            />
          </div>
        );
      case 'insights':
        return (
          <div className="p-4 pb-24">
            <CaregiverDashboard insights={insights} />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50" dir={user?.language === 'ar' ? 'rtl' : 'ltr'}>
      <NotificationManager />
      <Toaster 
        position="top-center"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />
      
      {hasSeenWelcome && (
        <Header
          userName={user?.name || "Friend"}
          onMenuClick={() => {}}
          onProfileClick={() => {}}
          onSettingsClick={() => {}}
        >
          <LanguageSelector />
        </Header>
      )}
      
      <main className={hasSeenWelcome ? 'pt-0' : ''}>
        {renderContent()}
      </main>
      
      {hasSeenWelcome && (
        <Navigation
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      )}
    </div>
  );
}

export default App;