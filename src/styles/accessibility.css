/* Accessibility Styles for MemoCare */

/* Base font size variable */
:root {
  --base-font-size: 16px;
  --button-min-height: 44px;
  --touch-target-size: 44px;
  --focus-ring-color: #3b82f6;
  --focus-ring-width: 2px;
  --animation-duration: 0.2s;
}

/* Font size scaling */
html {
  font-size: var(--base-font-size);
}

/* High contrast mode */
.high-contrast {
  --tw-bg-white: #000000;
  --tw-bg-gray-50: #1a1a1a;
  --tw-bg-gray-100: #2d2d2d;
  --tw-bg-gray-200: #404040;
  --tw-text-gray-900: #ffffff;
  --tw-text-gray-800: #f0f0f0;
  --tw-text-gray-700: #e0e0e0;
  --tw-text-gray-600: #d0d0d0;
  --tw-text-gray-500: #c0c0c0;
  --tw-border-gray-300: #606060;
  --tw-border-gray-200: #505050;
  
  /* Ensure sufficient contrast */
  filter: contrast(1.5);
}

.high-contrast button,
.high-contrast input,
.high-contrast select,
.high-contrast textarea {
  border: 2px solid #ffffff !important;
  background-color: #000000 !important;
  color: #ffffff !important;
}

.high-contrast button:hover {
  background-color: #333333 !important;
}

.high-contrast .bg-primary-600 {
  background-color: #0066ff !important;
}

.high-contrast .text-primary-600 {
  color: #66b3ff !important;
}

/* Color blind support */
.color-blind-support {
  /* Use patterns and shapes in addition to colors */
}

.color-blind-support .text-red-600 {
  text-decoration: underline;
  font-weight: bold;
}

.color-blind-support .text-green-600 {
  font-style: italic;
  font-weight: bold;
}

.color-blind-support .text-yellow-600 {
  text-decoration: overline;
  font-weight: bold;
}

/* Reduced motion */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Large buttons */
.large-buttons button,
.large-buttons .btn,
.large-buttons input[type="button"],
.large-buttons input[type="submit"] {
  min-height: 56px !important;
  min-width: 56px !important;
  padding: 16px 24px !important;
  font-size: 1.125rem !important;
  border-radius: 12px !important;
}

.large-buttons .navigation button {
  min-height: 64px !important;
  padding: 20px 16px !important;
}

.large-buttons input,
.large-buttons select,
.large-buttons textarea {
  min-height: 56px !important;
  padding: 16px !important;
  font-size: 1.125rem !important;
}

/* Simplified interface */
.simplified-interface .hidden-simple {
  display: none !important;
}

.simplified-interface .complex-feature {
  display: none !important;
}

.simplified-interface .card {
  border: 2px solid #d1d5db !important;
  box-shadow: none !important;
}

.simplified-interface .gradient {
  background: #f9fafb !important;
}

/* Slow animations */
.slow-animations * {
  animation-duration: calc(var(--animation-duration) * 3) !important;
  transition-duration: calc(var(--animation-duration) * 2) !important;
}

/* Clear instructions */
.clear-instructions .instruction-text {
  display: block !important;
  font-size: 0.875rem !important;
  color: #6b7280 !important;
  margin-top: 4px !important;
}

.clear-instructions .help-icon {
  display: inline-block !important;
}

/* Focus management */
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus,
[tabindex]:focus {
  outline: var(--focus-ring-width) solid var(--focus-ring-color) !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 var(--focus-ring-width) var(--focus-ring-color) !important;
}

/* Skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
  border-radius: 4px;
}

.skip-link:focus {
  top: 6px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Touch targets */
@media (pointer: coarse) {
  button,
  input,
  select,
  textarea,
  a,
  [role="button"] {
    min-height: var(--touch-target-size);
    min-width: var(--touch-target-size);
  }
}

/* Voice navigation indicator */
.voice-listening {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #ef4444;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  z-index: 1000;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Error and success states with better visibility */
.error-state {
  border: 2px solid #ef4444 !important;
  background-color: #fef2f2 !important;
}

.success-state {
  border: 2px solid #10b981 !important;
  background-color: #f0fdf4 !important;
}

/* Loading states */
.loading-accessible {
  position: relative;
}

.loading-accessible::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #d1d5db;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* High contrast focus indicators */
.high-contrast button:focus,
.high-contrast input:focus,
.high-contrast select:focus,
.high-contrast textarea:focus {
  outline: 3px solid #ffff00 !important;
  outline-offset: 2px !important;
}

/* Keyboard navigation hints */
.keyboard-hint {
  position: absolute;
  bottom: -20px;
  left: 0;
  font-size: 0.75rem;
  color: #6b7280;
  opacity: 0;
  transition: opacity 0.2s;
}

button:focus .keyboard-hint,
input:focus .keyboard-hint {
  opacity: 1;
}

/* Print styles for accessibility */
@media print {
  .no-print {
    display: none !important;
  }
  
  * {
    background: white !important;
    color: black !important;
  }
  
  a {
    text-decoration: underline !important;
  }
}

/* Responsive text scaling */
@media (max-width: 640px) {
  .large-buttons {
    font-size: 1.25rem !important;
  }
  
  .large-buttons button,
  .large-buttons input {
    min-height: 60px !important;
    padding: 20px !important;
  }
}

/* Motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast preferences */
@media (prefers-contrast: high) {
  :root {
    --focus-ring-width: 3px;
  }
  
  button,
  input,
  select,
  textarea {
    border-width: 2px !important;
  }
}

/* Color scheme preferences */
@media (prefers-color-scheme: dark) {
  .auto-dark {
    background-color: #1f2937;
    color: #f9fafb;
  }
}

/* Tooltip accessibility */
[data-tooltip] {
  position: relative;
}

[data-tooltip]:hover::after,
[data-tooltip]:focus::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: #f9fafb;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.875rem;
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: 8px;
}

/* Error announcements for screen readers */
.error-announcement {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Live region for dynamic content */
.live-region {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}
