import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Database, 
  ExternalLink, 
  Copy, 
  Check, 
  AlertCircle,
  RefreshCw,
  BookOpen
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import toast from 'react-hot-toast';

interface DatabaseSetupPromptProps {
  onRetry: () => void;
  onSkip: () => void;
}

export const DatabaseSetupPrompt: React.FC<DatabaseSetupPromptProps> = ({
  onRetry,
  onSkip
}) => {
  const [copied, setCopied] = useState(false);

  const migrationSQL = `-- MemoCare Tenant Management System
-- Copy this SQL and run it in your Supabase SQL Editor

-- Create tenants table
CREATE TABLE IF NOT EXISTS tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  type VARCHAR(50) NOT NULL CHECK (type IN ('family', 'facility', 'organization')),
  status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
  settings JSONB NOT NULL DEFAULT '{}',
  subscription JSONB,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create tenant_users table
CREATE TABLE IF NOT EXISTS tenant_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role VARCHAR(50) NOT NULL CHECK (role IN ('owner', 'admin', 'caregiver', 'patient', 'guest')),
  status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'invited', 'suspended')),
  permissions JSONB NOT NULL DEFAULT '{}',
  invited_by UUID REFERENCES auth.users(id),
  invited_at TIMESTAMP WITH TIME ZONE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tenant_id, user_id)
);

-- Enable Row Level Security
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_users ENABLE ROW LEVEL SECURITY;

-- Create basic policies
CREATE POLICY "Users can view their tenants" ON tenants
  FOR SELECT USING (
    id IN (
      SELECT tenant_id FROM tenant_users 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Users can view tenant users in their tenants" ON tenant_users
  FOR SELECT USING (
    tenant_id IN (
      SELECT tenant_id FROM tenant_users 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );`;

  const handleCopySQL = async () => {
    try {
      await navigator.clipboard.writeText(migrationSQL);
      setCopied(true);
      toast.success('SQL copied to clipboard!');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy SQL');
    }
  };

  const openSupabaseDocs = () => {
    window.open('https://supabase.com/docs/guides/database/sql-editor', '_blank');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <Database className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Database Setup Required</h2>
              <p className="text-gray-600">Set up the tenant management system to enable family groups</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          <div className="space-y-6">
            {/* Alert */}
            <div className="flex items-start gap-3 p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <AlertCircle className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="font-medium text-amber-800">Quick Setup Needed</h3>
                <p className="text-sm text-amber-700 mt-1">
                  To enable family groups and multi-user features, run this SQL migration in your Supabase dashboard.
                </p>
              </div>
            </div>

            {/* Steps */}
            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900">Setup Steps:</h3>
              
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">
                    1
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Copy the SQL migration</p>
                    <p className="text-sm text-gray-600">Click the copy button below to copy the SQL to your clipboard</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">
                    2
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Open Supabase SQL Editor</p>
                    <p className="text-sm text-gray-600">Go to your Supabase project dashboard → SQL Editor</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">
                    3
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Run the migration</p>
                    <p className="text-sm text-gray-600">Paste the SQL and click "Run" to create the tables</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">
                    4
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Refresh the app</p>
                    <p className="text-sm text-gray-600">Come back here and click "Try Again" to enable family groups</p>
                  </div>
                </div>
              </div>
            </div>

            {/* SQL Code Block */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-gray-900">SQL Migration Code:</h3>
                <Button
                  variant="outline"
                  size="sm"
                  icon={copied ? Check : Copy}
                  onClick={handleCopySQL}
                  className={copied ? 'text-green-600 border-green-300' : ''}
                >
                  {copied ? 'Copied!' : 'Copy SQL'}
                </Button>
              </div>
              
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                <pre className="text-sm">
                  <code>{migrationSQL}</code>
                </pre>
              </div>
            </div>

            {/* Help Links */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Need Help?</h4>
              <div className="space-y-2">
                <button
                  onClick={openSupabaseDocs}
                  className="flex items-center gap-2 text-sm text-blue-700 hover:text-blue-800"
                >
                  <BookOpen className="w-4 h-4" />
                  Supabase SQL Editor Documentation
                  <ExternalLink className="w-3 h-3" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              This setup only needs to be done once per Supabase project
            </div>
            
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={onSkip}
              >
                Skip for Now
              </Button>
              <Button
                variant="primary"
                icon={RefreshCw}
                onClick={onRetry}
              >
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};
