import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Users, 
  Plus, 
  ChevronDown, 
  Home, 
  Building, 
  Heart,
  Check,
  Settings,
  LogOut
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { useAuthStore } from '../../store/authStore';
import { tenantService } from '../../services/tenantService';
import type { Tenant } from '../../types/tenant';
import toast from 'react-hot-toast';

interface TenantSelectorProps {
  onTenantSelect: (tenant: Tenant) => void;
  onCreateTenant: () => void;
  currentTenant?: Tenant;
}

export const TenantSelector: React.FC<TenantSelectorProps> = ({
  onTenantSelect,
  onCreateTenant,
  currentTenant
}) => {
  const { user } = useAuthStore();
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user?.id) {
      loadUserTenants();
    }
  }, [user?.id]);

  const loadUserTenants = async () => {
    if (!user?.id) return;
    
    try {
      setIsLoading(true);
      const userTenants = await tenantService.getUserTenants(user.id);
      setTenants(userTenants);
      
      // Auto-select first tenant if none selected
      if (userTenants.length > 0 && !currentTenant) {
        onTenantSelect(userTenants[0]);
      }
    } catch (error) {
      console.error('Error loading tenants:', error);
      toast.error('Failed to load family groups');
    } finally {
      setIsLoading(false);
    }
  };

  const getTenantIcon = (type: string) => {
    switch (type) {
      case 'family': return Home;
      case 'facility': return Building;
      case 'organization': return Users;
      default: return Heart;
    }
  };

  const getTenantTypeLabel = (type: string) => {
    switch (type) {
      case 'family': return 'Family';
      case 'facility': return 'Care Facility';
      case 'organization': return 'Organization';
      default: return 'Group';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 bg-gray-100 rounded-lg animate-pulse">
        <div className="w-5 h-5 bg-gray-300 rounded"></div>
        <div className="w-24 h-4 bg-gray-300 rounded"></div>
      </div>
    );
  }

  if (tenants.length === 0) {
    return (
      <Button
        variant="outline"
        icon={Plus}
        onClick={onCreateTenant}
        className="text-sm"
      >
        Create Family Group
      </Button>
    );
  }

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
      >
        {currentTenant ? (
          <>
            {React.createElement(getTenantIcon(currentTenant.type), { 
              className: "w-4 h-4 text-gray-600" 
            })}
            <span className="text-sm font-medium text-gray-900 truncate max-w-32">
              {currentTenant.name}
            </span>
          </>
        ) : (
          <>
            <Users className="w-4 h-4 text-gray-600" />
            <span className="text-sm text-gray-600">Select Group</span>
          </>
        )}
        <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            
            {/* Dropdown */}
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute top-full left-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
            >
              <div className="p-3 border-b border-gray-100">
                <h3 className="font-medium text-gray-900">Family Groups</h3>
                <p className="text-xs text-gray-600 mt-1">
                  Switch between your family groups
                </p>
              </div>

              <div className="max-h-64 overflow-y-auto">
                {tenants.map((tenant) => {
                  const Icon = getTenantIcon(tenant.type);
                  const isSelected = currentTenant?.id === tenant.id;
                  
                  return (
                    <button
                      key={tenant.id}
                      onClick={() => {
                        onTenantSelect(tenant);
                        setIsOpen(false);
                      }}
                      className={`
                        w-full flex items-center gap-3 px-3 py-3 text-left hover:bg-gray-50 transition-colors
                        ${isSelected ? 'bg-primary-50 border-r-2 border-primary-500' : ''}
                      `}
                    >
                      <div className={`
                        w-8 h-8 rounded-full flex items-center justify-center
                        ${isSelected ? 'bg-primary-100' : 'bg-gray-100'}
                      `}>
                        <Icon className={`w-4 h-4 ${isSelected ? 'text-primary-600' : 'text-gray-600'}`} />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h4 className={`font-medium truncate ${isSelected ? 'text-primary-900' : 'text-gray-900'}`}>
                            {tenant.name}
                          </h4>
                          {isSelected && (
                            <Check className="w-4 h-4 text-primary-600 flex-shrink-0" />
                          )}
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-gray-500">
                            {getTenantTypeLabel(tenant.type)}
                          </span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-500">
                            {tenant.metadata?.familySize || 1} member{(tenant.metadata?.familySize || 1) !== 1 ? 's' : ''}
                          </span>
                        </div>
                      </div>
                    </button>
                  );
                })}
              </div>

              <div className="p-3 border-t border-gray-100 space-y-2">
                <Button
                  variant="outline"
                  icon={Plus}
                  onClick={() => {
                    onCreateTenant();
                    setIsOpen(false);
                  }}
                  fullWidth
                  size="sm"
                >
                  Create New Group
                </Button>
                
                {currentTenant && (
                  <Button
                    variant="ghost"
                    icon={Settings}
                    onClick={() => {
                      // Handle tenant settings
                      setIsOpen(false);
                    }}
                    fullWidth
                    size="sm"
                    className="text-gray-600"
                  >
                    Group Settings
                  </Button>
                )}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};
