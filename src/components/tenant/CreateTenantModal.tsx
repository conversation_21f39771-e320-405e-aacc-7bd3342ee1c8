import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  X, 
  Home, 
  Building, 
  Users, 
  Heart,
  ArrowRight,
  Check
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { useAuthStore } from '../../store/authStore';
import { tenantService } from '../../services/tenantService';
import type { Tenant } from '../../types/tenant';
import toast from 'react-hot-toast';

interface CreateTenantModalProps {
  onClose: () => void;
  onTenantCreated: (tenant: Tenant) => void;
}

type TenantType = 'family' | 'facility' | 'organization';

interface TenantTypeOption {
  id: TenantType;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  features: string[];
  recommended?: boolean;
}

const TENANT_TYPES: TenantTypeOption[] = [
  {
    id: 'family',
    name: 'Family Group',
    description: 'Perfect for families caring for a loved one with memory challenges',
    icon: Home,
    features: [
      'Family photo sharing',
      'Medication reminders',
      'Caregiver coordination',
      'Memory conversations',
      'Emergency contacts'
    ],
    recommended: true
  },
  {
    id: 'facility',
    name: 'Care Facility',
    description: 'Designed for assisted living facilities and memory care centers',
    icon: Building,
    features: [
      'Multiple patient management',
      'Staff coordination',
      'Family communication',
      'Activity tracking',
      'Compliance reporting'
    ]
  },
  {
    id: 'organization',
    name: 'Organization',
    description: 'For healthcare organizations and support groups',
    icon: Users,
    features: [
      'Multi-group management',
      'Advanced analytics',
      'Custom workflows',
      'Integration support',
      'Enterprise security'
    ]
  }
];

export const CreateTenantModal: React.FC<CreateTenantModalProps> = ({
  onClose,
  onTenantCreated
}) => {
  const { user } = useAuthStore();
  const [step, setStep] = useState<'type' | 'details' | 'creating'>('type');
  const [selectedType, setSelectedType] = useState<TenantType>('family');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    primaryContact: '',
    emergencyContact: '',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  });
  const [isCreating, setIsCreating] = useState(false);

  const handleTypeSelect = (type: TenantType) => {
    setSelectedType(type);
    setStep('details');
  };

  const handleCreateTenant = async () => {
    if (!user?.id || !formData.name.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setIsCreating(true);
      setStep('creating');

      const tenant = await tenantService.createTenant(
        formData.name.trim(),
        selectedType,
        user.id,
        {
          // Custom settings based on type
          features: {
            aiChat: true,
            photoSharing: true,
            medicationReminders: selectedType !== 'organization',
            caregiverDashboard: true,
            voiceMessages: true,
            videoChat: selectedType === 'facility'
          },
          notifications: {
            emailEnabled: true,
            smsEnabled: selectedType === 'facility',
            pushEnabled: true,
            medicationReminders: selectedType !== 'organization',
            lowEngagementAlerts: true,
            emergencyAlerts: true,
            weeklyReports: selectedType !== 'family'
          }
        }
      );

      // Update tenant metadata with additional info
      if (formData.description || formData.primaryContact || formData.emergencyContact) {
        await tenantService.updateTenantSettings(tenant.id, {
          ...tenant.settings
        }, user.id);
      }

      toast.success(`${TENANT_TYPES.find(t => t.id === selectedType)?.name} created successfully!`);
      onTenantCreated(tenant);
      onClose();
    } catch (error) {
      console.error('Error creating tenant:', error);
      toast.error('Failed to create group. Please try again.');
      setStep('details');
    } finally {
      setIsCreating(false);
    }
  };

  const renderTypeSelection = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Create Your Group</h2>
        <p className="text-gray-600">
          Choose the type of group that best fits your needs
        </p>
      </div>

      <div className="space-y-4">
        {TENANT_TYPES.map((type) => {
          const Icon = type.icon;
          
          return (
            <motion.button
              key={type.id}
              onClick={() => handleTypeSelect(type.id)}
              className="w-full text-left"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card className={`p-6 border-2 transition-all hover:border-primary-300 hover:shadow-md ${
                type.recommended ? 'border-primary-200 bg-primary-50' : 'border-gray-200'
              }`}>
                <div className="flex items-start gap-4">
                  <div className={`
                    w-12 h-12 rounded-lg flex items-center justify-center
                    ${type.recommended ? 'bg-primary-100' : 'bg-gray-100'}
                  `}>
                    <Icon className={`w-6 h-6 ${type.recommended ? 'text-primary-600' : 'text-gray-600'}`} />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{type.name}</h3>
                      {type.recommended && (
                        <span className="px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                          Recommended
                        </span>
                      )}
                    </div>
                    
                    <p className="text-gray-600 mb-3">{type.description}</p>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {type.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <Check className="w-4 h-4 text-green-600 flex-shrink-0" />
                          <span className="text-sm text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <ArrowRight className="w-5 h-5 text-gray-400" />
                </div>
              </Card>
            </motion.button>
          );
        })}
      </div>
    </div>
  );

  const renderDetailsForm = () => {
    const selectedTypeInfo = TENANT_TYPES.find(t => t.id === selectedType)!;
    const Icon = selectedTypeInfo.icon;

    return (
      <div className="space-y-6">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon className="w-8 h-8 text-primary-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Set Up Your {selectedTypeInfo.name}
          </h2>
          <p className="text-gray-600">
            Provide some basic information to get started
          </p>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Group Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder={`e.g., "The Johnson Family" or "Sunset Care Center"`}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              autoFocus
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description (Optional)
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Brief description of your group..."
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          {selectedType === 'family' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Primary Contact Name
                </label>
                <input
                  type="text"
                  value={formData.primaryContact}
                  onChange={(e) => setFormData(prev => ({ ...prev, primaryContact: e.target.value }))}
                  placeholder="Primary family contact"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Emergency Contact
                </label>
                <input
                  type="text"
                  value={formData.emergencyContact}
                  onChange={(e) => setFormData(prev => ({ ...prev, emergencyContact: e.target.value }))}
                  placeholder="Emergency contact information"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
            </>
          )}
        </div>

        <div className="flex gap-3 pt-4">
          <Button
            variant="outline"
            onClick={() => setStep('type')}
            fullWidth
          >
            Back
          </Button>
          <Button
            variant="primary"
            onClick={handleCreateTenant}
            disabled={!formData.name.trim() || isCreating}
            fullWidth
          >
            {isCreating ? 'Creating...' : `Create ${selectedTypeInfo.name}`}
          </Button>
        </div>
      </div>
    );
  };

  const renderCreating = () => (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <Heart className="w-8 h-8 text-primary-600 animate-pulse" />
      </div>
      <h2 className="text-xl font-semibold text-gray-900 mb-2">
        Creating Your Group...
      </h2>
      <p className="text-gray-600">
        Setting up your family group and configuring features
      </p>
      <div className="mt-6">
        <div className="w-32 h-2 bg-gray-200 rounded-full mx-auto overflow-hidden">
          <div className="h-full bg-primary-500 rounded-full animate-pulse" style={{ width: '70%' }} />
        </div>
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-2">
            <Users className="w-5 h-5 text-primary-600" />
            <span className="font-medium text-gray-900">Create Group</span>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            disabled={isCreating}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {step === 'type' && renderTypeSelection()}
          {step === 'details' && renderDetailsForm()}
          {step === 'creating' && renderCreating()}
        </div>
      </motion.div>
    </div>
  );
};
