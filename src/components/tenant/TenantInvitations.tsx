import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  UserPlus, 
  Mail, 
  Clock, 
  Check, 
  X, 
  MoreHorizontal,
  Send,
  Copy,
  Trash2,
  RefreshCw
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { useTenantStore } from '../../store/tenantStore';
import { tenantService } from '../../services/tenantService';
import type { TenantInvitation, TenantUser } from '../../types/tenant';
import { DEFAULT_PERMISSIONS } from '../../types/tenant';
import toast from 'react-hot-toast';

interface TenantInvitationsProps {
  tenantId: string;
  onInvitationSent?: (invitation: TenantInvitation) => void;
  onUserAdded?: (user: TenantUser) => void;
}

export const TenantInvitations: React.FC<TenantInvitationsProps> = ({
  tenantId,
  onInvitationSent,
  onUserAdded
}) => {
  const { currentTenant } = useTenantStore();
  const [invitations, setInvitations] = useState<TenantInvitation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [inviteForm, setInviteForm] = useState({
    email: '',
    role: 'caregiver' as 'admin' | 'caregiver' | 'patient' | 'guest',
    relationship: '',
    personalMessage: ''
  });
  const [isSending, setIsSending] = useState(false);

  useEffect(() => {
    loadInvitations();
  }, [tenantId]);

  const loadInvitations = async () => {
    try {
      setIsLoading(true);
      // In a real implementation, this would load from the database
      // For now, we'll use mock data
      setInvitations([]);
    } catch (error) {
      console.error('Error loading invitations:', error);
      toast.error('Failed to load invitations');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendInvitation = async () => {
    if (!inviteForm.email.trim()) {
      toast.error('Please enter an email address');
      return;
    }

    try {
      setIsSending(true);
      
      const invitation = await tenantService.createInvitation(
        tenantId,
        inviteForm.email.trim(),
        inviteForm.role,
        'current-user-id', // In real app, get from auth store
        {
          relationship: inviteForm.relationship,
          personalMessage: inviteForm.personalMessage
        }
      );

      setInvitations(prev => [invitation, ...prev]);
      setInviteForm({
        email: '',
        role: 'caregiver',
        relationship: '',
        personalMessage: ''
      });
      setShowInviteForm(false);
      
      toast.success('Invitation sent successfully!');
      onInvitationSent?.(invitation);
      
    } catch (error) {
      console.error('Error sending invitation:', error);
      toast.error('Failed to send invitation');
    } finally {
      setIsSending(false);
    }
  };

  const handleCopyInviteLink = (invitation: TenantInvitation) => {
    const inviteUrl = `${window.location.origin}/invite/${invitation.token}`;
    navigator.clipboard.writeText(inviteUrl);
    toast.success('Invite link copied to clipboard');
  };

  const handleRevokeInvitation = async (invitationId: string) => {
    try {
      // In real implementation, call API to revoke invitation
      setInvitations(prev => prev.filter(inv => inv.id !== invitationId));
      toast.success('Invitation revoked');
    } catch (error) {
      console.error('Error revoking invitation:', error);
      toast.error('Failed to revoke invitation');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'accepted': return 'text-green-600 bg-green-50 border-green-200';
      case 'expired': return 'text-red-600 bg-red-50 border-red-200';
      case 'revoked': return 'text-gray-600 bg-gray-50 border-gray-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return Clock;
      case 'accepted': return Check;
      case 'expired': return X;
      case 'revoked': return X;
      default: return Clock;
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin': return 'Administrator';
      case 'caregiver': return 'Caregiver';
      case 'patient': return 'Patient';
      case 'guest': return 'Guest';
      default: return role;
    }
  };

  const isExpired = (invitation: TenantInvitation) => {
    return new Date() > invitation.expiresAt;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Family Invitations</h3>
          <p className="text-sm text-gray-600">
            Invite family members and caregivers to join {currentTenant?.name}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            icon={RefreshCw}
            onClick={loadInvitations}
            disabled={isLoading}
          >
            Refresh
          </Button>
          <Button
            variant="primary"
            icon={UserPlus}
            onClick={() => setShowInviteForm(true)}
          >
            Invite Member
          </Button>
        </div>
      </div>

      {/* Invite Form */}
      <AnimatePresence>
        {showInviteForm && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4">Invite New Member</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    value={inviteForm.email}
                    onChange={(e) => setInviteForm(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Role *
                  </label>
                  <select
                    value={inviteForm.role}
                    onChange={(e) => setInviteForm(prev => ({ ...prev, role: e.target.value as any }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="caregiver">Caregiver</option>
                    <option value="admin">Administrator</option>
                    <option value="patient">Patient</option>
                    <option value="guest">Guest</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Relationship
                  </label>
                  <input
                    type="text"
                    value={inviteForm.relationship}
                    onChange={(e) => setInviteForm(prev => ({ ...prev, relationship: e.target.value }))}
                    placeholder="e.g., Daughter, Son, Spouse"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Personal Message
                  </label>
                  <textarea
                    value={inviteForm.personalMessage}
                    onChange={(e) => setInviteForm(prev => ({ ...prev, personalMessage: e.target.value }))}
                    placeholder="Optional personal message..."
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => setShowInviteForm(false)}
                  disabled={isSending}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  icon={Send}
                  onClick={handleSendInvitation}
                  disabled={isSending || !inviteForm.email.trim()}
                >
                  {isSending ? 'Sending...' : 'Send Invitation'}
                </Button>
              </div>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Invitations List */}
      <div className="space-y-3">
        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin w-6 h-6 border-2 border-primary-600 border-t-transparent rounded-full mx-auto mb-2"></div>
            <p className="text-gray-600">Loading invitations...</p>
          </div>
        ) : invitations.length === 0 ? (
          <Card className="p-8 text-center">
            <Mail className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <h4 className="font-medium text-gray-900 mb-2">No Invitations Sent</h4>
            <p className="text-gray-600 mb-4">
              Start by inviting family members and caregivers to join your group.
            </p>
            <Button
              variant="primary"
              icon={UserPlus}
              onClick={() => setShowInviteForm(true)}
            >
              Send First Invitation
            </Button>
          </Card>
        ) : (
          invitations.map((invitation) => {
            const StatusIcon = getStatusIcon(invitation.status);
            const expired = isExpired(invitation);
            
            return (
              <motion.div
                key={invitation.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                        <Mail className="w-5 h-5 text-gray-600" />
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-gray-900">{invitation.email}</h4>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-sm text-gray-600">
                            {getRoleLabel(invitation.role)}
                          </span>
                          {invitation.metadata?.relationship && (
                            <>
                              <span className="text-gray-400">•</span>
                              <span className="text-sm text-gray-600">
                                {invitation.metadata.relationship}
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className={`
                        flex items-center gap-1 px-2 py-1 rounded-full border text-xs font-medium
                        ${getStatusColor(expired ? 'expired' : invitation.status)}
                      `}>
                        <StatusIcon className="w-3 h-3" />
                        {expired ? 'Expired' : invitation.status}
                      </div>

                      {invitation.status === 'pending' && !expired && (
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            icon={Copy}
                            onClick={() => handleCopyInviteLink(invitation)}
                            className="text-gray-600"
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            icon={Trash2}
                            onClick={() => handleRevokeInvitation(invitation.id)}
                            className="text-red-600"
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  {invitation.metadata?.personalMessage && (
                    <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-700 italic">
                        "{invitation.metadata.personalMessage}"
                      </p>
                    </div>
                  )}

                  <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
                    <span>
                      Sent {invitation.invitedAt.toLocaleDateString()}
                    </span>
                    <span>
                      Expires {invitation.expiresAt.toLocaleDateString()}
                    </span>
                  </div>
                </Card>
              </motion.div>
            );
          })
        )}
      </div>
    </div>
  );
};
