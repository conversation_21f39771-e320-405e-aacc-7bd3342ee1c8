import React, { useEffect, useCallback } from 'react';
import { useAuthStore } from '../../store/authStore';
import { useNotificationStore } from '../../store/notificationStore';
import type { Photo } from '../../types';
import toast from 'react-hot-toast';

interface PhotoPromptNotificationsProps {
  photos: Photo[];
}

export const PhotoPromptNotifications: React.FC<PhotoPromptNotificationsProps> = ({
  photos,
}) => {
  const { user } = useAuthStore();
  const { sendNotification } = useNotificationStore();

  // Photo prompt messages
  const promptMessages = [
    {
      title: "💭 Memory Time!",
      body: "Would you like to chat about one of your photos?",
      action: "View Photos"
    },
    {
      title: "📸 Photo Memories",
      body: "I'd love to hear the story behind your photos!",
      action: "Share Stories"
    },
    {
      title: "🌟 Special Moments",
      body: "Let's talk about your favorite memories together",
      action: "Start Conversation"
    },
    {
      title: "👥 Family & Friends",
      body: "Tell me about the people in your photos",
      action: "Chat About Photos"
    },
    {
      title: "🎉 Happy Memories",
      body: "Your photos bring back wonderful memories - let's chat!",
      action: "Explore Memories"
    },
    {
      title: "📱 Photo Check-in",
      body: "How are you feeling about your recent photos?",
      action: "Share Feelings"
    }
  ];

  // Get random photo for context
  const getRandomPhoto = useCallback(() => {
    if (photos.length === 0) return null;
    const randomIndex = Math.floor(Math.random() * photos.length);
    return photos[randomIndex];
  }, [photos]);

  // Send photo prompt notification
  const sendPhotoPrompt = useCallback(async () => {
    if (!user?.preferences.notifications || photos.length === 0) return;

    const randomPrompt = promptMessages[Math.floor(Math.random() * promptMessages.length)];
    const randomPhoto = getRandomPhoto();

    try {
      // Send browser notification
      await sendNotification(randomPrompt.title, {
        body: randomPrompt.body,
        icon: '/pwa-192x192.png',
        badge: '/pwa-192x192.png',
        tag: 'photo-prompt',
        requireInteraction: false,
        actions: [
          {
            action: 'view-photos',
            title: randomPrompt.action,
          },
          {
            action: 'dismiss',
            title: 'Maybe Later',
          },
        ],
        data: {
          type: 'photo-prompt',
          photoId: randomPhoto?.id,
          photoUrl: randomPhoto?.url,
        }
      });

      // Also show in-app toast for immediate visibility
      toast((t) => (
        <div className="flex items-center gap-3">
          <div className="flex-shrink-0">
            {randomPhoto?.url ? (
              <img 
                src={randomPhoto.url} 
                alt="Memory" 
                className="w-12 h-12 rounded-lg object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            ) : (
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                📸
              </div>
            )}
          </div>
          <div className="flex-1">
            <div className="font-medium text-gray-900">{randomPrompt.title}</div>
            <div className="text-sm text-gray-600">{randomPrompt.body}</div>
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => {
                toast.dismiss(t.id);
                // Navigate to photos
                window.dispatchEvent(new CustomEvent('navigate-to-photos'));
              }}
              className="px-3 py-1 bg-primary-500 text-white text-sm rounded hover:bg-primary-600"
            >
              {randomPrompt.action}
            </button>
            <button
              onClick={() => toast.dismiss(t.id)}
              className="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded hover:bg-gray-300"
            >
              Later
            </button>
          </div>
        </div>
      ), {
        duration: 8000,
        position: 'top-center',
      });

      console.log('Photo prompt notification sent:', randomPrompt.title);
    } catch (error) {
      console.error('Failed to send photo prompt:', error);
    }
  }, [user, photos, sendNotification, getRandomPhoto]);

  // Schedule random photo prompts
  useEffect(() => {
    if (!user?.preferences.notifications || photos.length === 0) return;

    // Function to schedule next prompt
    const scheduleNextPrompt = () => {
      // Random interval between 2-6 hours (in milliseconds)
      const minInterval = 2 * 60 * 60 * 1000; // 2 hours
      const maxInterval = 6 * 60 * 60 * 1000; // 6 hours
      const randomInterval = Math.random() * (maxInterval - minInterval) + minInterval;

      console.log(`Next photo prompt scheduled in ${Math.round(randomInterval / (60 * 60 * 1000))} hours`);

      return setTimeout(() => {
        // Only send during reasonable hours (9 AM - 8 PM)
        const now = new Date();
        const hour = now.getHours();
        
        if (hour >= 9 && hour <= 20) {
          sendPhotoPrompt();
        } else {
          console.log('Skipping photo prompt - outside active hours');
        }
        
        // Schedule the next one
        scheduleNextPrompt();
      }, randomInterval);
    };

    // Start the scheduling
    const timeoutId = scheduleNextPrompt();

    // Cleanup
    return () => {
      clearTimeout(timeoutId);
    };
  }, [user, photos.length, sendPhotoPrompt]);

  // Handle notification clicks
  useEffect(() => {
    const handleNotificationClick = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { action, notification } = customEvent.detail;

      if (notification?.tag === 'photo-prompt') {
        switch (action) {
          case 'view-photos':
            window.dispatchEvent(new CustomEvent('navigate-to-photos'));
            break;
          case 'dismiss':
            // Just dismiss, no action needed
            break;
          default:
            // Default click - go to photos
            window.dispatchEvent(new CustomEvent('navigate-to-photos'));
            break;
        }
      }
    };

    window.addEventListener('notification-click', handleNotificationClick);
    
    return () => {
      window.removeEventListener('notification-click', handleNotificationClick);
    };
  }, []);

  // Listen for navigation events
  useEffect(() => {
    const handleNavigateToPhotos = () => {
      // This will be handled by the main App component
      window.dispatchEvent(new CustomEvent('change-tab', { detail: 'photos' }));
    };

    window.addEventListener('navigate-to-photos', handleNavigateToPhotos);
    
    return () => {
      window.removeEventListener('navigate-to-photos', handleNavigateToPhotos);
    };
  }, []);

  // Development: Send a test prompt after 10 seconds (remove in production)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && photos.length > 0) {
      const testTimeout = setTimeout(() => {
        console.log('Sending test photo prompt...');
        sendPhotoPrompt();
      }, 10000); // 10 seconds

      return () => clearTimeout(testTimeout);
    }
  }, [photos.length, sendPhotoPrompt]);

  return null; // This component doesn't render anything
};

// Hook for easy integration
export const usePhotoPrompts = (photos: Photo[]) => {
  const { user } = useAuthStore();
  
  return {
    isEnabled: user?.preferences.notifications && photos.length > 0,
    photoCount: photos.length,
  };
};
