import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bell, 
  X, 
  Check, 
  Clock, 
  AlertTriangle, 
  Heart,
  Camera,
  MessageCircle,
  Pill,
  Users
} from 'lucide-react';
import { useAuthStore } from '../../store/authStore';
import { useTenantStore } from '../../store/tenantStore';
import { pushNotificationService } from '../../services/pushNotificationService';
import { securityService } from '../../services/securityService';
import toast from 'react-hot-toast';

interface Notification {
  id: string;
  type: 'medication' | 'photo-prompt' | 'family-update' | 'emergency' | 'system';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
  data?: Record<string, any>;
  actions?: NotificationAction[];
}

interface NotificationAction {
  id: string;
  label: string;
  action: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
}

export const RealTimeNotificationManager: React.FC = () => {
  const { user } = useAuthStore();
  const { currentTenant } = useTenantStore();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    if (user?.id) {
      initializeNotifications();
      setupServiceWorkerListeners();
    }
  }, [user?.id]);

  useEffect(() => {
    // Update unread count
    const unread = notifications.filter(n => !n.read).length;
    setUnreadCount(unread);
  }, [notifications]);

  const initializeNotifications = async () => {
    try {
      // Initialize push notification service
      const initialized = await pushNotificationService.initialize();
      if (initialized) {
        // Subscribe to push notifications
        const subscription = await pushNotificationService.subscribe(
          user!.id,
          currentTenant?.id
        );
        setIsSubscribed(!!subscription);

        // Log notification setup
        await securityService.logAuditEvent(
          user!.id,
          'system_error',
          'notification_setup',
          { subscribed: !!subscription },
          undefined,
          currentTenant?.id
        );
      }

      // Load existing notifications
      loadNotifications();
    } catch (error) {
      console.error('Failed to initialize notifications:', error);
      toast.error('Failed to set up notifications');
    }
  };

  const setupServiceWorkerListeners = () => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        const { type, data } = event.data;
        
        switch (type) {
          case 'NOTIFICATION_CLICKED':
            handleNotificationClick(data);
            break;
          case 'MEDICATION_TAKEN':
            handleMedicationTaken(data);
            break;
          case 'EMERGENCY_CALL':
            handleEmergencyCall(data);
            break;
        }
      });
    }
  };

  const loadNotifications = () => {
    // Load notifications from localStorage for demo
    const saved = localStorage.getItem(`notifications_${user?.id}`);
    if (saved) {
      try {
        const parsed = JSON.parse(saved).map((n: any) => ({
          ...n,
          timestamp: new Date(n.timestamp)
        }));
        setNotifications(parsed);
      } catch (error) {
        console.error('Failed to load notifications:', error);
      }
    }

    // Add some demo notifications
    addDemoNotifications();
  };

  const addDemoNotifications = () => {
    const demoNotifications: Notification[] = [
      {
        id: '1',
        type: 'medication',
        title: 'Medication Reminder',
        message: 'Time to take your morning medication (Aspirin 81mg)',
        timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
        read: false,
        priority: 'high',
        data: { medicationId: 'med-1', medicationName: 'Aspirin', dosage: '81mg' },
        actions: [
          {
            id: 'taken',
            label: 'Mark as Taken',
            action: () => handleMedicationTaken({ medicationId: 'med-1' }),
            variant: 'primary'
          },
          {
            id: 'snooze',
            label: 'Snooze 10 min',
            action: () => handleMedicationSnooze({ medicationId: 'med-1' }),
            variant: 'secondary'
          }
        ]
      },
      {
        id: '2',
        type: 'photo-prompt',
        title: 'Share a Memory',
        message: 'Would you like to share a photo and tell us about it?',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        read: false,
        priority: 'medium',
        actions: [
          {
            id: 'take-photo',
            label: 'Take Photo',
            action: () => handleTakePhoto(),
            variant: 'primary'
          },
          {
            id: 'choose-photo',
            label: 'Choose Photo',
            action: () => handleChoosePhoto(),
            variant: 'secondary'
          }
        ]
      },
      {
        id: '3',
        type: 'family-update',
        title: 'Family Update',
        message: 'Sarah shared a new photo: "Beautiful sunset from our walk today!"',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
        read: true,
        priority: 'medium',
        data: { fromUser: 'Sarah', photoId: 'photo-123' }
      }
    ];

    setNotifications(prev => {
      const existing = prev.filter(n => !demoNotifications.find(d => d.id === n.id));
      return [...existing, ...demoNotifications];
    });
  };

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: crypto.randomUUID(),
      timestamp: new Date(),
      read: false
    };

    setNotifications(prev => [newNotification, ...prev]);
    
    // Save to localStorage
    const updated = [newNotification, ...notifications];
    localStorage.setItem(`notifications_${user?.id}`, JSON.stringify(updated));

    // Show toast for high priority notifications
    if (notification.priority === 'high' || notification.priority === 'critical') {
      toast(notification.message, {
        icon: getNotificationIcon(notification.type),
        duration: 6000
      });
    }

    // Send push notification if app is in background
    if (document.hidden) {
      pushNotificationService.sendLocalNotification({
        title: notification.title,
        body: notification.message,
        tag: notification.type,
        data: notification.data
      });
    }
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, read: true } : n
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const removeNotification = (notificationId: string) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  };

  const handleNotificationClick = (data: any) => {
    console.log('Notification clicked:', data);
    // Handle navigation based on notification data
  };

  const handleMedicationTaken = (data: any) => {
    console.log('Medication taken:', data);
    // Remove medication reminder notification
    setNotifications(prev => 
      prev.filter(n => !(n.type === 'medication' && n.data?.medicationId === data.medicationId))
    );
    toast.success('Medication marked as taken');
  };

  const handleMedicationSnooze = (data: any) => {
    console.log('Medication snoozed:', data);
    // Schedule new notification in 10 minutes
    setTimeout(() => {
      addNotification({
        type: 'medication',
        title: 'Medication Reminder (Snoozed)',
        message: `Time to take your medication (${data.medicationName})`,
        priority: 'high',
        data
      });
    }, 10 * 60 * 1000);
    
    // Remove current notification
    setNotifications(prev => 
      prev.filter(n => !(n.type === 'medication' && n.data?.medicationId === data.medicationId))
    );
    toast('Medication reminder snoozed for 10 minutes');
  };

  const handleTakePhoto = () => {
    // Navigate to camera
    window.dispatchEvent(new CustomEvent('change-tab', { detail: 'photos' }));
    setShowNotifications(false);
  };

  const handleChoosePhoto = () => {
    // Navigate to photo gallery
    window.dispatchEvent(new CustomEvent('change-tab', { detail: 'photos' }));
    setShowNotifications(false);
  };

  const handleEmergencyCall = (data: any) => {
    console.log('Emergency call requested:', data);
    // Handle emergency call logic
    toast.error('Emergency services contacted');
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'medication': return '💊';
      case 'photo-prompt': return '📸';
      case 'family-update': return '👥';
      case 'emergency': return '🚨';
      case 'system': return '⚙️';
      default: return '🔔';
    }
  };

  const getNotificationIconComponent = (type: string) => {
    switch (type) {
      case 'medication': return Pill;
      case 'photo-prompt': return Camera;
      case 'family-update': return Users;
      case 'emergency': return AlertTriangle;
      case 'system': return Bell;
      default: return Bell;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-blue-600 bg-blue-100';
      case 'low': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <>
      {/* Notification Bell */}
      <div className="relative">
        <button
          onClick={() => setShowNotifications(!showNotifications)}
          className="relative p-2 text-gray-600 hover:text-gray-900 transition-colors"
          aria-label={`Notifications ${unreadCount > 0 ? `(${unreadCount} unread)` : ''}`}
        >
          <Bell className="w-6 h-6" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
        </button>

        {/* Notification Dropdown */}
        <AnimatePresence>
          {showNotifications && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute right-0 top-full mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h3 className="font-semibold text-gray-900">Notifications</h3>
                <div className="flex items-center gap-2">
                  {unreadCount > 0 && (
                    <button
                      onClick={markAllAsRead}
                      className="text-sm text-blue-600 hover:text-blue-700"
                    >
                      Mark all read
                    </button>
                  )}
                  <button
                    onClick={() => setShowNotifications(false)}
                    className="p-1 hover:bg-gray-100 rounded"
                  >
                    <X className="w-4 h-4 text-gray-500" />
                  </button>
                </div>
              </div>

              {/* Notifications List */}
              <div className="max-h-80 overflow-y-auto">
                {notifications.length === 0 ? (
                  <div className="p-8 text-center text-gray-500">
                    <Bell className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                    <p>No notifications</p>
                  </div>
                ) : (
                  <div className="divide-y divide-gray-100">
                    {notifications.map((notification) => {
                      const IconComponent = getNotificationIconComponent(notification.type);
                      
                      return (
                        <div
                          key={notification.id}
                          className={`p-4 hover:bg-gray-50 transition-colors ${
                            !notification.read ? 'bg-blue-50' : ''
                          }`}
                        >
                          <div className="flex items-start gap-3">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getPriorityColor(notification.priority)}`}>
                              <IconComponent className="w-4 h-4" />
                            </div>
                            
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between mb-1">
                                <h4 className="font-medium text-gray-900 text-sm">
                                  {notification.title}
                                </h4>
                                <div className="flex items-center gap-1">
                                  <Clock className="w-3 h-3 text-gray-400" />
                                  <span className="text-xs text-gray-500">
                                    {notification.timestamp.toLocaleTimeString([], { 
                                      hour: '2-digit', 
                                      minute: '2-digit' 
                                    })}
                                  </span>
                                </div>
                              </div>
                              
                              <p className="text-sm text-gray-600 mb-2">
                                {notification.message}
                              </p>
                              
                              {/* Actions */}
                              {notification.actions && notification.actions.length > 0 && (
                                <div className="flex gap-2">
                                  {notification.actions.map((action) => (
                                    <button
                                      key={action.id}
                                      onClick={() => {
                                        action.action();
                                        markAsRead(notification.id);
                                      }}
                                      className={`px-3 py-1 text-xs rounded-full transition-colors ${
                                        action.variant === 'primary'
                                          ? 'bg-blue-600 text-white hover:bg-blue-700'
                                          : action.variant === 'danger'
                                          ? 'bg-red-600 text-white hover:bg-red-700'
                                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                      }`}
                                    >
                                      {action.label}
                                    </button>
                                  ))}
                                </div>
                              )}
                            </div>
                            
                            {!notification.read && (
                              <button
                                onClick={() => markAsRead(notification.id)}
                                className="p-1 hover:bg-gray-200 rounded"
                                aria-label="Mark as read"
                              >
                                <Check className="w-4 h-4 text-gray-400" />
                              </button>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </>
  );
};
