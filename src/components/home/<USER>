import React from "react";
import { <PERSON>, MessageCircle, Pill, Heart, ArrowRight } from "lucide-react";
import { Button } from "../ui/Button";
import { Card } from "../ui/Card";

interface WelcomeScreenProps {
  userName?: string;
  onGetStarted: () => void;
  onNavigate: (tab: string) => void;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  userName = "Friend",
  onGetStarted,
  onNavigate,
}) => {
  const features = [
    {
      icon: Camera,
      title: "Share Your Photos",
      description:
        "Upload your precious memories and let's talk about them together",
      action: () => {
        console.log("Navigating to photos");
        onNavigate("photos");
      },
      color: "bg-primary-100 text-primary-600",
    },
    {
      icon: MessageCircle,
      title: "Have Conversations",
      description:
        "Chat about your photos using voice or text - I'm here to listen",
      action: () => {
        console.log("Navigating to chat");
        onNavigate("chat");
      },
      color: "bg-warm-100 text-warm-600",
    },
    {
      icon: Pill,
      title: "Medication Reminders",
      description: "Never miss your medications with gentle, caring reminders",
      action: () => {
        console.log("Navigating to medications");
        onNavigate("medications");
      },
      color: "bg-success-100 text-success-600",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-warm-50 px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <Heart className="w-10 h-10 text-white" />
          </div>

          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Welcome, {userName}
          </h1>

          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
            Your AI companion for treasured memories and daily care. Let's
            explore your photos together and keep you healthy and happy.
          </p>

          <Button
            variant="primary"
            size="lg"
            icon={ArrowRight}
            iconPosition="right"
            onClick={onGetStarted}
            className="shadow-xl hover:shadow-2xl"
          >
            Get Started
          </Button>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card
                key={index}
                hover
                className="text-center space-y-4 cursor-pointer group"
                onClick={feature.action}
              >
                <div
                  className={`w-16 h-16 ${feature.color} rounded-2xl flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-200`}
                >
                  <Icon className="w-8 h-8" />
                </div>

                <h3 className="text-xl font-semibold text-gray-900">
                  {feature.title}
                </h3>
                <p className="text-gray-600">{feature.description}</p>

                <Button
                  variant="ghost"
                  size="sm"
                  icon={ArrowRight}
                  iconPosition="right"
                  className="group-hover:translate-x-1 transition-transform duration-200"
                >
                  Explore
                </Button>
              </Card>
            );
          })}
        </div>

        {/* Benefits Section */}
        <Card className="bg-gradient-to-r from-primary-50 to-warm-50 border-primary-200">
          <div className="text-center space-y-4">
            <h2 className="text-2xl font-bold text-gray-900">
              Designed With Love & Care
            </h2>
            <p className="text-gray-700 max-w-2xl mx-auto">
              Memory Companion combines the latest AI technology with a deep
              understanding of memory care. Every feature is crafted to bring
              joy, reduce stress, and keep you connected to what matters most.
            </p>

            <div className="grid sm:grid-cols-3 gap-6 mt-8">
              <div className="text-center">
                <div className="w-12 h-12 bg-primary-200 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <Heart className="w-6 h-6 text-primary-600" />
                </div>
                <h4 className="font-semibold text-gray-900">
                  Compassionate AI
                </h4>
                <p className="text-sm text-gray-600">
                  Designed to understand and respond with empathy
                </p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-warm-200 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <MessageCircle className="w-6 h-6 text-warm-600" />
                </div>
                <h4 className="font-semibold text-gray-900">Voice First</h4>
                <p className="text-sm text-gray-600">
                  Natural conversations, just like talking to a friend
                </p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-success-200 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <Pill className="w-6 h-6 text-success-600" />
                </div>
                <h4 className="font-semibold text-gray-900">Complete Care</h4>
                <p className="text-sm text-gray-600">
                  Memory conversations plus health reminders
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};
