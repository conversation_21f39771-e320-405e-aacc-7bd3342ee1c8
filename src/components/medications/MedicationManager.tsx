import React, { useState } from "react";
import {
  Plus,
  Pill,
  Clock,
  CheckCircle,
  AlertCircle,
  Camera,
  Bell,
  Trash2,
} from "lucide-react";
import { format, isToday, isTomorrow, isYesterday } from "date-fns";
import { Button } from "../ui/Button";
import { Card } from "../ui/Card";
import { useAuthStore } from "../../store/authStore";
import type { Medication, MedicationReminder } from "../../types";

interface MedicationManagerProps {
  medications: Medication[];
  reminders: MedicationReminder[];
  onAddMedication: (medication: Medication) => void;
  onUpdateReminder: (reminder: MedicationReminder) => void;
}

export const MedicationManager: React.FC<MedicationManagerProps> = ({
  medications,
  reminders,
  onAddMedication,
  onUpdateReminder,
}) => {
  const { user } = useAuthStore();
  const [showAddForm, setShowAddForm] = useState(false);
  const [newMedication, setNewMedication] = useState({
    name: "",
    dosage: "",
    frequency: "daily" as const,
    times: ["08:00"],
    instructions: "",
    caregiverNotifications: {
      enabled: user?.role === "caregiver",
      delayMinutes: 15, // Notify caregiver if not taken after X minutes
      notifyMissed: true,
      notifyTaken: false,
    },
  });

  const handleAddMedication = () => {
    const medication: Medication = {
      id: `med-${Date.now()}`,
      ...newMedication,
      isActive: true,
      createdAt: new Date(),
    };

    onAddMedication(medication);
    setNewMedication({
      name: "",
      dosage: "",
      frequency: "daily",
      times: ["08:00"],
      instructions: "",
      caregiverNotifications: {
        enabled: user?.role === "caregiver",
        delayMinutes: 15,
        notifyMissed: true,
        notifyTaken: false,
      },
    });
    setShowAddForm(false);
  };

  const getTodaysReminders = () => {
    return reminders.filter((reminder) => isToday(reminder.scheduledTime));
  };

  const getUpcomingReminders = () => {
    return reminders
      .filter(
        (reminder) =>
          !isToday(reminder.scheduledTime) &&
          reminder.scheduledTime > new Date()
      )
      .slice(0, 5);
  };

  const markAsTaken = (reminder: MedicationReminder) => {
    onUpdateReminder({
      ...reminder,
      status: "taken",
      actualTime: new Date(),
    });
  };

  const formatReminderTime = (date: Date) => {
    if (isToday(date)) return `Today at ${format(date, "h:mm a")}`;
    if (isTomorrow(date)) return `Tomorrow at ${format(date, "h:mm a")}`;
    if (isYesterday(date)) return `Yesterday at ${format(date, "h:mm a")}`;
    return format(date, "MMM d at h:mm a");
  };

  const getStatusColor = (status: MedicationReminder["status"]) => {
    switch (status) {
      case "taken":
        return "text-success-600";
      case "missed":
        return "text-error-600";
      case "pending":
        return "text-warm-600";
      default:
        return "text-gray-600";
    }
  };

  const getStatusIcon = (status: MedicationReminder["status"]) => {
    switch (status) {
      case "taken":
        return CheckCircle;
      case "missed":
        return AlertCircle;
      case "pending":
        return Clock;
      default:
        return Clock;
    }
  };

  const addTimeSlot = () => {
    setNewMedication((prev) => ({
      ...prev,
      times: [...prev.times, "12:00"],
    }));
  };

  const removeTimeSlot = (index: number) => {
    setNewMedication((prev) => ({
      ...prev,
      times: prev.times.filter((_, i) => i !== index),
    }));
  };

  const updateTimeSlot = (index: number, time: string) => {
    setNewMedication((prev) => ({
      ...prev,
      times: prev.times.map((t, i) => (i === index ? time : t)),
    }));
  };

  return (
    <div className="space-y-6">
      {/* Today's Reminders */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">
            Today's Medications
          </h2>
          <Button
            variant="primary"
            size="sm"
            icon={Plus}
            onClick={() => setShowAddForm(true)}
          >
            Add Medication
          </Button>
        </div>

        <div className="space-y-3">
          {getTodaysReminders().length === 0 ? (
            <Card className="text-center py-8">
              <Pill className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-600">
                No medications scheduled for today
              </p>
            </Card>
          ) : (
            getTodaysReminders().map((reminder) => {
              const medication = medications.find(
                (m) => m.id === reminder.medicationId
              );
              if (!medication) return null;

              const StatusIcon = getStatusIcon(reminder.status);

              return (
                <Card
                  key={reminder.id}
                  hover
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-4">
                    <div
                      className={`w-12 h-12 rounded-xl bg-primary-100 flex items-center justify-center`}
                    >
                      <Pill className="w-6 h-6 text-primary-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {medication.name}
                      </h3>
                      <p className="text-gray-600">{medication.dosage}</p>
                      <p className="text-sm text-gray-500">
                        {formatReminderTime(reminder.scheduledTime)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div
                      className={`flex items-center gap-1 ${getStatusColor(
                        reminder.status
                      )}`}
                    >
                      <StatusIcon className="w-4 h-4" />
                      <span className="text-sm font-medium capitalize">
                        {reminder.status}
                      </span>
                    </div>

                    {reminder.status === "pending" && (
                      <Button
                        variant="success"
                        size="sm"
                        onClick={() => markAsTaken(reminder)}
                      >
                        Mark Taken
                      </Button>
                    )}
                  </div>
                </Card>
              );
            })
          )}
        </div>
      </div>

      {/* Upcoming Reminders */}
      {getUpcomingReminders().length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Upcoming</h3>
          <div className="space-y-3">
            {getUpcomingReminders().map((reminder) => {
              const medication = medications.find(
                (m) => m.id === reminder.medicationId
              );
              if (!medication) return null;

              return (
                <Card
                  key={reminder.id}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center">
                      <Pill className="w-5 h-5 text-gray-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {medication.name}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {medication.dosage}
                      </p>
                    </div>
                  </div>
                  <p className="text-sm text-gray-500">
                    {formatReminderTime(reminder.scheduledTime)}
                  </p>
                </Card>
              );
            })}
          </div>
        </div>
      )}

      {/* Add Medication Form */}
      {showAddForm && (
        <Card className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Add New Medication
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAddForm(false)}
            >
              Cancel
            </Button>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Medication Name
              </label>
              <input
                type="text"
                value={newMedication.name}
                onChange={(e) =>
                  setNewMedication((prev) => ({
                    ...prev,
                    name: e.target.value,
                  }))
                }
                placeholder="e.g., Aspirin"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Dosage
              </label>
              <input
                type="text"
                value={newMedication.dosage}
                onChange={(e) =>
                  setNewMedication((prev) => ({
                    ...prev,
                    dosage: e.target.value,
                  }))
                }
                placeholder="e.g., 81mg"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Frequency
              </label>
              <select
                value={newMedication.frequency}
                onChange={(e) =>
                  setNewMedication((prev) => ({
                    ...prev,
                    frequency: e.target.value as Medication["frequency"],
                    times:
                      e.target.value === "daily"
                        ? ["08:00"]
                        : e.target.value === "twice-daily"
                        ? ["08:00", "20:00"]
                        : ["08:00", "12:00", "18:00"],
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="daily">Once daily</option>
                <option value="twice-daily">Twice daily</option>
                <option value="three-times-daily">Three times daily</option>
              </select>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  Exact Times
                </label>
                <Button
                  variant="ghost"
                  size="sm"
                  icon={Plus}
                  onClick={addTimeSlot}
                >
                  Add Time
                </Button>
              </div>
              <div className="space-y-2">
                {newMedication.times.map((time, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <input
                      type="time"
                      value={time}
                      onChange={(e) => updateTimeSlot(index, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                    {newMedication.times.length > 1 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        icon={Trash2}
                        onClick={() => removeTimeSlot(index)}
                        className="text-red-600 hover:text-red-700"
                      />
                    )}
                  </div>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Special Instructions (Optional)
              </label>
              <textarea
                value={newMedication.instructions}
                onChange={(e) =>
                  setNewMedication((prev) => ({
                    ...prev,
                    instructions: e.target.value,
                  }))
                }
                placeholder="e.g., Take with food"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                rows={2}
              />
            </div>

            {/* Caregiver Notification Settings */}
            {user?.role === "caregiver" && (
              <div className="border-t pt-4">
                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center gap-2">
                  <Bell className="w-4 h-4" />
                  Caregiver Notifications
                </h4>

                <div className="space-y-3">
                  <label className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={newMedication.caregiverNotifications.enabled}
                      onChange={(e) =>
                        setNewMedication((prev) => ({
                          ...prev,
                          caregiverNotifications: {
                            ...prev.caregiverNotifications,
                            enabled: e.target.checked,
                          },
                        }))
                      }
                      className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
                    />
                    <span className="text-sm text-gray-700">
                      Enable caregiver notifications for this medication
                    </span>
                  </label>

                  {newMedication.caregiverNotifications.enabled && (
                    <>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Notify me if not taken after (minutes)
                        </label>
                        <select
                          value={
                            newMedication.caregiverNotifications.delayMinutes
                          }
                          onChange={(e) =>
                            setNewMedication((prev) => ({
                              ...prev,
                              caregiverNotifications: {
                                ...prev.caregiverNotifications,
                                delayMinutes: parseInt(e.target.value),
                              },
                            }))
                          }
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        >
                          <option value={5}>5 minutes</option>
                          <option value={10}>10 minutes</option>
                          <option value={15}>15 minutes</option>
                          <option value={30}>30 minutes</option>
                          <option value={60}>1 hour</option>
                          <option value={120}>2 hours</option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <label className="flex items-center gap-3">
                          <input
                            type="checkbox"
                            checked={
                              newMedication.caregiverNotifications.notifyMissed
                            }
                            onChange={(e) =>
                              setNewMedication((prev) => ({
                                ...prev,
                                caregiverNotifications: {
                                  ...prev.caregiverNotifications,
                                  notifyMissed: e.target.checked,
                                },
                              }))
                            }
                            className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
                          />
                          <span className="text-sm text-gray-700">
                            Notify when medication is missed
                          </span>
                        </label>

                        <label className="flex items-center gap-3">
                          <input
                            type="checkbox"
                            checked={
                              newMedication.caregiverNotifications.notifyTaken
                            }
                            onChange={(e) =>
                              setNewMedication((prev) => ({
                                ...prev,
                                caregiverNotifications: {
                                  ...prev.caregiverNotifications,
                                  notifyTaken: e.target.checked,
                                },
                              }))
                            }
                            className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
                          />
                          <span className="text-sm text-gray-700">
                            Notify when medication is taken (confirmation)
                          </span>
                        </label>
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}

            <div className="flex gap-3">
              <Button variant="secondary" icon={Camera} className="flex-1">
                Add Photo
              </Button>
              <Button
                variant="primary"
                onClick={handleAddMedication}
                disabled={!newMedication.name || !newMedication.dosage}
                className="flex-1"
              >
                Add Medication
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};
