import React, { useState } from 'react';
import { Plus, Pill, Clock, CheckCircle, AlertCircle, Camera } from 'lucide-react';
import { format, isToday, isTomorrow, isYesterday } from 'date-fns';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import type { Medication, MedicationReminder } from '../../types';

interface MedicationManagerProps {
  medications: Medication[];
  reminders: MedicationReminder[];
  onAddMedication: (medication: Medication) => void;
  onUpdateReminder: (reminder: MedicationReminder) => void;
}

export const MedicationManager: React.FC<MedicationManagerProps> = ({
  medications,
  reminders,
  onAddMedication,
  onUpdateReminder,
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [newMedication, setNewMedication] = useState({
    name: '',
    dosage: '',
    frequency: 'daily' as const,
    times: ['08:00'],
    instructions: '',
  });

  const handleAddMedication = () => {
    const medication: Medication = {
      id: `med-${Date.now()}`,
      ...newMedication,
      isActive: true,
      createdAt: new Date(),
    };

    onAddMedication(medication);
    setNewMedication({
      name: '',
      dosage: '',
      frequency: 'daily',
      times: ['08:00'],
      instructions: '',
    });
    setShowAddForm(false);
  };

  const getTodaysReminders = () => {
    return reminders.filter(reminder => isToday(reminder.scheduledTime));
  };

  const getUpcomingReminders = () => {
    return reminders.filter(reminder => 
      !isToday(reminder.scheduledTime) && reminder.scheduledTime > new Date()
    ).slice(0, 5);
  };

  const markAsTaken = (reminder: MedicationReminder) => {
    onUpdateReminder({
      ...reminder,
      status: 'taken',
      actualTime: new Date(),
    });
  };

  const formatReminderTime = (date: Date) => {
    if (isToday(date)) return `Today at ${format(date, 'h:mm a')}`;
    if (isTomorrow(date)) return `Tomorrow at ${format(date, 'h:mm a')}`;
    if (isYesterday(date)) return `Yesterday at ${format(date, 'h:mm a')}`;
    return format(date, 'MMM d at h:mm a');
  };

  const getStatusColor = (status: MedicationReminder['status']) => {
    switch (status) {
      case 'taken': return 'text-success-600';
      case 'missed': return 'text-error-600';
      case 'pending': return 'text-warm-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: MedicationReminder['status']) => {
    switch (status) {
      case 'taken': return CheckCircle;
      case 'missed': return AlertCircle;
      case 'pending': return Clock;
      default: return Clock;
    }
  };

  return (
    <div className="space-y-6">
      {/* Today's Reminders */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Today's Medications</h2>
          <Button
            variant="primary"
            size="sm"
            icon={Plus}
            onClick={() => setShowAddForm(true)}
          >
            Add Medication
          </Button>
        </div>

        <div className="space-y-3">
          {getTodaysReminders().length === 0 ? (
            <Card className="text-center py-8">
              <Pill className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-600">No medications scheduled for today</p>
            </Card>
          ) : (
            getTodaysReminders().map((reminder) => {
              const medication = medications.find(m => m.id === reminder.medicationId);
              if (!medication) return null;

              const StatusIcon = getStatusIcon(reminder.status);

              return (
                <Card key={reminder.id} hover className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className={`w-12 h-12 rounded-xl bg-primary-100 flex items-center justify-center`}>
                      <Pill className="w-6 h-6 text-primary-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{medication.name}</h3>
                      <p className="text-gray-600">{medication.dosage}</p>
                      <p className="text-sm text-gray-500">
                        {formatReminderTime(reminder.scheduledTime)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className={`flex items-center gap-1 ${getStatusColor(reminder.status)}`}>
                      <StatusIcon className="w-4 h-4" />
                      <span className="text-sm font-medium capitalize">{reminder.status}</span>
                    </div>
                    
                    {reminder.status === 'pending' && (
                      <Button
                        variant="success"
                        size="sm"
                        onClick={() => markAsTaken(reminder)}
                      >
                        Mark Taken
                      </Button>
                    )}
                  </div>
                </Card>
              );
            })
          )}
        </div>
      </div>

      {/* Upcoming Reminders */}
      {getUpcomingReminders().length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Upcoming</h3>
          <div className="space-y-3">
            {getUpcomingReminders().map((reminder) => {
              const medication = medications.find(m => m.id === reminder.medicationId);
              if (!medication) return null;

              return (
                <Card key={reminder.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center">
                      <Pill className="w-5 h-5 text-gray-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{medication.name}</h4>
                      <p className="text-sm text-gray-600">{medication.dosage}</p>
                    </div>
                  </div>
                  <p className="text-sm text-gray-500">
                    {formatReminderTime(reminder.scheduledTime)}
                  </p>
                </Card>
              );
            })}
          </div>
        </div>
      )}

      {/* Add Medication Form */}
      {showAddForm && (
        <Card className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Add New Medication</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAddForm(false)}
            >
              Cancel
            </Button>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Medication Name
              </label>
              <input
                type="text"
                value={newMedication.name}
                onChange={(e) => setNewMedication(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., Aspirin"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Dosage
              </label>
              <input
                type="text"
                value={newMedication.dosage}
                onChange={(e) => setNewMedication(prev => ({ ...prev, dosage: e.target.value }))}
                placeholder="e.g., 81mg"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                When to take
              </label>
              <select
                value={newMedication.frequency}
                onChange={(e) => setNewMedication(prev => ({ 
                  ...prev, 
                  frequency: e.target.value as Medication['frequency'],
                  times: e.target.value === 'daily' ? ['08:00'] : 
                         e.target.value === 'twice-daily' ? ['08:00', '20:00'] :
                         ['08:00', '12:00', '18:00']
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="daily">Once daily</option>
                <option value="twice-daily">Twice daily</option>
                <option value="three-times-daily">Three times daily</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Special Instructions (Optional)
              </label>
              <textarea
                value={newMedication.instructions}
                onChange={(e) => setNewMedication(prev => ({ ...prev, instructions: e.target.value }))}
                placeholder="e.g., Take with food"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                rows={2}
              />
            </div>

            <div className="flex gap-3">
              <Button
                variant="secondary"
                icon={Camera}
                className="flex-1"
              >
                Add Photo
              </Button>
              <Button
                variant="primary"
                onClick={handleAddMedication}
                disabled={!newMedication.name || !newMedication.dosage}
                className="flex-1"
              >
                Add Medication
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};