import React, { useState, useEffect } from 'react';
import { Bell, Save } from 'lucide-react';
import { Button } from '../ui/Button';
import type { UserProfile, NotificationSettings } from '../../types/userProfile';

interface NotificationSettingsFormProps {
  profile: UserProfile;
  onSave: (data: Partial<UserProfile>) => Promise<void>;
  onChange: () => void;
  isLoading: boolean;
}

export const NotificationSettingsForm: React.FC<NotificationSettingsFormProps> = ({
  profile,
  onSave,
  onChange,
  isLoading
}) => {
  const [formData, setFormData] = useState<NotificationSettings>(profile.notificationSettings);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    setFormData(profile.notificationSettings);
    setHasChanges(false);
  }, [profile.notificationSettings]);

  const handleInputChange = (field: keyof NotificationSettings, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
    onChange();
  };

  const handleSave = async () => {
    await onSave({ notificationSettings: formData });
    setHasChanges(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <Bell className="w-6 h-6 text-primary-600" />
        <div>
          <h3 className="text-lg font-medium text-gray-900">Notification Preferences</h3>
          <p className="text-sm text-gray-600">Choose how and when you want to be notified</p>
        </div>
      </div>

      {/* Notification Methods */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Notification Methods</h4>
        
        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">Push Notifications</div>
            <div className="text-sm text-gray-600">Receive notifications in the app</div>
          </div>
          <input
            type="checkbox"
            checked={formData.pushNotifications}
            onChange={(e) => handleInputChange('pushNotifications', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>

        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">Email Notifications</div>
            <div className="text-sm text-gray-600">Receive notifications via email</div>
          </div>
          <input
            type="checkbox"
            checked={formData.emailNotifications}
            onChange={(e) => handleInputChange('emailNotifications', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>

        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">SMS Notifications</div>
            <div className="text-sm text-gray-600">Receive notifications via text message</div>
          </div>
          <input
            type="checkbox"
            checked={formData.smsNotifications}
            onChange={(e) => handleInputChange('smsNotifications', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>
      </div>

      {/* Notification Types */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Notification Types</h4>
        
        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">Medication Reminders</div>
            <div className="text-sm text-gray-600">Reminders to take medications</div>
          </div>
          <input
            type="checkbox"
            checked={formData.medicationReminders}
            onChange={(e) => handleInputChange('medicationReminders', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>

        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">Photo Prompts</div>
            <div className="text-sm text-gray-600">Gentle reminders to share photos</div>
          </div>
          <input
            type="checkbox"
            checked={formData.photoPrompts}
            onChange={(e) => handleInputChange('photoPrompts', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>

        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">Family Updates</div>
            <div className="text-sm text-gray-600">Updates from family members and caregivers</div>
          </div>
          <input
            type="checkbox"
            checked={formData.familyUpdates}
            onChange={(e) => handleInputChange('familyUpdates', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>

        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">Emergency Alerts</div>
            <div className="text-sm text-gray-600">Important safety and emergency notifications</div>
          </div>
          <input
            type="checkbox"
            checked={formData.emergencyAlerts}
            onChange={(e) => handleInputChange('emergencyAlerts', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>
      </div>

      {/* Quiet Hours */}
      <div>
        <h4 className="font-medium text-gray-900 mb-4">Quiet Hours</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Start Time</label>
            <input
              type="time"
              value={formData.quietHoursStart || ''}
              onChange={(e) => handleInputChange('quietHoursStart', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">End Time</label>
            <input
              type="time"
              value={formData.quietHoursEnd || ''}
              onChange={(e) => handleInputChange('quietHoursEnd', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end pt-6 border-t border-gray-200">
        <Button
          variant="primary"
          icon={Save}
          onClick={handleSave}
          disabled={!hasChanges || isLoading}
        >
          {isLoading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  );
};
