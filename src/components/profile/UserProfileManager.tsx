import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  User, 
  Settings, 
  Heart, 
  Shield, 
  Bell, 
  Eye,
  Edit3,
  Save,
  X,
  CheckCircle,
  AlertCircle,
  Users,
  Phone
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { PersonalInfoForm } from './PersonalInfoForm';
import { ContactInfoForm } from './ContactInfoForm';
import { PreferencesForm } from './PreferencesForm';
import { RelationshipsManager } from './RelationshipsManager';
import { EmergencyContactsForm } from './EmergencyContactsForm';
import { PrivacySettingsForm } from './PrivacySettingsForm';
import { NotificationSettingsForm } from './NotificationSettingsForm';
import { AccessibilitySettingsForm } from './AccessibilitySettingsForm';
import { useAuthStore } from '../../store/authStore';
import { useTenantStore } from '../../store/tenantStore';
import { userProfileService } from '../../services/userProfileService';
import type { UserProfile } from '../../types/userProfile';
import toast from 'react-hot-toast';

interface ProfileSection {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  description: string;
  component: React.ComponentType<any>;
}

const PROFILE_SECTIONS: ProfileSection[] = [
  {
    id: 'personal',
    name: 'Personal Information',
    icon: User,
    description: 'Basic personal details and bio',
    component: PersonalInfoForm
  },
  {
    id: 'contact',
    name: 'Contact Information',
    icon: Phone,
    description: 'Email, phone, and address details',
    component: ContactInfoForm
  },
  {
    id: 'preferences',
    name: 'Preferences',
    icon: Settings,
    description: 'App preferences and conversation settings',
    component: PreferencesForm
  },
  {
    id: 'relationships',
    name: 'Family & Caregivers',
    icon: Users,
    description: 'Manage family relationships and caregivers',
    component: RelationshipsManager
  },
  {
    id: 'emergency',
    name: 'Emergency Contacts',
    icon: Heart,
    description: 'Emergency contact information',
    component: EmergencyContactsForm
  },
  {
    id: 'privacy',
    name: 'Privacy Settings',
    icon: Shield,
    description: 'Data sharing and privacy controls',
    component: PrivacySettingsForm
  },
  {
    id: 'notifications',
    name: 'Notifications',
    icon: Bell,
    description: 'Notification preferences and timing',
    component: NotificationSettingsForm
  },
  {
    id: 'accessibility',
    name: 'Accessibility',
    icon: Eye,
    description: 'Accessibility and usability settings',
    component: AccessibilitySettingsForm
  }
];

export const UserProfileManager: React.FC = () => {
  const { user } = useAuthStore();
  const { currentTenant } = useTenantStore();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [activeSection, setActiveSection] = useState('personal');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  useEffect(() => {
    if (user?.id) {
      loadProfile();
    }
  }, [user?.id]);

  const loadProfile = async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      let userProfile = await userProfileService.getProfile(user.id);
      
      // If no profile exists, create one
      if (!userProfile && currentTenant?.id) {
        userProfile = await userProfileService.createProfile(
          user.id,
          currentTenant.id,
          {
            personalInfo: {
              firstName: user.name?.split(' ')[0] || '',
              lastName: user.name?.split(' ').slice(1).join(' ') || ''
            },
            contactInfo: {
              primaryEmail: user.email || '',
              preferredContactMethod: 'email',
              timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
              language: 'en'
            }
          }
        );
      }
      
      setProfile(userProfile);
    } catch (error) {
      console.error('Error loading profile:', error);
      toast.error('Failed to load profile');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveProfile = async (sectionData: Partial<UserProfile>) => {
    if (!user?.id || !profile) return;

    try {
      setIsSaving(true);
      const updatedProfile = await userProfileService.updateProfile(
        user.id,
        sectionData,
        user.id
      );
      setProfile(updatedProfile);
      setHasUnsavedChanges(false);
      toast.success('Profile updated successfully');
    } catch (error) {
      console.error('Error saving profile:', error);
      toast.error('Failed to save profile');
    } finally {
      setIsSaving(false);
    }
  };

  const getCompletionColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600 bg-green-100';
    if (percentage >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const renderActiveSection = () => {
    const section = PROFILE_SECTIONS.find(s => s.id === activeSection);
    if (!section || !profile) return null;

    const Component = section.component;
    return (
      <Component
        profile={profile}
        onSave={handleSaveProfile}
        onChange={() => setHasUnsavedChanges(true)}
        isLoading={isSaving}
      />
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-primary-600 border-t-transparent rounded-full mx-auto mb-4" />
          <p className="text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <Card className="p-8 text-center">
        <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Profile Not Found</h3>
        <p className="text-gray-600 mb-4">
          Unable to load your profile. This might be because the database tables haven't been set up yet.
        </p>
        <Button
          variant="primary"
          onClick={loadProfile}
          disabled={isLoading}
        >
          Try Again
        </Button>
      </Card>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Profile Settings</h1>
          <p className="text-gray-600">Manage your personal information and preferences</p>
        </div>
        
        {/* Profile Completion */}
        <div className="flex items-center gap-3">
          <div className="text-right">
            <div className="text-sm font-medium text-gray-900">
              Profile Completion
            </div>
            <div className={`text-xs px-2 py-1 rounded-full ${getCompletionColor(profile.profileCompleteness)}`}>
              {profile.profileCompleteness}% Complete
            </div>
          </div>
          <div className="w-16 h-16 relative">
            <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
              <path
                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke="#e5e7eb"
                strokeWidth="2"
              />
              <path
                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke="#3b82f6"
                strokeWidth="2"
                strokeDasharray={`${profile.profileCompleteness}, 100`}
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-xs font-semibold text-gray-900">
                {profile.profileCompleteness}%
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar Navigation */}
        <div className="lg:col-span-1">
          <Card className="p-4">
            <h3 className="font-semibold text-gray-900 mb-4">Profile Sections</h3>
            <nav className="space-y-2">
              {PROFILE_SECTIONS.map((section) => {
                const Icon = section.icon;
                const isActive = activeSection === section.id;
                
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`
                      w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors
                      ${isActive 
                        ? 'bg-primary-50 text-primary-700 border border-primary-200' 
                        : 'text-gray-700 hover:bg-gray-50'
                      }
                    `}
                  >
                    <Icon className={`w-4 h-4 ${isActive ? 'text-primary-600' : 'text-gray-500'}`} />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm">{section.name}</div>
                      <div className="text-xs text-gray-500 truncate">
                        {section.description}
                      </div>
                    </div>
                  </button>
                );
              })}
            </nav>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          <Card className="p-6">
            {/* Section Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                {React.createElement(
                  PROFILE_SECTIONS.find(s => s.id === activeSection)?.icon || User,
                  { className: "w-5 h-5 text-primary-600" }
                )}
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">
                    {PROFILE_SECTIONS.find(s => s.id === activeSection)?.name}
                  </h2>
                  <p className="text-sm text-gray-600">
                    {PROFILE_SECTIONS.find(s => s.id === activeSection)?.description}
                  </p>
                </div>
              </div>
              
              {hasUnsavedChanges && (
                <div className="flex items-center gap-2 text-amber-600">
                  <AlertCircle className="w-4 h-4" />
                  <span className="text-sm">Unsaved changes</span>
                </div>
              )}
            </div>

            {/* Section Content */}
            <AnimatePresence mode="wait">
              <motion.div
                key={activeSection}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                {renderActiveSection()}
              </motion.div>
            </AnimatePresence>
          </Card>
        </div>
      </div>
    </div>
  );
};
