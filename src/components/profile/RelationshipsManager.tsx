import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Users, 
  Plus, 
  Edit3, 
  Trash2, 
  Heart,
  Shield,
  Bell,
  Pill,
  Save,
  X,
  UserPlus,
  Search
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import type { UserProfile, UserRelationship } from '../../types/userProfile';
import { userProfileService } from '../../services/userProfileService';
import toast from 'react-hot-toast';

interface RelationshipsManagerProps {
  profile: UserProfile;
  onSave: (data: Partial<UserProfile>) => Promise<void>;
  onChange: () => void;
  isLoading: boolean;
}

interface RelationshipFormData {
  relatedUserId: string;
  relationshipType: 'spouse' | 'child' | 'parent' | 'sibling' | 'friend' | 'caregiver' | 'healthcare_provider' | 'other';
  relationshipLabel: string;
  isPrimaryCaregiver: boolean;
  canViewMedicalInfo: boolean;
  canReceiveAlerts: boolean;
  canManageMedications: boolean;
  notes: string;
}

const RELATIONSHIP_TYPES = [
  { value: 'spouse', label: 'Spouse/Partner' },
  { value: 'child', label: 'Child' },
  { value: 'parent', label: 'Parent' },
  { value: 'sibling', label: 'Sibling' },
  { value: 'friend', label: 'Friend' },
  { value: 'caregiver', label: 'Caregiver' },
  { value: 'healthcare_provider', label: 'Healthcare Provider' },
  { value: 'other', label: 'Other' }
];

export const RelationshipsManager: React.FC<RelationshipsManagerProps> = ({
  profile,
  onSave,
  onChange,
  isLoading
}) => {
  const [relationships, setRelationships] = useState<UserRelationship[]>(profile.relationships);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingRelationship, setEditingRelationship] = useState<UserRelationship | null>(null);
  const [formData, setFormData] = useState<RelationshipFormData>({
    relatedUserId: '',
    relationshipType: 'caregiver',
    relationshipLabel: '',
    isPrimaryCaregiver: false,
    canViewMedicalInfo: false,
    canReceiveAlerts: true,
    canManageMedications: false,
    notes: ''
  });
  const [searchResults, setSearchResults] = useState<UserProfile[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  useEffect(() => {
    setRelationships(profile.relationships);
  }, [profile.relationships]);

  const resetForm = () => {
    setFormData({
      relatedUserId: '',
      relationshipType: 'caregiver',
      relationshipLabel: '',
      isPrimaryCaregiver: false,
      canViewMedicalInfo: false,
      canReceiveAlerts: true,
      canManageMedications: false,
      notes: ''
    });
    setSearchTerm('');
    setSearchResults([]);
  };

  const handleSearch = async (term: string) => {
    setSearchTerm(term);
    if (term.length < 2) {
      setSearchResults([]);
      return;
    }

    try {
      setIsSearching(true);
      const results = await userProfileService.searchProfiles({
        tenantId: profile.tenantId,
        name: term,
        email: term,
        limit: 10
      });
      
      // Filter out current user and existing relationships
      const existingUserIds = new Set([
        profile.userId,
        ...relationships.map(r => r.relatedUserId)
      ]);
      
      const filteredResults = results.filter(p => !existingUserIds.has(p.userId));
      setSearchResults(filteredResults);
    } catch (error) {
      console.error('Error searching users:', error);
      toast.error('Failed to search users');
    } finally {
      setIsSearching(false);
    }
  };

  const handleSelectUser = (selectedProfile: UserProfile) => {
    setFormData(prev => ({
      ...prev,
      relatedUserId: selectedProfile.userId,
      relationshipLabel: `${selectedProfile.personalInfo.firstName} ${selectedProfile.personalInfo.lastName}`
    }));
    setSearchResults([]);
    setSearchTerm(`${selectedProfile.personalInfo.firstName} ${selectedProfile.personalInfo.lastName}`);
  };

  const handleAddRelationship = async () => {
    if (!formData.relatedUserId) {
      toast.error('Please select a user');
      return;
    }

    try {
      await userProfileService.addRelationship(profile.userId, formData.relatedUserId, {
        relationshipType: formData.relationshipType,
        relationshipLabel: formData.relationshipLabel,
        isPrimaryCaregiver: formData.isPrimaryCaregiver,
        canViewMedicalInfo: formData.canViewMedicalInfo,
        canReceiveAlerts: formData.canReceiveAlerts,
        canManageMedications: formData.canManageMedications,
        notes: formData.notes
      });

      // Refresh relationships
      const updatedProfile = await userProfileService.getProfile(profile.userId);
      if (updatedProfile) {
        setRelationships(updatedProfile.relationships);
        onChange();
      }

      setShowAddForm(false);
      resetForm();
      toast.success('Relationship added successfully');
    } catch (error) {
      console.error('Error adding relationship:', error);
      toast.error('Failed to add relationship');
    }
  };

  const handleEditRelationship = (relationship: UserRelationship) => {
    setEditingRelationship(relationship);
    setFormData({
      relatedUserId: relationship.relatedUserId,
      relationshipType: relationship.relationshipType,
      relationshipLabel: relationship.relationshipLabel || '',
      isPrimaryCaregiver: relationship.isPrimaryCaregiver,
      canViewMedicalInfo: relationship.canViewMedicalInfo,
      canReceiveAlerts: relationship.canReceiveAlerts,
      canManageMedications: relationship.canManageMedications,
      notes: relationship.notes || ''
    });
  };

  const handleUpdateRelationship = async () => {
    if (!editingRelationship) return;

    try {
      await userProfileService.updateRelationship(profile.userId, editingRelationship.id, {
        relationshipType: formData.relationshipType,
        relationshipLabel: formData.relationshipLabel,
        isPrimaryCaregiver: formData.isPrimaryCaregiver,
        canViewMedicalInfo: formData.canViewMedicalInfo,
        canReceiveAlerts: formData.canReceiveAlerts,
        canManageMedications: formData.canManageMedications,
        notes: formData.notes
      });

      // Refresh relationships
      const updatedProfile = await userProfileService.getProfile(profile.userId);
      if (updatedProfile) {
        setRelationships(updatedProfile.relationships);
        onChange();
      }

      setEditingRelationship(null);
      resetForm();
      toast.success('Relationship updated successfully');
    } catch (error) {
      console.error('Error updating relationship:', error);
      toast.error('Failed to update relationship');
    }
  };

  const handleRemoveRelationship = async (relationshipId: string) => {
    if (!confirm('Are you sure you want to remove this relationship?')) return;

    try {
      await userProfileService.removeRelationship(profile.userId, relationshipId);
      
      // Refresh relationships
      const updatedProfile = await userProfileService.getProfile(profile.userId);
      if (updatedProfile) {
        setRelationships(updatedProfile.relationships);
        onChange();
      }

      toast.success('Relationship removed successfully');
    } catch (error) {
      console.error('Error removing relationship:', error);
      toast.error('Failed to remove relationship');
    }
  };

  const getRelationshipIcon = (type: string) => {
    switch (type) {
      case 'spouse': return Heart;
      case 'caregiver': return Shield;
      case 'healthcare_provider': return Pill;
      default: return Users;
    }
  };

  const renderRelationshipForm = () => (
    <Card className="p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">
          {editingRelationship ? 'Edit Relationship' : 'Add New Relationship'}
        </h3>
        <Button
          variant="ghost"
          size="sm"
          icon={X}
          onClick={() => {
            setShowAddForm(false);
            setEditingRelationship(null);
            resetForm();
          }}
        />
      </div>

      <div className="space-y-4">
        {/* User Search */}
        {!editingRelationship && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search for User
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                placeholder="Search by name or email..."
              />
            </div>
            
            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="mt-2 border border-gray-200 rounded-lg max-h-40 overflow-y-auto">
                {searchResults.map((user) => (
                  <button
                    key={user.id}
                    onClick={() => handleSelectUser(user)}
                    className="w-full flex items-center gap-3 px-3 py-2 hover:bg-gray-50 text-left"
                  >
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                      <Users className="w-4 h-4 text-gray-600" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">
                        {user.personalInfo.firstName} {user.personalInfo.lastName}
                      </div>
                      <div className="text-sm text-gray-600">
                        {user.contactInfo.primaryEmail}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Relationship Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Relationship Type
            </label>
            <select
              value={formData.relationshipType}
              onChange={(e) => setFormData(prev => ({ ...prev, relationshipType: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
            >
              {RELATIONSHIP_TYPES.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Custom Label
            </label>
            <input
              type="text"
              value={formData.relationshipLabel}
              onChange={(e) => setFormData(prev => ({ ...prev, relationshipLabel: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
              placeholder="e.g., Daughter, Son-in-law"
            />
          </div>
        </div>

        {/* Permissions */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Permissions & Access
          </label>
          <div className="space-y-3">
            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                checked={formData.isPrimaryCaregiver}
                onChange={(e) => setFormData(prev => ({ ...prev, isPrimaryCaregiver: e.target.checked }))}
                className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
              />
              <div>
                <div className="font-medium text-gray-900">Primary Caregiver</div>
                <div className="text-sm text-gray-600">Main person responsible for care</div>
              </div>
            </label>

            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                checked={formData.canViewMedicalInfo}
                onChange={(e) => setFormData(prev => ({ ...prev, canViewMedicalInfo: e.target.checked }))}
                className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
              />
              <div>
                <div className="font-medium text-gray-900">View Medical Information</div>
                <div className="text-sm text-gray-600">Access to medical records and health data</div>
              </div>
            </label>

            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                checked={formData.canReceiveAlerts}
                onChange={(e) => setFormData(prev => ({ ...prev, canReceiveAlerts: e.target.checked }))}
                className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
              />
              <div>
                <div className="font-medium text-gray-900">Receive Alerts</div>
                <div className="text-sm text-gray-600">Get notifications about important events</div>
              </div>
            </label>

            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                checked={formData.canManageMedications}
                onChange={(e) => setFormData(prev => ({ ...prev, canManageMedications: e.target.checked }))}
                className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
              />
              <div>
                <div className="font-medium text-gray-900">Manage Medications</div>
                <div className="text-sm text-gray-600">Add, edit, and monitor medication schedules</div>
              </div>
            </label>
          </div>
        </div>

        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Notes
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
            placeholder="Additional notes about this relationship..."
          />
        </div>

        {/* Actions */}
        <div className="flex gap-3 pt-4">
          <Button
            variant="outline"
            onClick={() => {
              setShowAddForm(false);
              setEditingRelationship(null);
              resetForm();
            }}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            icon={Save}
            onClick={editingRelationship ? handleUpdateRelationship : handleAddRelationship}
            disabled={!formData.relatedUserId}
          >
            {editingRelationship ? 'Update Relationship' : 'Add Relationship'}
          </Button>
        </div>
      </div>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Family & Caregivers</h3>
          <p className="text-sm text-gray-600">
            Manage relationships with family members and caregivers
          </p>
        </div>
        
        {!showAddForm && !editingRelationship && (
          <Button
            variant="primary"
            icon={UserPlus}
            onClick={() => setShowAddForm(true)}
          >
            Add Relationship
          </Button>
        )}
      </div>

      {/* Add/Edit Form */}
      {(showAddForm || editingRelationship) && renderRelationshipForm()}

      {/* Relationships List */}
      <div className="space-y-4">
        {relationships.length === 0 ? (
          <Card className="p-8 text-center">
            <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 className="font-medium text-gray-900 mb-2">No Relationships Added</h4>
            <p className="text-gray-600 mb-4">
              Add family members and caregivers to help coordinate care and stay connected.
            </p>
            <Button
              variant="primary"
              icon={UserPlus}
              onClick={() => setShowAddForm(true)}
            >
              Add First Relationship
            </Button>
          </Card>
        ) : (
          relationships.map((relationship) => {
            const Icon = getRelationshipIcon(relationship.relationshipType);
            
            return (
              <motion.div
                key={relationship.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                        <Icon className="w-6 h-6 text-primary-600" />
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-gray-900">
                          {relationship.relationshipLabel || 
                           RELATIONSHIP_TYPES.find(t => t.value === relationship.relationshipType)?.label}
                        </h4>
                        <div className="flex items-center gap-4 mt-1">
                          <span className="text-sm text-gray-600">
                            {RELATIONSHIP_TYPES.find(t => t.value === relationship.relationshipType)?.label}
                          </span>
                          {relationship.isPrimaryCaregiver && (
                            <span className="px-2 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full">
                              Primary Caregiver
                            </span>
                          )}
                        </div>
                        
                        {/* Permissions */}
                        <div className="flex items-center gap-3 mt-2">
                          {relationship.canViewMedicalInfo && (
                            <div className="flex items-center gap-1 text-xs text-gray-600">
                              <Shield className="w-3 h-3" />
                              Medical Access
                            </div>
                          )}
                          {relationship.canReceiveAlerts && (
                            <div className="flex items-center gap-1 text-xs text-gray-600">
                              <Bell className="w-3 h-3" />
                              Alerts
                            </div>
                          )}
                          {relationship.canManageMedications && (
                            <div className="flex items-center gap-1 text-xs text-gray-600">
                              <Pill className="w-3 h-3" />
                              Medications
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        icon={Edit3}
                        onClick={() => handleEditRelationship(relationship)}
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        icon={Trash2}
                        onClick={() => handleRemoveRelationship(relationship.id)}
                        className="text-red-600 hover:text-red-700"
                      />
                    </div>
                  </div>

                  {relationship.notes && (
                    <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-700">{relationship.notes}</p>
                    </div>
                  )}
                </Card>
              </motion.div>
            );
          })
        )}
      </div>
    </div>
  );
};
