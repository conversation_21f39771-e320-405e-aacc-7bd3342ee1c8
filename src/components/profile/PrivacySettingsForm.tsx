import React, { useState, useEffect } from 'react';
import { Shield, Save } from 'lucide-react';
import { Button } from '../ui/Button';
import type { UserProfile, PrivacySettings } from '../../types/userProfile';

interface PrivacySettingsFormProps {
  profile: UserProfile;
  onSave: (data: Partial<UserProfile>) => Promise<void>;
  onChange: () => void;
  isLoading: boolean;
}

export const PrivacySettingsForm: React.FC<PrivacySettingsFormProps> = ({
  profile,
  onSave,
  onChange,
  isLoading
}) => {
  const [formData, setFormData] = useState<PrivacySettings>(profile.privacySettings);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    setFormData(profile.privacySettings);
    setHasChanges(false);
  }, [profile.privacySettings]);

  const handleInputChange = (field: keyof PrivacySettings, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
    onChange();
  };

  const handleSave = async () => {
    await onSave({ privacySettings: formData });
    setHasChanges(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <Shield className="w-6 h-6 text-primary-600" />
        <div>
          <h3 className="text-lg font-medium text-gray-900">Privacy & Data Sharing</h3>
          <p className="text-sm text-gray-600">Control how your data is shared and used</p>
        </div>
      </div>

      {/* Profile Visibility */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Profile Visibility</label>
        <select
          value={formData.profileVisibility}
          onChange={(e) => handleInputChange('profileVisibility', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
        >
          <option value="private">Private (Only me)</option>
          <option value="family_only">Family Only</option>
          <option value="caregivers_only">Caregivers Only</option>
          <option value="public">Public</option>
        </select>
      </div>

      {/* Data Sharing Options */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Data Sharing Permissions</h4>
        
        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">Share Photos</div>
            <div className="text-sm text-gray-600">Allow family members to view your photos</div>
          </div>
          <input
            type="checkbox"
            checked={formData.sharePhotos}
            onChange={(e) => handleInputChange('sharePhotos', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>

        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">Share Conversations</div>
            <div className="text-sm text-gray-600">Allow caregivers to view AI conversation summaries</div>
          </div>
          <input
            type="checkbox"
            checked={formData.shareConversations}
            onChange={(e) => handleInputChange('shareConversations', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>

        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">Share Medical Information</div>
            <div className="text-sm text-gray-600">Allow authorized caregivers to view medical data</div>
          </div>
          <input
            type="checkbox"
            checked={formData.shareMedicalInfo}
            onChange={(e) => handleInputChange('shareMedicalInfo', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>

        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">Share Location</div>
            <div className="text-sm text-gray-600">Allow caregivers to see your location for safety</div>
          </div>
          <input
            type="checkbox"
            checked={formData.shareLocation}
            onChange={(e) => handleInputChange('shareLocation', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>
      </div>

      <div className="flex justify-end pt-6 border-t border-gray-200">
        <Button
          variant="primary"
          icon={Save}
          onClick={handleSave}
          disabled={!hasChanges || isLoading}
        >
          {isLoading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  );
};
