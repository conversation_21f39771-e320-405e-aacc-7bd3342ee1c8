import React, { useState, useEffect } from 'react';
import { Eye, Save } from 'lucide-react';
import { Button } from '../ui/Button';
import type { UserProfile, AccessibilitySettings } from '../../types/userProfile';

interface AccessibilitySettingsFormProps {
  profile: UserProfile;
  onSave: (data: Partial<UserProfile>) => Promise<void>;
  onChange: () => void;
  isLoading: boolean;
}

export const AccessibilitySettingsForm: React.FC<AccessibilitySettingsFormProps> = ({
  profile,
  onSave,
  onChange,
  isLoading
}) => {
  const [formData, setFormData] = useState<AccessibilitySettings>(profile.accessibilitySettings);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    setFormData(profile.accessibilitySettings);
    setHasChanges(false);
  }, [profile.accessibilitySettings]);

  const handleInputChange = (field: keyof AccessibilitySettings, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
    onChange();
  };

  const handleSave = async () => {
    await onSave({ accessibilitySettings: formData });
    setHasChanges(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <Eye className="w-6 h-6 text-primary-600" />
        <div>
          <h3 className="text-lg font-medium text-gray-900">Accessibility Settings</h3>
          <p className="text-sm text-gray-600">Customize the app for your accessibility needs</p>
        </div>
      </div>

      {/* Visual Accessibility */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Visual Accessibility</h4>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Font Size</label>
          <select
            value={formData.fontSize}
            onChange={(e) => handleInputChange('fontSize', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
          >
            <option value="small">Small</option>
            <option value="medium">Medium</option>
            <option value="large">Large</option>
            <option value="extra-large">Extra Large</option>
          </select>
        </div>

        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">High Contrast</div>
            <div className="text-sm text-gray-600">Increase contrast for better visibility</div>
          </div>
          <input
            type="checkbox"
            checked={formData.highContrast}
            onChange={(e) => handleInputChange('highContrast', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>

        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">Reduced Motion</div>
            <div className="text-sm text-gray-600">Minimize animations and transitions</div>
          </div>
          <input
            type="checkbox"
            checked={formData.reducedMotion}
            onChange={(e) => handleInputChange('reducedMotion', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>
      </div>

      {/* Motor Accessibility */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Motor Accessibility</h4>
        
        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">Large Buttons</div>
            <div className="text-sm text-gray-600">Make buttons and touch targets larger</div>
          </div>
          <input
            type="checkbox"
            checked={formData.largeButtons}
            onChange={(e) => handleInputChange('largeButtons', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>

        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">Voice Control</div>
            <div className="text-sm text-gray-600">Enable voice commands for navigation</div>
          </div>
          <input
            type="checkbox"
            checked={formData.voiceControl}
            onChange={(e) => handleInputChange('voiceControl', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>
      </div>

      {/* Cognitive Accessibility */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Cognitive Accessibility</h4>
        
        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">Simplified Interface</div>
            <div className="text-sm text-gray-600">Show only essential features and options</div>
          </div>
          <input
            type="checkbox"
            checked={formData.simplifiedInterface}
            onChange={(e) => handleInputChange('simplifiedInterface', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>

        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">Extra Confirmations</div>
            <div className="text-sm text-gray-600">Ask for confirmation before important actions</div>
          </div>
          <input
            type="checkbox"
            checked={formData.extraConfirmations}
            onChange={(e) => handleInputChange('extraConfirmations', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>

        <label className="flex items-center justify-between">
          <div>
            <div className="font-medium text-gray-900">Clear Instructions</div>
            <div className="text-sm text-gray-600">Show detailed instructions for each action</div>
          </div>
          <input
            type="checkbox"
            checked={formData.clearInstructions}
            onChange={(e) => handleInputChange('clearInstructions', e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
        </label>
      </div>

      <div className="flex justify-end pt-6 border-t border-gray-200">
        <Button
          variant="primary"
          icon={Save}
          onClick={handleSave}
          disabled={!hasChanges || isLoading}
        >
          {isLoading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  );
};
