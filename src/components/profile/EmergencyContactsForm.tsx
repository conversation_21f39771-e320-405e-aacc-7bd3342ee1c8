import React, { useState, useEffect } from 'react';
import { Phone, Plus, Trash2, Save } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import type { UserProfile, EmergencyContact } from '../../types/userProfile';
import { v4 as uuidv4 } from 'uuid';

interface EmergencyContactsFormProps {
  profile: UserProfile;
  onSave: (data: Partial<UserProfile>) => Promise<void>;
  onChange: () => void;
  isLoading: boolean;
}

export const EmergencyContactsForm: React.FC<EmergencyContactsFormProps> = ({
  profile,
  onSave,
  onChange,
  isLoading
}) => {
  const [contacts, setContacts] = useState<EmergencyContact[]>(profile.emergencyContacts);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    setContacts(profile.emergencyContacts);
    setHasChanges(false);
  }, [profile.emergencyContacts]);

  const addContact = () => {
    const newContact: EmergencyContact = {
      id: uuidv4(),
      name: '',
      relationship: '',
      primaryPhone: '',
      isPrimary: contacts.length === 0,
      canMakeMedicalDecisions: false
    };
    setContacts(prev => [...prev, newContact]);
    setHasChanges(true);
    onChange();
  };

  const updateContact = (id: string, field: keyof EmergencyContact, value: any) => {
    setContacts(prev => prev.map(contact => 
      contact.id === id ? { ...contact, [field]: value } : contact
    ));
    setHasChanges(true);
    onChange();
  };

  const removeContact = (id: string) => {
    setContacts(prev => prev.filter(contact => contact.id !== id));
    setHasChanges(true);
    onChange();
  };

  const handleSave = async () => {
    await onSave({ emergencyContacts: contacts });
    setHasChanges(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Emergency Contacts</h3>
          <p className="text-sm text-gray-600">People to contact in case of emergency</p>
        </div>
        <Button variant="outline" icon={Plus} onClick={addContact}>
          Add Contact
        </Button>
      </div>

      <div className="space-y-4">
        {contacts.map((contact, index) => (
          <Card key={contact.id} className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Name *</label>
                <input
                  type="text"
                  value={contact.name}
                  onChange={(e) => updateContact(contact.id, 'name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  placeholder="Contact name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Relationship</label>
                <input
                  type="text"
                  value={contact.relationship}
                  onChange={(e) => updateContact(contact.id, 'relationship', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  placeholder="e.g., Daughter, Friend"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Primary Phone *</label>
                <input
                  type="tel"
                  value={contact.primaryPhone}
                  onChange={(e) => updateContact(contact.id, 'primaryPhone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  placeholder="(*************"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                <input
                  type="email"
                  value={contact.email || ''}
                  onChange={(e) => updateContact(contact.id, 'email', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div className="mt-4 space-y-3">
              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={contact.isPrimary}
                  onChange={(e) => updateContact(contact.id, 'isPrimary', e.target.checked)}
                  className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                />
                <span className="text-sm text-gray-700">Primary emergency contact</span>
              </label>

              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={contact.canMakeMedicalDecisions}
                  onChange={(e) => updateContact(contact.id, 'canMakeMedicalDecisions', e.target.checked)}
                  className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                />
                <span className="text-sm text-gray-700">Can make medical decisions</span>
              </label>
            </div>

            <div className="flex justify-end mt-4">
              <Button
                variant="ghost"
                size="sm"
                icon={Trash2}
                onClick={() => removeContact(contact.id)}
                className="text-red-600"
              >
                Remove
              </Button>
            </div>
          </Card>
        ))}

        {contacts.length === 0 && (
          <Card className="p-8 text-center">
            <Phone className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 className="font-medium text-gray-900 mb-2">No Emergency Contacts</h4>
            <p className="text-gray-600 mb-4">Add emergency contacts for safety and peace of mind.</p>
            <Button variant="primary" icon={Plus} onClick={addContact}>
              Add First Contact
            </Button>
          </Card>
        )}
      </div>

      <div className="flex justify-end pt-6 border-t border-gray-200">
        <Button
          variant="primary"
          icon={Save}
          onClick={handleSave}
          disabled={!hasChanges || isLoading}
        >
          {isLoading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  );
};
