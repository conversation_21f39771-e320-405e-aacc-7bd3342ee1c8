import React, { useState, useEffect } from 'react';
import { Save } from 'lucide-react';
import { Button } from '../ui/Button';
import type { UserProfile, UserPreferences } from '../../types/userProfile';

interface PreferencesFormProps {
  profile: UserProfile;
  onSave: (data: Partial<UserProfile>) => Promise<void>;
  onChange: () => void;
  isLoading: boolean;
}

export const PreferencesForm: React.FC<PreferencesFormProps> = ({
  profile,
  onSave,
  onChange,
  isLoading
}) => {
  const [formData, setFormData] = useState<UserPreferences>(profile.preferences);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    setFormData(profile.preferences);
    setHasChanges(false);
  }, [profile.preferences]);

  const handleInputChange = (field: keyof UserPreferences, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
    onChange();
  };

  const handleSave = async () => {
    await onSave({ preferences: formData });
    setHasChanges(false);
  };

  return (
    <div className="space-y-6">
      {/* Interface Preferences */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Interface Preferences</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Theme</label>
            <select
              value={formData.theme}
              onChange={(e) => handleInputChange('theme', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
            >
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="auto">Auto (System)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Font Size</label>
            <select
              value={formData.fontSize}
              onChange={(e) => handleInputChange('fontSize', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
            >
              <option value="small">Small</option>
              <option value="medium">Medium</option>
              <option value="large">Large</option>
              <option value="extra-large">Extra Large</option>
            </select>
          </div>
        </div>

        <div className="mt-4 space-y-3">
          <label className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={formData.highContrast}
              onChange={(e) => handleInputChange('highContrast', e.target.checked)}
              className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
            />
            <span className="text-sm text-gray-700">High contrast mode</span>
          </label>

          <label className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={formData.reducedMotion}
              onChange={(e) => handleInputChange('reducedMotion', e.target.checked)}
              className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
            />
            <span className="text-sm text-gray-700">Reduce motion and animations</span>
          </label>
        </div>
      </div>

      {/* AI Conversation Preferences */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">AI Conversation Preferences</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Conversation Style</label>
            <select
              value={formData.conversationStyle}
              onChange={(e) => handleInputChange('conversationStyle', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
            >
              <option value="formal">Formal</option>
              <option value="casual">Casual</option>
              <option value="caring">Caring</option>
              <option value="playful">Playful</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Reminder Frequency</label>
            <select
              value={formData.reminderFrequency}
              onChange={(e) => handleInputChange('reminderFrequency', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
            </select>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end pt-6 border-t border-gray-200">
        <Button
          variant="primary"
          icon={Save}
          onClick={handleSave}
          disabled={!hasChanges || isLoading}
        >
          {isLoading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  );
};
