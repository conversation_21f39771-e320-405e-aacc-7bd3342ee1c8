import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Download,
  Smartphone,
  Wifi,
  WifiOff,
  RotateCcw,
  Bell,
  Settings,
  CheckCircle,
  AlertCircle,
  Info,
  RefreshCw,
} from "lucide-react";
import { Button } from "../ui/Button";
import { Card } from "../ui/Card";
import toast from "react-hot-toast";

interface PWAManagerProps {
  onInstallPrompt?: () => void;
}

export const PWAManager: React.FC<PWAManagerProps> = ({ onInstallPrompt }) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [syncStatus, setSyncStatus] = useState<"idle" | "syncing" | "error">(
    "idle"
  );
  const [notificationPermission, setNotificationPermission] =
    useState<NotificationPermission>("default");
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);

  useEffect(() => {
    // Check if app is already installed
    const isStandalone = window.matchMedia(
      "(display-mode: standalone)"
    ).matches;
    const isInWebAppiOS = (window.navigator as any).standalone === true;
    setIsInstalled(isStandalone || isInWebAppiOS);

    // Listen for online/offline events
    const handleOnline = () => {
      setIsOnline(true);
      toast.success("Back online! Syncing data...");
      handleSync();
    };

    const handleOffline = () => {
      setIsOnline(false);
      toast.error("You are now offline. Some features may be limited.");
    };

    // Listen for install prompt
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e);
      setIsInstallable(true);
    };

    // Listen for app installed
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setIsInstallable(false);
      setDeferredPrompt(null);
      toast.success("MemoCare app installed successfully!");
    };

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);
    window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
    window.addEventListener("appinstalled", handleAppInstalled);

    // Check notification permission
    if ("Notification" in window) {
      setNotificationPermission(Notification.permission);
    }

    // Register service worker
    registerServiceWorker();

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
      window.removeEventListener(
        "beforeinstallprompt",
        handleBeforeInstallPrompt
      );
      window.removeEventListener("appinstalled", handleAppInstalled);
    };
  }, []);

  const registerServiceWorker = async () => {
    if ("serviceWorker" in navigator) {
      try {
        const registration = await navigator.serviceWorker.register("/sw.js");
        console.log("Service Worker registered:", registration);

        // Listen for updates
        registration.addEventListener("updatefound", () => {
          const newWorker = registration.installing;
          if (newWorker) {
            newWorker.addEventListener("statechange", () => {
              if (
                newWorker.state === "installed" &&
                navigator.serviceWorker.controller
              ) {
                toast.success(
                  "App update available! Refresh to get the latest version."
                );
              }
            });
          }
        });
      } catch (error) {
        console.error("Service Worker registration failed:", error);
      }
    }
  };

  const handleInstallApp = async () => {
    if (deferredPrompt) {
      deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;

      if (outcome === "accepted") {
        toast.success("Installing MemoCare app...");
      } else {
        toast.info("App installation cancelled");
      }

      setDeferredPrompt(null);
      setIsInstallable(false);
    }
  };

  const handleRequestNotifications = async () => {
    if ("Notification" in window) {
      const permission = await Notification.requestPermission();
      setNotificationPermission(permission);

      if (permission === "granted") {
        toast.success(
          "Notifications enabled! You'll receive medication reminders."
        );

        // Send a test notification
        new Notification("MemoCare Notifications Enabled", {
          body: "You'll now receive important reminders and updates.",
          icon: "/icon-192x192.png",
          badge: "/icon-192x192.png",
        });
      } else {
        toast.error(
          "Notifications denied. You can enable them later in settings."
        );
      }
    }
  };

  const handleSync = async () => {
    setSyncStatus("syncing");

    try {
      // Simulate sync process
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // In a real app, this would sync offline data with the server
      console.log("Syncing offline data...");

      setSyncStatus("idle");
      toast.success("Data synced successfully!");
    } catch (error) {
      setSyncStatus("error");
      toast.error("Sync failed. Will retry automatically.");
      console.error("Sync error:", error);
    }
  };

  const getConnectionStatus = () => {
    if (isOnline) {
      return {
        icon: Wifi,
        color: "text-green-600",
        bgColor: "bg-green-100",
        text: "Online",
        description: "All features available",
      };
    } else {
      return {
        icon: WifiOff,
        color: "text-red-600",
        bgColor: "bg-red-100",
        text: "Offline",
        description: "Limited functionality",
      };
    }
  };

  const getNotificationStatus = () => {
    switch (notificationPermission) {
      case "granted":
        return {
          icon: CheckCircle,
          color: "text-green-600",
          bgColor: "bg-green-100",
          text: "Enabled",
          description: "You'll receive reminders",
        };
      case "denied":
        return {
          icon: AlertCircle,
          color: "text-red-600",
          bgColor: "bg-red-100",
          text: "Disabled",
          description: "Enable in browser settings",
        };
      default:
        return {
          icon: Bell,
          color: "text-yellow-600",
          bgColor: "bg-yellow-100",
          text: "Not Set",
          description: "Click to enable",
        };
    }
  };

  const connectionStatus = getConnectionStatus();
  const notificationStatus = getNotificationStatus();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">App Settings</h2>
        <p className="text-gray-600">Manage your MemoCare app experience</p>
      </div>

      {/* Installation */}
      {!isInstalled && (
        <Card className="p-6">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center flex-shrink-0">
              <Download className="w-6 h-6 text-blue-600" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Install MemoCare App
              </h3>
              <p className="text-gray-600 mb-4">
                Install MemoCare on your device for the best experience. You'll
                get:
              </p>
              <ul className="text-sm text-gray-600 space-y-1 mb-4">
                <li>• Faster loading and better performance</li>
                <li>• Works offline for viewing photos and medications</li>
                <li>• Push notifications for medication reminders</li>
                <li>• Easy access from your home screen</li>
              </ul>
              {isInstallable ? (
                <Button
                  variant="primary"
                  icon={Smartphone}
                  onClick={handleInstallApp}
                >
                  Install App
                </Button>
              ) : (
                <div className="text-sm text-gray-500">
                  Installation will be available when you visit from a supported
                  browser
                </div>
              )}
            </div>
          </div>
        </Card>
      )}

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Connection Status */}
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div
              className={`w-10 h-10 ${connectionStatus.bgColor} rounded-lg flex items-center justify-center`}
            >
              <connectionStatus.icon
                className={`w-5 h-5 ${connectionStatus.color}`}
              />
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Connection</h4>
              <p className="text-sm text-gray-600">{connectionStatus.text}</p>
              <p className="text-xs text-gray-500">
                {connectionStatus.description}
              </p>
            </div>
          </div>
        </Card>

        {/* Notification Status */}
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div
              className={`w-10 h-10 ${notificationStatus.bgColor} rounded-lg flex items-center justify-center`}
            >
              <notificationStatus.icon
                className={`w-5 h-5 ${notificationStatus.color}`}
              />
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Notifications</h4>
              <p className="text-sm text-gray-600">{notificationStatus.text}</p>
              <p className="text-xs text-gray-500">
                {notificationStatus.description}
              </p>
            </div>
          </div>
          {notificationPermission === "default" && (
            <Button
              variant="outline"
              size="sm"
              className="mt-3 w-full"
              onClick={handleRequestNotifications}
            >
              Enable Notifications
            </Button>
          )}
        </Card>

        {/* Sync Status */}
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div
              className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                syncStatus === "syncing"
                  ? "bg-blue-100"
                  : syncStatus === "error"
                  ? "bg-red-100"
                  : "bg-green-100"
              }`}
            >
              {syncStatus === "syncing" ? (
                <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />
              ) : syncStatus === "error" ? (
                <AlertCircle className="w-5 h-5 text-red-600" />
              ) : (
                <RotateCcw className="w-5 h-5 text-green-600" />
              )}
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Data Sync</h4>
              <p className="text-sm text-gray-600">
                {syncStatus === "syncing"
                  ? "Syncing..."
                  : syncStatus === "error"
                  ? "Sync Error"
                  : "Up to Date"}
              </p>
              <p className="text-xs text-gray-500">
                {syncStatus === "syncing"
                  ? "Please wait"
                  : syncStatus === "error"
                  ? "Will retry automatically"
                  : "All data synchronized"}
              </p>
            </div>
          </div>
          {syncStatus === "error" && (
            <Button
              variant="outline"
              size="sm"
              className="mt-3 w-full"
              onClick={handleSync}
            >
              Retry Sync
            </Button>
          )}
        </Card>
      </div>

      {/* Offline Features */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Offline Features
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">
              Available Offline:
            </h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                View uploaded photos
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                Check medication schedules
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                Mark medications as taken
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                View conversation history
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-700 mb-2">
              Requires Internet:
            </h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li className="flex items-center gap-2">
                <AlertCircle className="w-4 h-4 text-yellow-500" />
                Upload new photos
              </li>
              <li className="flex items-center gap-2">
                <AlertCircle className="w-4 h-4 text-yellow-500" />
                Start new conversations
              </li>
              <li className="flex items-center gap-2">
                <AlertCircle className="w-4 h-4 text-yellow-500" />
                Sync with caregivers
              </li>
              <li className="flex items-center gap-2">
                <AlertCircle className="w-4 h-4 text-yellow-500" />
                Update medication schedules
              </li>
            </ul>
          </div>
        </div>
      </Card>

      {/* App Info */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          App Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <h4 className="font-medium text-gray-700">Version</h4>
            <p className="text-sm text-gray-600">1.0.0</p>
          </div>
          <div>
            <h4 className="font-medium text-gray-700">Last Updated</h4>
            <p className="text-sm text-gray-600">Today</p>
          </div>
          <div>
            <h4 className="font-medium text-gray-700">Storage Used</h4>
            <p className="text-sm text-gray-600">~2.5 MB</p>
          </div>
        </div>
      </Card>

      {/* Tips */}
      <Card className="p-6 bg-blue-50 border-blue-200">
        <div className="flex items-start gap-3">
          <Info className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-800 mb-2">
              Tips for Best Experience
            </h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Install the app for faster access and offline features</li>
              <li>• Enable notifications to never miss medication reminders</li>
              <li>
                • The app works offline, but sync when connected for latest
                updates
              </li>
              <li>• Photos and data are automatically backed up when online</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};
