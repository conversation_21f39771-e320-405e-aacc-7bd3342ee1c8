import React, { useEffect } from "react";
import { toast } from "react-hot-toast";
import { useNotificationStore } from "../../store/notificationStore";
import { useAuthStore } from "../../store/authStore";

export const NotificationManager: React.FC = () => {
  const { requestPermission, scheduleReminder, sendNotification } =
    useNotificationStore();
  const { user } = useAuthStore();

  useEffect(() => {
    // Request notification permission only once when user first enables notifications
    if (
      user?.preferences.notifications &&
      Notification.permission === "default"
    ) {
      requestPermission().then((granted) => {
        if (granted) {
          toast.success("Notifications enabled successfully!");
        } else {
          toast.error("Notifications permission denied");
        }
      });
    }
  }, [user?.id, requestPermission]); // Only depend on user ID, not the entire user object

  useEffect(() => {
    // Register service worker for background notifications
    if ("serviceWorker" in navigator) {
      navigator.serviceWorker
        .register("/sw.js")
        .then((registration) => {
          console.log("SW registered: ", registration);
        })
        .catch((registrationError) => {
          console.log("SW registration failed: ", registrationError);
        });
    }
  }, []);

  // Listen for medication reminders
  useEffect(() => {
    const handleMedicationReminder = (event: CustomEvent) => {
      const { medication, time } = event.detail;

      sendNotification(`Time for ${medication.name}`, {
        body: `Don't forget to take your ${medication.dosage} of ${medication.name}`,
        icon: "/pwa-192x192.png",
        badge: "/pwa-192x192.png",
        tag: "medication-reminder",
        requireInteraction: true,
        actions: [
          {
            action: "taken",
            title: "Mark as Taken",
          },
          {
            action: "snooze",
            title: "Remind me in 10 minutes",
          },
        ],
      });
    };

    window.addEventListener(
      "medication-reminder",
      handleMedicationReminder as EventListener
    );

    return () => {
      window.removeEventListener(
        "medication-reminder",
        handleMedicationReminder as EventListener
      );
    };
  }, [sendNotification]);

  return null; // This component doesn't render anything
};
