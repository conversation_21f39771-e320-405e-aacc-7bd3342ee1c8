import React from 'react';
import { TrendingUp, TrendingDown, Minus, Heart, MessageCircle, Pill, Calendar } from 'lucide-react';
import { format, startOfWeek, endOfWeek } from 'date-fns';
import { Card } from '../ui/Card';
import type { CaregiverInsight } from '../../types';

interface CaregiverDashboardProps {
  insights: CaregiverInsight[];
}

export const CaregiverDashboard: React.FC<CaregiverDashboardProps> = ({ insights }) => {
  const weekStart = startOfWeek(new Date());
  const weekEnd = endOfWeek(new Date());

  const getTrendIcon = (trend?: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-success-600" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-error-600" />;
      case 'stable': return <Minus className="w-4 h-4 text-gray-500" />;
      default: return null;
    }
  };

  const getInsightIcon = (type: CaregiverInsight['type']) => {
    switch (type) {
      case 'engagement': return MessageCircle;
      case 'medication': return Pill;
      case 'mood': return Heart;
      case 'activity': return Calendar;
      default: return Heart;
    }
  };

  const getSeverityColor = (severity?: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'high': return 'border-l-error-500 bg-error-50';
      case 'medium': return 'border-l-warm-500 bg-warm-50';
      case 'low': return 'border-l-success-500 bg-success-50';
      default: return 'border-l-primary-500 bg-primary-50';
    }
  };

  // Mock data for demo
  const mockStats = [
    {
      label: 'Daily Conversations',
      value: '12 min',
      trend: 'up' as const,
      change: '+15%',
      description: 'Average conversation time this week'
    },
    {
      label: 'Medication Adherence',
      value: '95%',
      trend: 'stable' as const,
      change: '0%',
      description: 'Medications taken on time'
    },
    {
      label: 'Photos Discussed',
      value: '8',
      trend: 'up' as const,
      change: '+3',
      description: 'New memories shared this week'
    },
    {
      label: 'Overall Mood',
      value: 'Positive',
      trend: 'up' as const,
      change: 'Improved',
      description: 'Based on conversation analysis'
    }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Weekly Overview</h2>
        <p className="text-gray-600 mb-6">
          {format(weekStart, 'MMM d')} - {format(weekEnd, 'MMM d, yyyy')}
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
          {mockStats.map((stat, index) => (
            <Card key={index} className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-600">{stat.label}</h3>
                <div className="flex items-center gap-1">
                  {getTrendIcon(stat.trend)}
                  <span className={`text-xs font-medium ${
                    stat.trend === 'up' ? 'text-success-600' :
                    stat.trend === 'down' ? 'text-error-600' :
                    'text-gray-500'
                  }`}>
                    {stat.change}
                  </span>
                </div>
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <p className="text-sm text-gray-500">{stat.description}</p>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Key Insights */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Insights</h3>
        <div className="space-y-3">
          {insights.length === 0 ? (
            <Card className="text-center py-8">
              <Heart className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-600">Insights will appear here as data is collected</p>
              <p className="text-sm text-gray-500 mt-1">
                Continue using the app to see personalized care insights
              </p>
            </Card>
          ) : (
            insights.map((insight) => {
              const Icon = getInsightIcon(insight.type);
              return (
                <Card
                  key={insight.id}
                  className={`border-l-4 ${getSeverityColor(insight.severity)} space-y-2`}
                >
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 rounded-lg bg-white flex items-center justify-center">
                      <Icon className="w-4 h-4 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{insight.title}</h4>
                      <p className="text-gray-600 text-sm">{insight.description}</p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-lg font-semibold text-gray-900">
                          {insight.value}
                        </span>
                        <span className="text-xs text-gray-500">
                          {format(insight.date, 'MMM d')}
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>
              );
            })
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <Card className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
        <div className="grid grid-cols-2 gap-3">
          <button className="p-4 text-left rounded-xl border border-gray-200 hover:border-primary-300 hover:bg-primary-50 transition-colors">
            <MessageCircle className="w-6 h-6 text-primary-600 mb-2" />
            <p className="font-medium text-gray-900">Start Conversation</p>
            <p className="text-sm text-gray-500">Begin a photo chat</p>
          </button>
          <button className="p-4 text-left rounded-xl border border-gray-200 hover:border-primary-300 hover:bg-primary-50 transition-colors">
            <Pill className="w-6 h-6 text-primary-600 mb-2" />
            <p className="font-medium text-gray-900">Check Medications</p>
            <p className="text-sm text-gray-500">Review today's schedule</p>
          </button>
        </div>
      </Card>
    </div>
  );
};