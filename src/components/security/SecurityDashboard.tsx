import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Shield, 
  AlertTriangle, 
  Eye, 
  Lock, 
  Activity,
  Clock,
  MapPin,
  Smartphone,
  Monitor,
  Download,
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { useAuthStore } from '../../store/authStore';
import { useTenantStore } from '../../store/tenantStore';
import { securityService } from '../../services/securityService';
import type { AuditLog, SecurityAlert } from '../../types/security';

interface SecurityDashboardProps {
  className?: string;
}

interface SecurityMetrics {
  totalLogins: number;
  failedLogins: number;
  dataAccesses: number;
  securityAlerts: number;
  lastLogin: Date | null;
  activeDevices: number;
}

interface RecentActivity {
  id: string;
  action: string;
  resource: string;
  timestamp: Date;
  ipAddress: string;
  location: string;
  device: string;
  status: 'success' | 'warning' | 'error';
}

export const SecurityDashboard: React.FC<SecurityDashboardProps> = ({ className = '' }) => {
  const { user } = useAuthStore();
  const { currentTenant } = useTenantStore();
  const [metrics, setMetrics] = useState<SecurityMetrics>({
    totalLogins: 0,
    failedLogins: 0,
    dataAccesses: 0,
    securityAlerts: 0,
    lastLogin: null,
    activeDevices: 1
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [securityAlerts, setSecurityAlerts] = useState<SecurityAlert[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user?.id) {
      loadSecurityData();
    }
  }, [user?.id]);

  const loadSecurityData = async () => {
    try {
      setIsLoading(true);
      
      // Load mock data for demonstration
      // In a real implementation, this would fetch from the database
      setMetrics({
        totalLogins: 47,
        failedLogins: 2,
        dataAccesses: 156,
        securityAlerts: 1,
        lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        activeDevices: 2
      });

      setRecentActivity([
        {
          id: '1',
          action: 'Login',
          resource: 'Authentication',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          ipAddress: '*************',
          location: 'San Francisco, CA',
          device: 'iPhone 14',
          status: 'success'
        },
        {
          id: '2',
          action: 'Photo Upload',
          resource: 'Photos',
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
          ipAddress: '*************',
          location: 'San Francisco, CA',
          device: 'iPhone 14',
          status: 'success'
        },
        {
          id: '3',
          action: 'Profile Update',
          resource: 'User Profile',
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
          ipAddress: '*************',
          location: 'San Francisco, CA',
          device: 'MacBook Pro',
          status: 'success'
        },
        {
          id: '4',
          action: 'Failed Login',
          resource: 'Authentication',
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
          ipAddress: '***********',
          location: 'Unknown',
          device: 'Unknown',
          status: 'error'
        }
      ]);

      setSecurityAlerts([
        {
          id: '1',
          type: 'multiple_failed_logins',
          severity: 'medium',
          title: 'Multiple Failed Login Attempts',
          description: 'Someone attempted to log into your account multiple times with incorrect credentials.',
          userId: user?.id,
          tenantId: currentTenant?.id,
          ipAddress: '***********',
          details: { attempts: 3, timeWindow: '5 minutes' },
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
          status: 'active'
        }
      ]);

      // Log security dashboard access
      await securityService.logAuditEvent(
        user!.id,
        'data_viewed',
        'security_dashboard',
        { section: 'security_overview' },
        undefined,
        currentTenant?.id
      );
    } catch (error) {
      console.error('Error loading security data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportSecurityReport = async () => {
    try {
      // In a real implementation, generate and download security report
      const reportData = {
        user: user?.email,
        tenant: currentTenant?.name,
        generatedAt: new Date().toISOString(),
        metrics,
        recentActivity,
        securityAlerts
      };

      const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `security-report-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      // Log export activity
      await securityService.logAuditEvent(
        user!.id,
        'data_exported',
        'security_report',
        { reportType: 'security_overview' },
        undefined,
        currentTenant?.id
      );
    } catch (error) {
      console.error('Error exporting security report:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'warning': return <AlertCircle className="w-4 h-4 text-yellow-600" />;
      case 'error': return <XCircle className="w-4 h-4 text-red-600" />;
      default: return <AlertCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-blue-600 bg-blue-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    if (diffHours > 0) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    return 'Just now';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-primary-600 border-t-transparent rounded-full mx-auto mb-4" />
          <p className="text-gray-600">Loading security data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Shield className="w-6 h-6 text-primary-600" />
          <h2 className="text-xl font-semibold text-gray-900">Security Dashboard</h2>
        </div>
        <Button
          variant="outline"
          icon={Download}
          onClick={handleExportSecurityReport}
        >
          Export Report
        </Button>
      </div>

      {/* Security Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Logins</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.totalLogins}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Failed Logins</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.failedLogins}</p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
              <XCircle className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Data Accesses</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.dataAccesses}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <Eye className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Security Alerts</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.securityAlerts}</p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Security Alerts */}
      {securityAlerts.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Security Alerts</h3>
          <div className="space-y-4">
            {securityAlerts.map((alert) => (
              <motion.div
                key={alert.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-start gap-4 p-4 border border-gray-200 rounded-lg"
              >
                <div className="flex-shrink-0">
                  <AlertTriangle className="w-5 h-5 text-yellow-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-gray-900">{alert.title}</h4>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(alert.severity)}`}>
                      {alert.severity}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{alert.description}</p>
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <span className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {formatTimeAgo(alert.timestamp)}
                    </span>
                    {alert.ipAddress && (
                      <span className="flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        {alert.ipAddress}
                      </span>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </Card>
      )}

      {/* Recent Activity */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-4">
          {recentActivity.map((activity) => (
            <div key={activity.id} className="flex items-center gap-4 p-3 hover:bg-gray-50 rounded-lg transition-colors">
              <div className="flex-shrink-0">
                {getStatusIcon(activity.status)}
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-medium text-gray-900">{activity.action}</span>
                  <span className="text-sm text-gray-500">on {activity.resource}</span>
                </div>
                <div className="flex items-center gap-4 text-xs text-gray-500">
                  <span className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {formatTimeAgo(activity.timestamp)}
                  </span>
                  <span className="flex items-center gap-1">
                    <MapPin className="w-3 h-3" />
                    {activity.location}
                  </span>
                  <span className="flex items-center gap-1">
                    {activity.device.includes('iPhone') ? (
                      <Smartphone className="w-3 h-3" />
                    ) : (
                      <Monitor className="w-3 h-3" />
                    )}
                    {activity.device}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Account Security */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Account Security</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Login Information</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Last Login:</span>
                <span className="text-gray-900">
                  {metrics.lastLogin ? formatTimeAgo(metrics.lastLogin) : 'Never'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Active Devices:</span>
                <span className="text-gray-900">{metrics.activeDevices}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Two-Factor Auth:</span>
                <span className="text-green-600">Enabled</span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 mb-3">Data Protection</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Encryption:</span>
                <span className="text-green-600">AES-256</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Backup:</span>
                <span className="text-green-600">Encrypted</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">HIPAA Compliant:</span>
                <span className="text-green-600">Yes</span>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};
