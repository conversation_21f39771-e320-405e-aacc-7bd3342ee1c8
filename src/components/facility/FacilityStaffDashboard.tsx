import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  MessageCircle,
  Calendar,
  Heart,
  Clock,
  AlertCircle,
  CheckCircle,
  Camera,
  Phone,
  Mail,
  Activity,
  Star,
  Plus,
  Search,
  Filter
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { useAuthStore } from '../../store/authStore';

interface ResidentProfile {
  id: string;
  name: string;
  room: string;
  age: number;
  familyMembers: number;
  lastActivity: string;
  engagementLevel: 'high' | 'medium' | 'low';
  recentPhotos: number;
  conversationsToday: number;
  medicationCompliance: number;
  mood: 'happy' | 'neutral' | 'concerned';
  notes: string;
}

interface FamilyMember {
  id: string;
  name: string;
  relationship: string;
  lastContact: string;
  contactFrequency: 'daily' | 'weekly' | 'monthly';
}

type StaffView = 'residents' | 'activities' | 'families' | 'notes';

export const FacilityStaffDashboard: React.FC = () => {
  const { user } = useAuthStore();
  const [activeView, setActiveView] = useState<StaffView>('residents');
  const [residents, setResidents] = useState<ResidentProfile[]>([]);
  const [selectedResident, setSelectedResident] = useState<ResidentProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadResidentData();
  }, []);

  const loadResidentData = async () => {
    setIsLoading(true);
    
    // Simulate loading demo data for care staff
    setTimeout(() => {
      setResidents([
        {
          id: '1',
          name: 'Margaret Thompson',
          room: '214',
          age: 78,
          familyMembers: 4,
          lastActivity: '2 hours ago',
          engagementLevel: 'high',
          recentPhotos: 8,
          conversationsToday: 12,
          medicationCompliance: 95,
          mood: 'happy',
          notes: 'Very engaged today. Enjoyed looking at family photos from the weekend.'
        },
        {
          id: '2',
          name: 'Robert Chen',
          room: '208',
          age: 82,
          familyMembers: 3,
          lastActivity: '4 hours ago',
          engagementLevel: 'medium',
          recentPhotos: 3,
          conversationsToday: 7,
          medicationCompliance: 88,
          mood: 'neutral',
          notes: 'Quiet morning. Son called during lunch which brightened his mood.'
        },
        {
          id: '3',
          name: 'Dorothy Williams',
          room: '301',
          age: 75,
          familyMembers: 6,
          lastActivity: '1 hour ago',
          engagementLevel: 'high',
          recentPhotos: 12,
          conversationsToday: 18,
          medicationCompliance: 100,
          mood: 'happy',
          notes: 'Excellent day! Granddaughter shared new baby photos. Very talkative.'
        },
        {
          id: '4',
          name: 'James Rodriguez',
          room: '156',
          age: 80,
          familyMembers: 2,
          lastActivity: '6 hours ago',
          engagementLevel: 'low',
          recentPhotos: 1,
          conversationsToday: 3,
          medicationCompliance: 75,
          mood: 'concerned',
          notes: 'Seems withdrawn today. May need extra encouragement for family interaction.'
        }
      ]);

      setIsLoading(false);
    }, 800);
  };

  const navigation = [
    { id: 'residents', label: 'My Residents', icon: Users },
    { id: 'activities', label: 'Daily Activities', icon: Calendar },
    { id: 'families', label: 'Family Communications', icon: MessageCircle },
    { id: 'notes', label: 'Care Notes', icon: Activity }
  ];

  const getEngagementColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getMoodIcon = (mood: string) => {
    switch (mood) {
      case 'happy': return '😊';
      case 'neutral': return '😐';
      case 'concerned': return '😟';
      default: return '😐';
    }
  };

  const renderResidents = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">My Residents</h2>
        <div className="flex space-x-3">
          <Button variant="secondary" icon={Filter}>Filter</Button>
          <Button variant="primary" icon={Plus}>Add Note</Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {residents.map((resident) => (
          <Card key={resident.id} className="p-6 hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => setSelectedResident(resident)}>
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <Users className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{resident.name}</h3>
                  <p className="text-sm text-gray-600">Room {resident.room}</p>
                </div>
              </div>
              <div className="text-2xl">{getMoodIcon(resident.mood)}</div>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Engagement</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getEngagementColor(resident.engagementLevel)}`}>
                  {resident.engagementLevel}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Family Members</span>
                <span className="text-sm font-medium text-gray-900">{resident.familyMembers}</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Conversations Today</span>
                <span className="text-sm font-medium text-gray-900">{resident.conversationsToday}</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Medication Compliance</span>
                <div className="flex items-center space-x-2">
                  <div className="w-16 bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${resident.medicationCompliance >= 90 ? 'bg-green-600' : 'bg-yellow-600'}`}
                      style={{ width: `${resident.medicationCompliance}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900">{resident.medicationCompliance}%</span>
                </div>
              </div>

              <div className="pt-3 border-t border-gray-200">
                <p className="text-sm text-gray-600 line-clamp-2">{resident.notes}</p>
                <p className="text-xs text-gray-500 mt-2">Last activity: {resident.lastActivity}</p>
              </div>
            </div>

            <div className="mt-4 flex space-x-2">
              <Button variant="secondary" size="sm" icon={MessageCircle} className="flex-1">
                Chat
              </Button>
              <Button variant="secondary" size="sm" icon={Phone} className="flex-1">
                Call Family
              </Button>
            </div>
          </Card>
        ))}
      </div>

      {/* Resident Detail Modal */}
      {selectedResident && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-6">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                    <Users className="w-8 h-8 text-blue-600" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">{selectedResident.name}</h2>
                    <p className="text-gray-600">Room {selectedResident.room} • Age {selectedResident.age}</p>
                  </div>
                </div>
                <Button variant="secondary" onClick={() => setSelectedResident(null)}>
                  Close
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-3">Today's Activity</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Conversations</span>
                      <span className="font-medium">{selectedResident.conversationsToday}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Photos Shared</span>
                      <span className="font-medium">{selectedResident.recentPhotos}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Family Members Active</span>
                      <span className="font-medium">{selectedResident.familyMembers}</span>
                    </div>
                  </div>
                </Card>

                <Card className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-3">Care Status</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Mood</span>
                      <span className="text-2xl">{getMoodIcon(selectedResident.mood)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Engagement</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getEngagementColor(selectedResident.engagementLevel)}`}>
                        {selectedResident.engagementLevel}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Medication</span>
                      <span className="font-medium text-green-600">{selectedResident.medicationCompliance}%</span>
                    </div>
                  </div>
                </Card>
              </div>

              <Card className="p-4 mt-6">
                <h3 className="font-semibold text-gray-900 mb-3">Care Notes</h3>
                <p className="text-gray-700 mb-4">{selectedResident.notes}</p>
                <div className="flex space-x-3">
                  <Button variant="primary" icon={Plus}>Add Note</Button>
                  <Button variant="secondary" icon={MessageCircle}>Contact Family</Button>
                  <Button variant="secondary" icon={Camera}>Share Photo</Button>
                </div>
              </Card>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderActivities = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-900">Daily Activities</h2>
      <Card className="p-6">
        <div className="text-center py-12">
          <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Activity Tracking</h3>
          <p className="text-gray-600">Track daily activities and engagement for your residents.</p>
          <Button variant="primary" className="mt-4" icon={Plus}>
            Log Activity
          </Button>
        </div>
      </Card>
    </div>
  );

  const renderFamilies = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-900">Family Communications</h2>
      <Card className="p-6">
        <div className="text-center py-12">
          <MessageCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Family Connections</h3>
          <p className="text-gray-600">Facilitate communication between residents and their families.</p>
          <Button variant="primary" className="mt-4" icon={Plus}>
            Send Update
          </Button>
        </div>
      </Card>
    </div>
  );

  const renderNotes = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-900">Care Notes</h2>
      <Card className="p-6">
        <div className="text-center py-12">
          <Activity className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Care Documentation</h3>
          <p className="text-gray-600">Document care observations and resident progress.</p>
          <Button variant="primary" className="mt-4" icon={Plus}>
            Add Note
          </Button>
        </div>
      </Card>
    </div>
  );

  const renderContent = () => {
    switch (activeView) {
      case 'residents':
        return renderResidents();
      case 'activities':
        return renderActivities();
      case 'families':
        return renderFamilies();
      case 'notes':
        return renderNotes();
      default:
        return renderResidents();
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading care dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Care Staff Portal</h1>
              <p className="text-gray-600">Sunny Meadows Memory Care</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-green-600">
                    {user?.email?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <span className="text-sm font-medium text-gray-900">{user?.email}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex space-x-8">
          {/* Sidebar Navigation */}
          <div className="w-64 flex-shrink-0">
            <nav className="space-y-2">
              {navigation.map((item) => (
                <button
                  key={item.id}
                  onClick={() => setActiveView(item.id as StaffView)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg transition-colors ${
                    activeView === item.id
                      ? 'bg-green-100 text-green-700 font-medium'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <item.icon className="w-5 h-5" />
                  <span>{item.label}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <motion.div
              key={activeView}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {renderContent()}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};
