import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  BarChart3,
  Heart,
  MessageCircle,
  Calendar,
  TrendingUp,
  AlertCircle,
  Clock,
  UserCheck,
  Activity,
  Star,
  Phone,
  Mail,
  Settings,
  Download,
  Filter,
  Search,
  Plus
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { useAuthStore } from '../../store/authStore';

interface ResidentMetrics {
  totalResidents: number;
  activeToday: number;
  familyConnections: number;
  avgSatisfaction: number;
  engagementRate: number;
  medicationCompliance: number;
}

interface FamilyEngagement {
  residentId: string;
  residentName: string;
  familyMembers: number;
  lastActivity: string;
  engagementScore: number;
  recentPhotos: number;
  conversationsToday: number;
  satisfactionRating: number;
}

type DashboardView = 'overview' | 'residents' | 'families' | 'staff' | 'analytics' | 'settings';

export const FacilityAdminDashboard: React.FC = () => {
  const { user } = useAuthStore();
  const [activeView, setActiveView] = useState<DashboardView>('overview');
  const [metrics, setMetrics] = useState<ResidentMetrics>({
    totalResidents: 0,
    activeToday: 0,
    familyConnections: 0,
    avgSatisfaction: 0,
    engagementRate: 0,
    medicationCompliance: 0
  });
  const [familyEngagement, setFamilyEngagement] = useState<FamilyEngagement[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setIsLoading(true);
    
    // Simulate loading demo data for Sunny Meadows facility
    setTimeout(() => {
      setMetrics({
        totalResidents: 48,
        activeToday: 32,
        familyConnections: 156,
        avgSatisfaction: 4.7,
        engagementRate: 78,
        medicationCompliance: 94
      });

      setFamilyEngagement([
        {
          residentId: '1',
          residentName: 'Margaret Thompson',
          familyMembers: 4,
          lastActivity: '2 hours ago',
          engagementScore: 92,
          recentPhotos: 8,
          conversationsToday: 12,
          satisfactionRating: 5.0
        },
        {
          residentId: '2',
          residentName: 'Robert Chen',
          familyMembers: 3,
          lastActivity: '4 hours ago',
          engagementScore: 85,
          recentPhotos: 3,
          conversationsToday: 7,
          satisfactionRating: 4.8
        },
        {
          residentId: '3',
          residentName: 'Dorothy Williams',
          familyMembers: 6,
          lastActivity: '1 hour ago',
          engagementScore: 96,
          recentPhotos: 12,
          conversationsToday: 18,
          satisfactionRating: 4.9
        },
        {
          residentId: '4',
          residentName: 'James Rodriguez',
          familyMembers: 2,
          lastActivity: '6 hours ago',
          engagementScore: 67,
          recentPhotos: 1,
          conversationsToday: 3,
          satisfactionRating: 4.2
        }
      ]);

      setIsLoading(false);
    }, 1000);
  };

  const navigation = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'residents', label: 'Residents', icon: Users },
    { id: 'families', label: 'Family Engagement', icon: Heart },
    { id: 'staff', label: 'Staff Management', icon: UserCheck },
    { id: 'analytics', label: 'Analytics', icon: TrendingUp },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Residents</p>
              <p className="text-3xl font-bold text-gray-900">{metrics.totalResidents}</p>
              <p className="text-sm text-green-600">
                <span className="inline-flex items-center">
                  <TrendingUp className="w-4 h-4 mr-1" />
                  {metrics.activeToday} active today
                </span>
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Family Connections</p>
              <p className="text-3xl font-bold text-gray-900">{metrics.familyConnections}</p>
              <p className="text-sm text-green-600">
                <span className="inline-flex items-center">
                  <Heart className="w-4 h-4 mr-1" />
                  {metrics.engagementRate}% engagement rate
                </span>
              </p>
            </div>
            <div className="p-3 bg-pink-100 rounded-lg">
              <Heart className="w-6 h-6 text-pink-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Satisfaction Score</p>
              <p className="text-3xl font-bold text-gray-900">{metrics.avgSatisfaction}</p>
              <p className="text-sm text-green-600">
                <span className="inline-flex items-center">
                  <Star className="w-4 h-4 mr-1" />
                  Excellent rating
                </span>
              </p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-lg">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Family Activity</h3>
          <div className="space-y-4">
            {familyEngagement.slice(0, 4).map((resident) => (
              <div key={resident.residentId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <Users className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{resident.residentName}</p>
                    <p className="text-sm text-gray-600">{resident.conversationsToday} conversations today</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-green-600">{resident.engagementScore}%</p>
                  <p className="text-xs text-gray-500">{resident.lastActivity}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Today's Highlights</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
              <MessageCircle className="w-5 h-5 text-green-600" />
              <div>
                <p className="font-medium text-gray-900">127 conversations started</p>
                <p className="text-sm text-gray-600">15% increase from yesterday</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
              <Calendar className="w-5 h-5 text-blue-600" />
              <div>
                <p className="font-medium text-gray-900">23 photos shared</p>
                <p className="text-sm text-gray-600">New memories created</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
              <Activity className="w-5 h-5 text-purple-600" />
              <div>
                <p className="font-medium text-gray-900">94% medication compliance</p>
                <p className="text-sm text-gray-600">Above target of 90%</p>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );

  const renderResidents = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Resident Management</h2>
        <div className="flex space-x-3">
          <Button variant="secondary" icon={Filter}>Filter</Button>
          <Button variant="secondary" icon={Download}>Export</Button>
          <Button variant="primary" icon={Plus}>Add Resident</Button>
        </div>
      </div>

      <Card className="p-6">
        <div className="flex items-center space-x-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search residents..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-900">Resident</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Family Members</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Engagement</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Last Activity</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Satisfaction</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
              </tr>
            </thead>
            <tbody>
              {familyEngagement.map((resident) => (
                <tr key={resident.residentId} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-4 px-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <Users className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{resident.residentName}</p>
                        <p className="text-sm text-gray-600">Room 2{resident.residentId}4</p>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {resident.familyMembers} members
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-600 h-2 rounded-full" 
                          style={{ width: `${resident.engagementScore}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-gray-900">{resident.engagementScore}%</span>
                    </div>
                  </td>
                  <td className="py-4 px-4 text-sm text-gray-600">{resident.lastActivity}</td>
                  <td className="py-4 px-4">
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-medium text-gray-900">{resident.satisfactionRating}</span>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className="flex space-x-2">
                      <Button variant="secondary" size="sm">View</Button>
                      <Button variant="secondary" size="sm" icon={Mail}>Contact</Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );

  const renderContent = () => {
    switch (activeView) {
      case 'overview':
        return renderOverview();
      case 'residents':
        return renderResidents();
      case 'families':
        return <div className="p-8 text-center text-gray-500">Family Engagement view coming soon...</div>;
      case 'staff':
        return <div className="p-8 text-center text-gray-500">Staff Management view coming soon...</div>;
      case 'analytics':
        return <div className="p-8 text-center text-gray-500">Analytics view coming soon...</div>;
      case 'settings':
        return <div className="p-8 text-center text-gray-500">Settings view coming soon...</div>;
      default:
        return renderOverview();
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading facility dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Sunny Meadows Memory Care</h1>
              <p className="text-gray-600">Facility Administrator Dashboard</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="secondary" icon={Download}>Export Report</Button>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-600">
                    {user?.email?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <span className="text-sm font-medium text-gray-900">{user?.email}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex space-x-8">
          {/* Sidebar Navigation */}
          <div className="w-64 flex-shrink-0">
            <nav className="space-y-2">
              {navigation.map((item) => (
                <button
                  key={item.id}
                  onClick={() => setActiveView(item.id as DashboardView)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg transition-colors ${
                    activeView === item.id
                      ? 'bg-blue-100 text-blue-700 font-medium'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <item.icon className="w-5 h-5" />
                  <span>{item.label}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <motion.div
              key={activeView}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {renderContent()}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};
