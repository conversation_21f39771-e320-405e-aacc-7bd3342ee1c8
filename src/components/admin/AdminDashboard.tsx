import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Users, Building, Settings, Globe, CreditCard, Plus, Edit, Trash2, Search } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { useAuthStore, type User, type Tenant } from '../../store/authStore';

export const AdminDashboard: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { user, tenant } = useAuthStore();
  const [activeTab, setActiveTab] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data - in production this would come from API
  const [users, setUsers] = useState<User[]>([
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'patient',
      tenantId: 'tenant1',
      language: 'en',
      preferences: {
        voiceEnabled: true,
        fontSize: 'medium',
        highContrast: false,
        notifications: true,
      },
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'caregiver',
      tenantId: 'tenant1',
      language: 'fr',
      preferences: {
        voiceEnabled: true,
        fontSize: 'medium',
        highContrast: false,
        notifications: true,
      },
      patientIds: ['1'],
    },
  ]);

  const [tenants, setTenants] = useState<Tenant[]>([
    {
      id: 'tenant1',
      name: 'Smith Family',
      plan: 'premium',
      settings: {
        allowedLanguages: ['en', 'fr'],
        maxUsers: 10,
        features: ['chat', 'medications', 'insights'],
      },
      createdAt: new Date(),
    },
  ]);

  const availableLanguages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'fr', name: 'Français', flag: '🇫🇷' },
    { code: 'ar', name: 'العربية', flag: '🇸🇦' },
  ];

  const plans = [
    {
      id: 'free',
      name: 'Free',
      price: '$0',
      features: ['Basic chat', '5 photos', 'Single user'],
    },
    {
      id: 'premium',
      name: 'Premium',
      price: '$9.99',
      features: ['Unlimited chat', 'Unlimited photos', 'Multiple users', 'Medication reminders'],
    },
    {
      id: 'professional',
      name: 'Professional',
      price: '$29.99',
      features: ['All premium features', 'Caregiver dashboard', 'Analytics', 'Priority support'],
    },
  ];

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Settings },
    { id: 'users', label: t('admin.users'), icon: Users },
    { id: 'tenants', label: t('admin.tenants'), icon: Building },
    { id: 'languages', label: t('admin.languages'), icon: Globe },
    { id: 'plans', label: t('admin.plans'), icon: CreditCard },
  ];

  const renderOverview = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <Card className="text-center">
        <Users className="w-8 h-8 text-primary-600 mx-auto mb-2" />
        <h3 className="text-2xl font-bold text-gray-900">{users.length}</h3>
        <p className="text-gray-600">Total Users</p>
      </Card>
      <Card className="text-center">
        <Building className="w-8 h-8 text-primary-600 mx-auto mb-2" />
        <h3 className="text-2xl font-bold text-gray-900">{tenants.length}</h3>
        <p className="text-gray-600">Active Tenants</p>
      </Card>
      <Card className="text-center">
        <Globe className="w-8 h-8 text-primary-600 mx-auto mb-2" />
        <h3 className="text-2xl font-bold text-gray-900">{availableLanguages.length}</h3>
        <p className="text-gray-600">Languages</p>
      </Card>
      <Card className="text-center">
        <CreditCard className="w-8 h-8 text-primary-600 mx-auto mb-2" />
        <h3 className="text-2xl font-bold text-gray-900">{plans.length}</h3>
        <p className="text-gray-600">Available Plans</p>
      </Card>
    </div>
  );

  const renderUsers = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="relative">
          <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>
        <Button variant="primary" icon={Plus}>
          {t('admin.addUser')}
        </Button>
      </div>

      <div className="grid gap-4">
        {users.map((user) => (
          <Card key={user.id} className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                <Users className="w-6 h-6 text-primary-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">{user.name}</h3>
                <p className="text-gray-600">{user.email}</p>
                <div className="flex items-center gap-2 mt-1">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    user.role === 'patient' ? 'bg-blue-100 text-blue-800' :
                    user.role === 'caregiver' ? 'bg-green-100 text-green-800' :
                    'bg-purple-100 text-purple-800'
                  }`}>
                    {user.role}
                  </span>
                  <span className="text-xs text-gray-500">
                    {availableLanguages.find(lang => lang.code === user.language)?.flag} {user.language.toUpperCase()}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm" icon={Edit}>
                {t('common.edit')}
              </Button>
              <Button variant="ghost" size="sm" icon={Trash2}>
                {t('common.delete')}
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderTenants = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">{t('admin.tenants')}</h2>
        <Button variant="primary" icon={Plus}>
          {t('admin.addTenant')}
        </Button>
      </div>

      <div className="grid gap-4">
        {tenants.map((tenant) => (
          <Card key={tenant.id} className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-gray-900">{tenant.name}</h3>
                <p className="text-gray-600">ID: {tenant.id}</p>
              </div>
              <div className="flex items-center gap-2">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  tenant.plan === 'free' ? 'bg-gray-100 text-gray-800' :
                  tenant.plan === 'premium' ? 'bg-primary-100 text-primary-800' :
                  'bg-purple-100 text-purple-800'
                }`}>
                  {tenant.plan}
                </span>
                <Button variant="ghost" size="sm" icon={Edit}>
                  {t('common.edit')}
                </Button>
              </div>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Max Users:</span>
                <span className="ml-2 font-medium">{tenant.settings.maxUsers}</span>
              </div>
              <div>
                <span className="text-gray-500">Languages:</span>
                <span className="ml-2 font-medium">{tenant.settings.allowedLanguages.join(', ')}</span>
              </div>
              <div>
                <span className="text-gray-500">Features:</span>
                <span className="ml-2 font-medium">{tenant.settings.features.length}</span>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderLanguages = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">{t('admin.languages')}</h2>
        <Button variant="primary" icon={Plus}>
          Add Language
        </Button>
      </div>

      <div className="grid gap-4">
        {availableLanguages.map((language) => (
          <Card key={language.code} className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="text-2xl">{language.flag}</div>
              <div>
                <h3 className="font-semibold text-gray-900">{language.name}</h3>
                <p className="text-gray-600">Code: {language.code}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button 
                variant={i18n.language === language.code ? 'primary' : 'secondary'}
                size="sm"
                onClick={() => i18n.changeLanguage(language.code)}
              >
                {i18n.language === language.code ? 'Active' : 'Switch'}
              </Button>
              <Button variant="ghost" size="sm" icon={Edit}>
                {t('common.edit')}
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderPlans = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">{t('admin.plans')}</h2>
        <Button variant="primary" icon={Plus}>
          Add Plan
        </Button>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        {plans.map((plan) => (
          <Card key={plan.id} className="text-center space-y-4">
            <div>
              <h3 className="text-xl font-bold text-gray-900">{plan.name}</h3>
              <p className="text-3xl font-bold text-primary-600 mt-2">{plan.price}</p>
              <p className="text-gray-600">per month</p>
            </div>
            <ul className="space-y-2 text-sm text-gray-600">
              {plan.features.map((feature, index) => (
                <li key={index} className="flex items-center justify-center gap-2">
                  <div className="w-1.5 h-1.5 bg-primary-500 rounded-full" />
                  {feature}
                </li>
              ))}
            </ul>
            <Button variant="ghost" size="sm" icon={Edit} fullWidth>
              Edit Plan
            </Button>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'overview': return renderOverview();
      case 'users': return renderUsers();
      case 'tenants': return renderTenants();
      case 'languages': return renderLanguages();
      case 'plans': return renderPlans();
      default: return renderOverview();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('admin.dashboard')}</h1>
          <p className="text-gray-600">Manage users, tenants, and system settings</p>
        </motion.div>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors
                      ${activeTab === tab.id
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }
                    `}
                  >
                    <Icon className="w-5 h-5" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          {renderContent()}
        </motion.div>
      </div>
    </div>
  );
};