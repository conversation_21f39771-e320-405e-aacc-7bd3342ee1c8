import React from 'react';
import { Card } from '../ui/Card';
import { TrendingUp, Users, MessageCircle, Camera, Pill } from 'lucide-react';

export const SystemAnalytics: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">System Analytics</h2>
        <p className="text-gray-600">Platform usage statistics and insights</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Daily Active Users</p>
              <p className="text-2xl font-bold text-gray-900">892</p>
            </div>
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="w-5 h-5 text-blue-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-green-600">
            +12% from yesterday
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Photos Uploaded</p>
              <p className="text-2xl font-bold text-gray-900">234</p>
            </div>
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <Camera className="w-5 h-5 text-green-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-green-600">
            +8% from yesterday
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Conversations</p>
              <p className="text-2xl font-bold text-gray-900">1,456</p>
            </div>
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <MessageCircle className="w-5 h-5 text-purple-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-green-600">
            +15% from yesterday
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Medication Adherence</p>
              <p className="text-2xl font-bold text-gray-900">87%</p>
            </div>
            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
              <Pill className="w-5 h-5 text-orange-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-yellow-600">
            -2% from yesterday
          </div>
        </Card>
      </div>

      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Detailed Analytics</h3>
        <p className="text-gray-600">
          Comprehensive analytics dashboard with charts, graphs, and detailed metrics would be implemented here.
          This would include user engagement trends, photo interaction rates, conversation quality metrics,
          and medication adherence patterns.
        </p>
      </Card>
    </div>
  );
};
