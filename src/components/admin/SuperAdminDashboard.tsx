import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  Users,
  BarChart3,
  Shield,
  Settings,
  Database,
  AlertTriangle,
  Activity,
  MessageCircle,
  Camera,
  Pill,
  TrendingUp,
  Clock,
  Globe,
  Server,
  HardDrive,
  Wifi,
  TestTube,
} from "lucide-react";
import { Button } from "../ui/Button";
import { Card } from "../ui/Card";
import { useAuthStore } from "../../store/authStore";
import { EnhancedUserManagement } from "./EnhancedUserManagement";
import { TenantManager } from "../tenant/TenantManager";
import { EnhancedSystemAnalytics } from "./EnhancedSystemAnalytics";
import { ContentModerationTools } from "./ContentModerationTools";
import { SystemConfiguration } from "./SystemConfiguration";
import { AuditLogs } from "./AuditLogs";
import { DataManagement } from "./DataManagement";
import { SuperAdminTesting } from "./SuperAdminTesting";

type AdminView =
  | "overview"
  | "users"
  | "tenants"
  | "analytics"
  | "moderation"
  | "config"
  | "audit"
  | "data"
  | "testing";

export const SuperAdminDashboard: React.FC = () => {
  const { user, signOut } = useAuthStore();
  const [activeView, setActiveView] = useState<AdminView>("overview");

  // Mock system metrics - in production, these would come from APIs
  const systemMetrics = {
    totalUsers: 1247,
    activeUsers: 892,
    totalPhotos: 15634,
    totalConversations: 8923,
    medicationReminders: 2341,
    systemUptime: "99.9%",
    serverLoad: 23,
    storageUsed: 67,
    networkLatency: 45,
  };

  const recentAlerts = [
    {
      id: 1,
      type: "warning",
      message: "High server load detected",
      time: "2 minutes ago",
    },
    {
      id: 2,
      type: "info",
      message: "Scheduled backup completed",
      time: "1 hour ago",
    },
    {
      id: 3,
      type: "error",
      message: "Failed login attempts from IP *************",
      time: "3 hours ago",
    },
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* System Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">
                {systemMetrics.totalUsers.toLocaleString()}
              </p>
            </div>
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="w-5 h-5 text-blue-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-green-600">
            +12% from last month
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active Users</p>
              <p className="text-2xl font-bold text-gray-900">
                {systemMetrics.activeUsers.toLocaleString()}
              </p>
            </div>
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <Activity className="w-5 h-5 text-green-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-green-600">+8% from last week</div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">System Uptime</p>
              <p className="text-2xl font-bold text-gray-900">
                {systemMetrics.systemUptime}
              </p>
            </div>
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <Server className="w-5 h-5 text-green-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-green-600">
            Excellent performance
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Server Load</p>
              <p className="text-2xl font-bold text-gray-900">
                {systemMetrics.serverLoad}%
              </p>
            </div>
            <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-yellow-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-yellow-600">Moderate load</div>
        </Card>
      </div>

      {/* Platform Statistics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Platform Activity
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Camera className="w-5 h-5 text-blue-600" />
                <span className="text-gray-700">Total Photos</span>
              </div>
              <span className="font-semibold">
                {systemMetrics.totalPhotos.toLocaleString()}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <MessageCircle className="w-5 h-5 text-green-600" />
                <span className="text-gray-700">Conversations</span>
              </div>
              <span className="font-semibold">
                {systemMetrics.totalConversations.toLocaleString()}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Pill className="w-5 h-5 text-purple-600" />
                <span className="text-gray-700">Medication Reminders</span>
              </div>
              <span className="font-semibold">
                {systemMetrics.medicationReminders.toLocaleString()}
              </span>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            System Health
          </h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Storage Usage</span>
                <span>{systemMetrics.storageUsed}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{ width: `${systemMetrics.storageUsed}%` }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Network Latency</span>
                <span>{systemMetrics.networkLatency}ms</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-600 h-2 rounded-full"
                  style={{ width: `${100 - systemMetrics.networkLatency}%` }}
                ></div>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Recent Alerts */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Recent Alerts
        </h3>
        <div className="space-y-3">
          {recentAlerts.map((alert) => (
            <div
              key={alert.id}
              className={`p-3 rounded-lg border-l-4 ${
                alert.type === "error"
                  ? "bg-red-50 border-red-500"
                  : alert.type === "warning"
                  ? "bg-yellow-50 border-yellow-500"
                  : "bg-blue-50 border-blue-500"
              }`}
            >
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-gray-900">
                  {alert.message}
                </p>
                <span className="text-xs text-gray-500">{alert.time}</span>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Quick Actions */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button
            variant="secondary"
            icon={Users}
            onClick={() => setActiveView("users")}
            className="justify-start"
          >
            Manage Users
          </Button>
          <Button
            variant="secondary"
            icon={BarChart3}
            onClick={() => setActiveView("analytics")}
            className="justify-start"
          >
            View Analytics
          </Button>
          <Button
            variant="secondary"
            icon={Shield}
            onClick={() => setActiveView("moderation")}
            className="justify-start"
          >
            Content Moderation
          </Button>
          <Button
            variant="secondary"
            icon={Settings}
            onClick={() => setActiveView("config")}
            className="justify-start"
          >
            System Config
          </Button>
        </div>
      </Card>
    </div>
  );

  const renderContent = () => {
    switch (activeView) {
      case "overview":
        return renderOverview();
      case "users":
        return <EnhancedUserManagement />;
      case "tenants":
        return <TenantManager />;
      case "analytics":
        return <EnhancedSystemAnalytics />;
      case "moderation":
        return <ContentModerationTools />;
      case "config":
        return <SystemConfiguration />;
      case "audit":
        return <AuditLogs />;
      case "data":
        return <DataManagement />;
      case "testing":
        return <SuperAdminTesting />;
      default:
        return renderOverview();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-orange-600 rounded-lg flex items-center justify-center">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  Super Admin Portal
                </h1>
                <p className="text-sm text-gray-600">
                  Welcome back, {user?.name}
                </p>
              </div>
            </div>
            <Button
              variant="secondary"
              onClick={async () => {
                try {
                  await signOut();
                  // Force page reload to ensure clean state
                  window.location.href = "/";
                } catch (error) {
                  console.error("Logout failed:", error);
                }
              }}
            >
              Logout
            </Button>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex space-x-8">
            {[
              { id: "overview", label: "Overview", icon: BarChart3 },
              { id: "users", label: "Users", icon: Users },
              { id: "tenants", label: "Tenants", icon: Globe },
              { id: "analytics", label: "Analytics", icon: TrendingUp },
              { id: "moderation", label: "Moderation", icon: Shield },
              { id: "config", label: "Config", icon: Settings },
              { id: "audit", label: "Audit", icon: Clock },
              { id: "data", label: "Data", icon: Database },
              { id: "testing", label: "Testing", icon: TestTube },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveView(tab.id as AdminView)}
                  className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                    activeView === tab.id
                      ? "border-red-500 text-red-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <motion.div
          key={activeView}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          {renderContent()}
        </motion.div>
      </div>
    </div>
  );
};
