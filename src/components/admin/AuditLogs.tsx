import React from 'react';
import { Card } from '../ui/Card';
import { Clock, Search, Filter, Download } from 'lucide-react';
import { Button } from '../ui/Button';

export const AuditLogs: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Audit Logs</h2>
          <p className="text-gray-600">Track all system activities and user actions</p>
        </div>
        <Button variant="outline" icon={Download}>
          Export Logs
        </Button>
      </div>

      <Card className="p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search logs..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
            />
          </div>
          
          <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500">
            <option value="all">All Actions</option>
            <option value="login">Login Events</option>
            <option value="user_management">User Management</option>
            <option value="content_moderation">Content Moderation</option>
            <option value="system_config">System Configuration</option>
          </select>
          
          <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500">
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
        </div>
      </Card>

      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-4">
          {[
            { time: '2 minutes ago', user: '<EMAIL>', action: 'Viewed user details for John Doe', type: 'info' },
            { time: '15 minutes ago', user: '<EMAIL>', action: 'Updated system configuration', type: 'warning' },
            { time: '1 hour ago', user: 'system', action: 'Automated backup completed', type: 'success' },
            { time: '2 hours ago', user: '<EMAIL>', action: 'Suspended user account', type: 'error' },
          ].map((log, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <Clock className="w-4 h-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{log.action}</p>
                  <p className="text-xs text-gray-500">by {log.user}</p>
                </div>
              </div>
              <span className="text-xs text-gray-500">{log.time}</span>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};
