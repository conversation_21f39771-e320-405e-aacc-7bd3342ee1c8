import React from 'react';
import { Card } from '../ui/Card';
import { Settings, Globe, Bell, Shield } from 'lucide-react';

export const SystemConfiguration: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">System Configuration</h2>
        <p className="text-gray-600">Manage global system settings and parameters</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Globe className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Global Settings</h3>
          </div>
          <p className="text-gray-600">
            Platform-wide configuration options including default user settings,
            system limits, and global preferences.
          </p>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Bell className="w-5 h-5 text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900">Notification Templates</h3>
          </div>
          <p className="text-gray-600">
            Manage email templates, push notification settings, and
            communication preferences for all user types.
          </p>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Shield className="w-5 h-5 text-red-600" />
            <h3 className="text-lg font-semibold text-gray-900">Security Settings</h3>
          </div>
          <p className="text-gray-600">
            Configure security policies, access controls, rate limiting,
            and authentication requirements.
          </p>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Settings className="w-5 h-5 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900">AI Configuration</h3>
          </div>
          <p className="text-gray-600">
            Adjust AI behavior parameters, conversation models,
            and automated response settings.
          </p>
        </Card>
      </div>
    </div>
  );
};
