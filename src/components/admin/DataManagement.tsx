import React from 'react';
import { Card } from '../ui/Card';
import { Database, Download, Upload, Trash2, Shield, Archive } from 'lucide-react';
import { Button } from '../ui/Button';

export const DataManagement: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Data Management</h2>
        <p className="text-gray-600">Backup, export, and manage platform data</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Database className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Database Backup</h3>
          </div>
          <p className="text-gray-600 mb-4">
            Create and manage database backups for disaster recovery.
          </p>
          <Button variant="primary" icon={Archive} fullWidth>
            Create Backup
          </Button>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Download className="w-5 h-5 text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900">Data Export</h3>
          </div>
          <p className="text-gray-600 mb-4">
            Export user data for compliance and analysis purposes.
          </p>
          <Button variant="outline" icon={Download} fullWidth>
            Export Data
          </Button>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Shield className="w-5 h-5 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900">GDPR Compliance</h3>
          </div>
          <p className="text-gray-600 mb-4">
            Manage data deletion requests and privacy compliance.
          </p>
          <Button variant="outline" icon={Shield} fullWidth>
            Privacy Tools
          </Button>
        </Card>
      </div>

      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Storage Statistics</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">2.4 TB</div>
            <div className="text-sm text-gray-600">Total Storage Used</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">15,634</div>
            <div className="text-sm text-gray-600">Photos Stored</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">1,247</div>
            <div className="text-sm text-gray-600">User Accounts</div>
          </div>
        </div>
      </Card>

      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Backups</h3>
        <div className="space-y-3">
          {[
            { date: '2024-01-15 02:00', size: '2.4 GB', status: 'Completed', type: 'Automated' },
            { date: '2024-01-14 02:00', size: '2.3 GB', status: 'Completed', type: 'Automated' },
            { date: '2024-01-13 14:30', size: '2.3 GB', status: 'Completed', type: 'Manual' },
          ].map((backup, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-900">{backup.date}</p>
                <p className="text-xs text-gray-500">{backup.type} backup - {backup.size}</p>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs text-green-600">{backup.status}</span>
                <Button variant="ghost" size="sm" icon={Download} />
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};
