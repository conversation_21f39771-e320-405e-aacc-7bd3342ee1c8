import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Shield, Lock, Eye, EyeOff, AlertTriangle } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { useAuthStore } from '../../store/authStore';
import toast from 'react-hot-toast';

interface SuperAdminAuthProps {
  onAuthenticated: () => void;
}

export const SuperAdminAuth: React.FC<SuperAdminAuthProps> = ({
  onAuthenticated,
}) => {
  const [credentials, setCredentials] = useState({
    email: '',
    password: '',
    adminKey: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showAdminKey, setShowAdminKey] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuthStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Validate admin credentials
      // In production, this would be a secure API call
      const validAdminEmails = ['<EMAIL>', '<EMAIL>'];
      const validAdminKey = 'MEMOCARE_SUPER_ADMIN_2024'; // In production, this would be more secure
      
      if (!validAdminEmails.includes(credentials.email)) {
        throw new Error('Invalid admin email address');
      }
      
      if (credentials.adminKey !== validAdminKey) {
        throw new Error('Invalid admin key');
      }
      
      if (credentials.password !== 'SuperAdmin2024!') { // In production, proper password validation
        throw new Error('Invalid password');
      }

      // Create super admin user
      const superAdminUser = {
        id: 'super-admin-1',
        email: credentials.email,
        name: 'Super Administrator',
        role: 'super_admin' as const,
        preferences: {
          notifications: true,
          theme: 'light' as const,
          language: 'en' as const
        },
        createdAt: new Date(),
        lastLoginAt: new Date()
      };

      // Login as super admin
      login(superAdminUser);
      
      toast.success('Super Admin access granted');
      onAuthenticated();
      
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Authentication failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="w-full max-w-md"
      >
        <Card className="p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Super Admin Portal
            </h1>
            <p className="text-gray-600">
              Secure access to MemoCare administration
            </p>
          </div>

          {/* Security Warning */}
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
              <div className="text-sm text-red-800">
                <p className="font-medium mb-1">Restricted Access</p>
                <p>This portal is for authorized administrators only. All access attempts are logged and monitored.</p>
              </div>
            </div>
          </div>

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Admin Email
              </label>
              <input
                type="email"
                value={credentials.email}
                onChange={(e) => setCredentials(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={credentials.password}
                  onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                  placeholder="Enter admin password"
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Admin Key
              </label>
              <div className="relative">
                <input
                  type={showAdminKey ? 'text' : 'password'}
                  value={credentials.adminKey}
                  onChange={(e) => setCredentials(prev => ({ ...prev, adminKey: e.target.value }))}
                  placeholder="Enter super admin key"
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowAdminKey(!showAdminKey)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showAdminKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Contact system administrator for admin key
              </p>
            </div>

            <Button
              type="submit"
              variant="primary"
              icon={Lock}
              fullWidth
              disabled={isLoading || !credentials.email || !credentials.password || !credentials.adminKey}
              className="bg-gradient-to-r from-red-500 to-orange-600 hover:from-red-600 hover:to-orange-700"
            >
              {isLoading ? 'Authenticating...' : 'Access Admin Portal'}
            </Button>
          </form>

          {/* Demo Credentials */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <p className="text-xs font-medium text-gray-700 mb-2">Demo Credentials:</p>
            <div className="text-xs text-gray-600 space-y-1">
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Password:</strong> SuperAdmin2024!</p>
              <p><strong>Admin Key:</strong> MEMOCARE_SUPER_ADMIN_2024</p>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              MemoCare Super Admin Portal v1.0
            </p>
            <p className="text-xs text-gray-500">
              All activities are monitored and logged
            </p>
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

// Protected Route Component for Super Admin
interface SuperAdminRouteProps {
  children: React.ReactNode;
}

export const SuperAdminRoute: React.FC<SuperAdminRouteProps> = ({ children }) => {
  const { user } = useAuthStore();
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check if user is super admin
  const isSuperAdmin = user?.role === 'super_admin';

  if (!isSuperAdmin && !isAuthenticated) {
    return <SuperAdminAuth onAuthenticated={() => setIsAuthenticated(true)} />;
  }

  return <>{children}</>;
};

// Hook for super admin access control
export const useSuperAdminAccess = () => {
  const { user } = useAuthStore();
  
  return {
    isSuperAdmin: user?.role === 'super_admin',
    hasAccess: (permission: string) => {
      // In production, implement granular permissions
      return user?.role === 'super_admin';
    },
    requireSuperAdmin: () => {
      if (user?.role !== 'super_admin') {
        throw new Error('Super admin access required');
      }
    }
  };
};
