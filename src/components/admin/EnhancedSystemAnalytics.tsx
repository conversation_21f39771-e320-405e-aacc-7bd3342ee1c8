import React, { useState, useEffect } from "react";
import { Card } from "../ui/Card";
import {
  TrendingUp,
  Users,
  MessageCircle,
  Camera,
  Pill,
  Calendar,
  Activity,
  Target,
  AlertTriangle,
  Bar<PERSON>hart3,
  <PERSON>,
  Heart,
} from "lucide-react";
import { supabase } from "../../lib/supabase";
import { format, subDays, startOfDay } from "date-fns";

interface AnalyticsData {
  totalUsers: number;
  activeUsers: number;
  totalPhotos: number;
  totalConversations: number;
  averageAdherence: number;
  dailyStats: Array<{
    date: string;
    users: number;
    photos: number;
    conversations: number;
    adherence: number;
  }>;
  userGrowth: number;
  photoGrowth: number;
  conversationGrowth: number;
}

export const EnhancedSystemAnalytics: React.FC = () => {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [timeRange, setTimeRange] = useState<"7d" | "30d" | "90d">("30d");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadAnalytics();
  }, [timeRange]);

  const loadAnalytics = async () => {
    try {
      setIsLoading(true);

      // Calculate date range
      const days = timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90;
      const startDate = startOfDay(subDays(new Date(), days));

      // Load users data
      const { data: usersData, error: usersError } = await supabase
        .from("users")
        .select("id, created_at, last_login_at")
        .gte("created_at", startDate.toISOString());

      if (usersError) throw usersError;

      // Load photos data
      const { data: photosData, error: photosError } = await supabase
        .from("photos")
        .select("id, uploaded_at")
        .gte("uploaded_at", startDate.toISOString());

      if (photosError) throw photosError;

      // Load medication reminders for adherence
      const { data: remindersData, error: remindersError } = await supabase
        .from("medication_reminders")
        .select("id, status, scheduled_time")
        .gte("scheduled_time", startDate.toISOString());

      if (remindersError) throw remindersError;

      // Calculate metrics
      const totalUsers = usersData?.length || 0;
      const activeUsers =
        usersData?.filter(
          (u) =>
            u.last_login_at &&
            new Date(u.last_login_at) > subDays(new Date(), 7)
        ).length || 0;

      const totalPhotos = photosData?.length || 0;
      const totalConversations = Math.floor(totalPhotos * 0.6); // Mock conversation data

      const takenReminders =
        remindersData?.filter((r) => r.status === "taken").length || 0;
      const totalReminders = remindersData?.length || 0;
      const averageAdherence =
        totalReminders > 0
          ? Math.round((takenReminders / totalReminders) * 100)
          : 0;

      // Calculate daily stats for the chart
      const dailyStats = [];
      for (let i = days - 1; i >= 0; i--) {
        const date = subDays(new Date(), i);
        const dateStr = format(date, "yyyy-MM-dd");

        const dayUsers =
          usersData?.filter(
            (u) => format(new Date(u.created_at), "yyyy-MM-dd") === dateStr
          ).length || 0;

        const dayPhotos =
          photosData?.filter(
            (p) => format(new Date(p.uploaded_at), "yyyy-MM-dd") === dateStr
          ).length || 0;

        const dayReminders =
          remindersData?.filter(
            (r) => format(new Date(r.scheduled_time), "yyyy-MM-dd") === dateStr
          ) || [];

        const dayTaken = dayReminders.filter(
          (r) => r.status === "taken"
        ).length;
        const dayTotal = dayReminders.length;
        const dayAdherence =
          dayTotal > 0 ? Math.round((dayTaken / dayTotal) * 100) : 0;

        dailyStats.push({
          date: dateStr,
          users: dayUsers,
          photos: dayPhotos,
          conversations: Math.floor(dayPhotos * 0.6),
          adherence: dayAdherence,
        });
      }

      // Calculate growth rates (mock data for now)
      const userGrowth = Math.floor(Math.random() * 20) - 5; // -5% to +15%
      const photoGrowth = Math.floor(Math.random() * 25) - 5; // -5% to +20%
      const conversationGrowth = Math.floor(Math.random() * 30) - 5; // -5% to +25%

      setAnalytics({
        totalUsers,
        activeUsers,
        totalPhotos,
        totalConversations,
        averageAdherence,
        dailyStats,
        userGrowth,
        photoGrowth,
        conversationGrowth,
      });
    } catch (error) {
      console.error("Error loading analytics:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return "text-green-600";
    if (growth < 0) return "text-red-600";
    return "text-gray-600";
  };

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return "↗";
    if (growth < 0) return "↘";
    return "→";
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">System Analytics</h2>
          <p className="text-gray-600">
            Platform usage statistics and insights
          </p>
        </div>
        <select
          value={timeRange}
          onChange={(e) => setTimeRange(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
        >
          <option value="7d">Last 7 days</option>
          <option value="30d">Last 30 days</option>
          <option value="90d">Last 90 days</option>
        </select>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">
                {analytics?.totalUsers || 0}
              </p>
            </div>
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="w-5 h-5 text-blue-600" />
            </div>
          </div>
          <div
            className={`mt-2 text-xs ${getGrowthColor(
              analytics?.userGrowth || 0
            )}`}
          >
            {getGrowthIcon(analytics?.userGrowth || 0)}{" "}
            {Math.abs(analytics?.userGrowth || 0)}% from last period
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active Users</p>
              <p className="text-2xl font-bold text-gray-900">
                {analytics?.activeUsers || 0}
              </p>
            </div>
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <Activity className="w-5 h-5 text-green-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">Last 7 days activity</div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Photos Uploaded</p>
              <p className="text-2xl font-bold text-gray-900">
                {analytics?.totalPhotos || 0}
              </p>
            </div>
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <Camera className="w-5 h-5 text-purple-600" />
            </div>
          </div>
          <div
            className={`mt-2 text-xs ${getGrowthColor(
              analytics?.photoGrowth || 0
            )}`}
          >
            {getGrowthIcon(analytics?.photoGrowth || 0)}{" "}
            {Math.abs(analytics?.photoGrowth || 0)}% from last period
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg Adherence</p>
              <p className="text-2xl font-bold text-gray-900">
                {analytics?.averageAdherence || 0}%
              </p>
            </div>
            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
              <Target className="w-5 h-5 text-orange-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            Medication adherence rate
          </div>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Daily Activity Chart */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Daily Activity
          </h3>
          <div className="h-64 flex items-end justify-between gap-1">
            {analytics?.dailyStats.map((day, index) => {
              const maxValue = Math.max(
                ...analytics.dailyStats.map((d) => d.photos + d.conversations)
              );
              const height =
                maxValue > 0
                  ? ((day.photos + day.conversations) / maxValue) * 100
                  : 0;

              return (
                <div
                  key={day.date}
                  className="flex flex-col items-center flex-1"
                >
                  <div className="w-full flex flex-col items-center">
                    <div
                      className="w-full bg-blue-500 rounded-t"
                      style={{
                        height: `${(day.photos / maxValue) * 100}%`,
                        minHeight: day.photos > 0 ? "4px" : "0",
                      }}
                      title={`${day.photos} photos`}
                    ></div>
                    <div
                      className="w-full bg-green-500"
                      style={{
                        height: `${(day.conversations / maxValue) * 100}%`,
                        minHeight: day.conversations > 0 ? "4px" : "0",
                      }}
                      title={`${day.conversations} conversations`}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-600 mt-2 transform -rotate-45 origin-center">
                    {format(new Date(day.date), "MM/dd")}
                  </div>
                </div>
              );
            })}
          </div>
          <div className="flex items-center justify-center gap-4 mt-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span className="text-xs text-gray-600">Photos</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span className="text-xs text-gray-600">Conversations</span>
            </div>
          </div>
        </Card>

        {/* Adherence Trend */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Medication Adherence Trend
          </h3>
          <div className="h-64 flex items-end justify-between gap-1">
            {analytics?.dailyStats.map((day, index) => {
              const height = Math.max(day.adherence, 5); // Minimum height for visibility

              return (
                <div
                  key={day.date}
                  className="flex flex-col items-center flex-1"
                >
                  <div
                    className={`w-full rounded-t ${
                      day.adherence >= 80
                        ? "bg-green-500"
                        : day.adherence >= 60
                        ? "bg-yellow-500"
                        : "bg-red-500"
                    }`}
                    style={{ height: `${height}%` }}
                    title={`${day.adherence}% adherence`}
                  ></div>
                  <div className="text-xs text-gray-600 mt-2 transform -rotate-45 origin-center">
                    {format(new Date(day.date), "MM/dd")}
                  </div>
                </div>
              );
            })}
          </div>
          <div className="flex items-center justify-center gap-4 mt-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span className="text-xs text-gray-600">Good (80%+)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-yellow-500 rounded"></div>
              <span className="text-xs text-gray-600">Fair (60-79%)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded"></div>
              <span className="text-xs text-gray-600">Poor (&lt;60%)</span>
            </div>
          </div>
        </Card>
      </div>

      {/* Insights */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Platform Insights
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <Users className="w-5 h-5 text-blue-600" />
              <h4 className="font-medium text-blue-800">User Engagement</h4>
            </div>
            <p className="text-sm text-blue-700">
              {analytics?.activeUsers && analytics?.totalUsers
                ? Math.round(
                    (analytics.activeUsers / analytics.totalUsers) * 100
                  )
                : 0}
              % of users are active in the last 7 days. This indicates good
              platform engagement.
            </p>
          </div>

          <div className="p-4 bg-green-50 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <Camera className="w-5 h-5 text-green-600" />
              <h4 className="font-medium text-green-800">Content Creation</h4>
            </div>
            <p className="text-sm text-green-700">
              Users are actively uploading photos, with an average of{" "}
              {analytics?.totalUsers && analytics?.totalPhotos
                ? Math.round(analytics.totalPhotos / analytics.totalUsers)
                : 0}{" "}
              photos per user.
            </p>
          </div>

          <div className="p-4 bg-orange-50 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <Target className="w-5 h-5 text-orange-600" />
              <h4 className="font-medium text-orange-800">Health Outcomes</h4>
            </div>
            <p className="text-sm text-orange-700">
              Average medication adherence of {analytics?.averageAdherence || 0}
              %{" "}
              {(analytics?.averageAdherence || 0) >= 80
                ? "exceeds"
                : "is below"}{" "}
              the 80% target for optimal health outcomes.
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};
