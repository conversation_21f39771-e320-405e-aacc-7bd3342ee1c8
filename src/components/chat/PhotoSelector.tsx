import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Search, 
  Calendar, 
  MapPin, 
  Users, 
  Heart,
  Check,
  Image as ImageIcon
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import type { Photo } from '../../types';

interface PhotoSelectorProps {
  photos: Photo[];
  onSelect: (photos: Photo[]) => void;
  onClose: () => void;
  maxSelection?: number;
  preselected?: Photo[];
}

export const PhotoSelector: React.FC<PhotoSelectorProps> = ({
  photos,
  onSelect,
  onClose,
  maxSelection = 5,
  preselected = []
}) => {
  const [selectedPhotos, setSelectedPhotos] = useState<Photo[]>(preselected);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'recent' | 'favorites' | 'people'>('all');

  const filteredPhotos = useMemo(() => {
    let filtered = [...photos];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(photo => 
        photo.filename.toLowerCase().includes(searchQuery.toLowerCase()) ||
        photo.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        photo.metadata?.tags?.some(tag => 
          tag.toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
    }

    // Apply type filter
    switch (filterType) {
      case 'recent':
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        filtered = filtered.filter(photo => photo.uploadedAt > weekAgo);
        break;
      case 'favorites':
        filtered = filtered.filter(photo => photo.metadata?.isFavorite);
        break;
      case 'people':
        filtered = filtered.filter(photo => 
          photo.metadata?.people && photo.metadata.people.length > 0
        );
        break;
    }

    // Sort by upload date (newest first)
    return filtered.sort((a, b) => b.uploadedAt.getTime() - a.uploadedAt.getTime());
  }, [photos, searchQuery, filterType]);

  const handlePhotoToggle = (photo: Photo) => {
    setSelectedPhotos(prev => {
      const isSelected = prev.some(p => p.id === photo.id);
      
      if (isSelected) {
        return prev.filter(p => p.id !== photo.id);
      } else if (prev.length < maxSelection) {
        return [...prev, photo];
      } else {
        // Replace the first selected photo if at max
        return [photo, ...prev.slice(1)];
      }
    });
  };

  const handleConfirmSelection = () => {
    onSelect(selectedPhotos);
  };

  const isPhotoSelected = (photo: Photo) => {
    return selectedPhotos.some(p => p.id === photo.id);
  };

  const getPhotoDisplayInfo = (photo: Photo) => {
    const info = [];
    
    if (photo.metadata?.location?.city) {
      info.push(photo.metadata.location.city);
    }
    
    if (photo.metadata?.people && photo.metadata.people.length > 0) {
      info.push(`${photo.metadata.people.length} person${photo.metadata.people.length !== 1 ? 's' : ''}`);
    }
    
    return info.join(' • ');
  };

  const filterOptions = [
    { id: 'all', label: 'All Photos', icon: ImageIcon },
    { id: 'recent', label: 'Recent', icon: Calendar },
    { id: 'favorites', label: 'Favorites', icon: Heart },
    { id: 'people', label: 'People', icon: Users }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Select Photos</h2>
            <Button variant="ghost" icon={X} onClick={onClose} />
          </div>

          {/* Search */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search photos by name, description, or tags..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
            />
          </div>

          {/* Filters */}
          <div className="flex gap-2 overflow-x-auto">
            {filterOptions.map((option) => {
              const Icon = option.icon;
              const isActive = filterType === option.id;
              
              return (
                <button
                  key={option.id}
                  onClick={() => setFilterType(option.id as any)}
                  className={`
                    flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-all
                    ${isActive
                      ? 'bg-primary-100 text-primary-700'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }
                  `}
                >
                  <Icon className="w-4 h-4" />
                  {option.label}
                </button>
              );
            })}
          </div>
        </div>

        {/* Photo Grid */}
        <div className="p-6 overflow-y-auto max-h-96">
          {filteredPhotos.length === 0 ? (
            <div className="text-center py-12">
              <ImageIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Photos Found</h3>
              <p className="text-gray-600">
                {searchQuery ? 'Try adjusting your search terms' : 'No photos match the selected filter'}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              <AnimatePresence>
                {filteredPhotos.map((photo, index) => {
                  const isSelected = isPhotoSelected(photo);
                  const selectionIndex = selectedPhotos.findIndex(p => p.id === photo.id);
                  
                  return (
                    <motion.div
                      key={photo.id}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      transition={{ delay: index * 0.02 }}
                      className={`
                        relative aspect-square rounded-lg overflow-hidden cursor-pointer border-2 transition-all
                        ${isSelected
                          ? 'border-primary-500 ring-2 ring-primary-200'
                          : 'border-gray-200 hover:border-gray-300'
                        }
                      `}
                      onClick={() => handlePhotoToggle(photo)}
                    >
                      <img
                        src={photo.url}
                        alt={photo.filename}
                        className="w-full h-full object-cover"
                      />
                      
                      {/* Selection Indicator */}
                      {isSelected && (
                        <div className="absolute top-2 right-2 w-6 h-6 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                          {selectionIndex + 1}
                        </div>
                      )}
                      
                      {/* Photo Info Overlay */}
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2">
                        <p className="text-white text-xs font-medium truncate">
                          {photo.filename}
                        </p>
                        {getPhotoDisplayInfo(photo) && (
                          <p className="text-white/80 text-xs truncate">
                            {getPhotoDisplayInfo(photo)}
                          </p>
                        )}
                      </div>
                      
                      {/* Hover Overlay */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-all" />
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {selectedPhotos.length} of {maxSelection} photo{maxSelection !== 1 ? 's' : ''} selected
            </div>
            
            <div className="flex gap-3">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleConfirmSelection}
                disabled={selectedPhotos.length === 0}
              >
                Add {selectedPhotos.length} Photo{selectedPhotos.length !== 1 ? 's' : ''}
              </Button>
            </div>
          </div>
          
          {selectedPhotos.length > 0 && (
            <div className="mt-4 flex gap-2 overflow-x-auto">
              {selectedPhotos.map((photo, index) => (
                <div key={photo.id} className="relative flex-shrink-0">
                  <img
                    src={photo.url}
                    alt={photo.filename}
                    className="w-12 h-12 object-cover rounded-lg border-2 border-primary-500"
                  />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-primary-500 text-white rounded-full flex items-center justify-center text-xs">
                    {index + 1}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
};
