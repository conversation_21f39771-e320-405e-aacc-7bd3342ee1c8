import React, { useState, useRef, useEffect } from 'react';
import { Send, Image as ImageIcon, Volume2, VolumeX } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { VoiceButton } from '../ui/VoiceButton';
import { useSpeechRecognition } from '../../hooks/useSpeechRecognition';
import { useTextToSpeech } from '../../hooks/useTextToSpeech';
import type { Photo, Message } from '../../types';

interface ConversationInterfaceProps {
  selectedPhoto?: Photo;
  photos: Photo[];
  onPhotoSelect: (photo: Photo) => void;
}

export const ConversationInterface: React.FC<ConversationInterfaceProps> = ({
  selectedPhoto,
  photos,
  onPhotoSelect,
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const {
    isListening,
    transcript,
    startListening,
    stopListening,
    resetTranscript,
  } = useSpeechRecognition();
  
  const {
    isSpeaking,
    speak,
    stop: stopSpeaking,
  } = useTextToSpeech();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (transcript) {
      setInputValue(transcript);
    }
  }, [transcript]);

  const generateAIResponse = (userMessage: string, photo?: Photo): string => {
    // Simple response generation for MVP - in production this would call an AI service
    const responses = {
      greeting: [
        "It's wonderful to see you! Tell me about this beautiful photo.",
        "Hello there! This photo brings back such lovely memories, doesn't it?",
        "What a delightful photo! I'd love to hear the story behind it.",
      ],
      photo: [
        "This is such a beautiful memory. Can you tell me more about when this was taken?",
        "I can see the joy in this photo. What was happening that day?",
        "This looks like a special moment. Who else was there with you?",
        "What a wonderful photo! It must have been a lovely day.",
      ],
      general: [
        "That sounds wonderful. Tell me more about that.",
        "How lovely! What else do you remember about that time?",
        "That brings such warmth to my heart. Can you share more?",
        "What a beautiful memory to treasure.",
      ],
    };

    if (userMessage.toLowerCase().includes('hello') || userMessage.toLowerCase().includes('hi')) {
      return responses.greeting[Math.floor(Math.random() * responses.greeting.length)];
    }
    
    if (photo) {
      return responses.photo[Math.floor(Math.random() * responses.photo.length)];
    }
    
    return responses.general[Math.floor(Math.random() * responses.general.length)];
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: `msg-${Date.now()}`,
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    resetTranscript();
    setIsTyping(true);

    // Simulate AI response delay
    setTimeout(() => {
      const aiResponse: Message = {
        id: `msg-${Date.now() + 1}`,
        type: 'assistant',
        content: generateAIResponse(inputValue, selectedPhoto),
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);

      // Auto-speak the response
      speak(aiResponse.content);
    }, 1500);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleVoiceStart = () => {
    if (isSpeaking) {
      stopSpeaking();
    }
    startListening();
  };

  return (
    <div className="flex flex-col h-full max-h-screen">
      {/* Photo Selection */}
      {photos.length > 0 && (
        <div className="p-4 border-b border-gray-100">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Choose a photo to talk about:</h3>
          <div className="flex gap-3 overflow-x-auto pb-2">
            {photos.map((photo) => (
              <button
                key={photo.id}
                onClick={() => onPhotoSelect(photo)}
                className={`
                  flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200
                  ${selectedPhoto?.id === photo.id 
                    ? 'border-primary-500 ring-2 ring-primary-200' 
                    : 'border-gray-200 hover:border-primary-300'
                  }
                `}
              >
                <img
                  src={photo.url}
                  alt={photo.filename}
                  className="w-full h-full object-cover"
                />
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Selected Photo Display */}
      {selectedPhoto && (
        <Card className="m-4 mb-0" padding="sm">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
              <img
                src={selectedPhoto.url}
                alt={selectedPhoto.filename}
                className="w-full h-full object-cover"
              />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">Talking about this photo</h3>
              <p className="text-sm text-gray-500">{selectedPhoto.filename}</p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              icon={isSpeaking ? VolumeX : Volume2}
              onClick={() => isSpeaking ? stopSpeaking() : speak("Let's talk about this beautiful photo!")}
            />
          </div>
        </Card>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <ImageIcon className="w-8 h-8 text-primary-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Let's Talk About Your Memories</h3>
            <p className="text-gray-600 max-w-md mx-auto">
              {selectedPhoto 
                ? "I'm ready to chat about this wonderful photo. What would you like to share?"
                : "Select a photo above and let's have a conversation about your precious memories."
              }
            </p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`
                  max-w-xs sm:max-w-md px-4 py-3 rounded-2xl
                  ${message.type === 'user'
                    ? 'bg-primary-500 text-white'
                    : 'bg-gray-100 text-gray-900'
                  }
                `}
              >
                <p className="text-sm leading-5">{message.content}</p>
                <p className={`text-xs mt-1 ${message.type === 'user' ? 'text-primary-100' : 'text-gray-500'}`}>
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </p>
              </div>
            </div>
          ))
        )}

        {isTyping && (
          <div className="flex justify-start">
            <div className="bg-gray-100 px-4 py-3 rounded-2xl">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="p-4 border-t border-gray-100 bg-white">
        <div className="flex items-end gap-3 max-w-4xl mx-auto">
          <div className="flex-1">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={isListening ? "Listening..." : "Type your message or tap to speak..."}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              rows={1}
              disabled={isListening}
            />
          </div>
          
          <VoiceButton
            isListening={isListening}
            isSpeaking={isSpeaking}
            onStartListening={handleVoiceStart}
            onStopListening={stopListening}
            onStopSpeaking={stopSpeaking}
            size="md"
          />
          
          <Button
            variant="primary"
            size="md"
            icon={Send}
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isTyping}
          />
        </div>
      </div>
    </div>
  );
};