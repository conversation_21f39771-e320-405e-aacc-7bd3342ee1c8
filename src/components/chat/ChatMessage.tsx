import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Heart, 
  User, 
  Volume2, 
  Copy, 
  MoreHorizontal,
  Clock,
  Image as ImageIcon
} from 'lucide-react';
import { Button } from '../ui/Button';
import type { Photo } from '../../types';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  photos?: Photo[];
  audioUrl?: string;
  metadata?: {
    emotion?: string;
    confidence?: number;
    context?: string[];
  };
}

interface ChatMessageProps {
  message: Message;
  onPhotoClick?: (photo: Photo) => void;
  onReply?: (message: Message) => void;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  onPhotoClick,
  onReply
}) => {
  const [showActions, setShowActions] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  const isAI = message.sender === 'ai';
  const isUser = message.sender === 'user';

  const handleCopyMessage = () => {
    navigator.clipboard.writeText(message.content);
    // Could show a toast here
  };

  const handlePlayAudio = () => {
    if (message.audioUrl) {
      // Play audio message
      const audio = new Audio(message.audioUrl);
      setIsPlaying(true);
      audio.play();
      audio.onended = () => setIsPlaying(false);
    } else if (isAI && 'speechSynthesis' in window) {
      // Text-to-speech for AI messages
      const utterance = new SpeechSynthesisUtterance(message.content);
      utterance.rate = 0.8;
      utterance.pitch = 1.0;
      utterance.volume = 0.8;
      
      setIsPlaying(true);
      speechSynthesis.speak(utterance);
      utterance.onend = () => setIsPlaying(false);
    }
  };

  const getEmotionColor = (emotion?: string) => {
    switch (emotion) {
      case 'happy': return 'text-yellow-600';
      case 'caring': return 'text-blue-600';
      case 'excited': return 'text-orange-600';
      case 'gentle': return 'text-green-600';
      default: return 'text-primary-600';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`flex gap-3 ${isUser ? 'flex-row-reverse' : 'flex-row'}`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Avatar */}
      <div className={`
        w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0
        ${isAI ? 'bg-primary-100' : 'bg-gray-100'}
      `}>
        {isAI ? (
          <Heart className={`w-4 h-4 ${getEmotionColor(message.metadata?.emotion)}`} />
        ) : (
          <User className="w-4 h-4 text-gray-600" />
        )}
      </div>

      {/* Message Content */}
      <div className={`flex-1 max-w-xs sm:max-w-md ${isUser ? 'text-right' : 'text-left'}`}>
        {/* Message Bubble */}
        <div className={`
          inline-block px-4 py-3 rounded-2xl relative
          ${isUser 
            ? 'bg-primary-500 text-white rounded-br-md' 
            : 'bg-white text-gray-900 border border-gray-200 rounded-bl-md shadow-sm'
          }
        `}>
          {/* Message Text */}
          {message.content && (
            <p className="text-sm leading-relaxed whitespace-pre-wrap">
              {message.content}
            </p>
          )}

          {/* Photos */}
          {message.photos && message.photos.length > 0 && (
            <div className={`${message.content ? 'mt-3' : ''}`}>
              <div className={`
                grid gap-2
                ${message.photos.length === 1 ? 'grid-cols-1' : 
                  message.photos.length === 2 ? 'grid-cols-2' : 
                  'grid-cols-2'}
              `}>
                {message.photos.map((photo, index) => (
                  <div
                    key={photo.id}
                    className="relative group cursor-pointer"
                    onClick={() => onPhotoClick?.(photo)}
                  >
                    <img
                      src={photo.url}
                      alt={photo.filename}
                      className={`
                        w-full object-cover rounded-lg transition-transform group-hover:scale-105
                        ${message.photos!.length === 1 ? 'h-48' : 'h-24'}
                      `}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-lg transition-all" />
                    
                    {/* Photo overlay info */}
                    <div className="absolute bottom-1 left-1 right-1">
                      <div className="bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded backdrop-blur-sm">
                        {photo.filename}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Audio Message */}
          {message.audioUrl && (
            <div className={`${message.content ? 'mt-3' : ''} flex items-center gap-2`}>
              <Button
                variant="ghost"
                size="sm"
                icon={Volume2}
                onClick={handlePlayAudio}
                className={`${isUser ? 'text-white hover:bg-white/20' : 'text-gray-600'}`}
                disabled={isPlaying}
              />
              <div className={`flex-1 h-1 rounded-full ${isUser ? 'bg-white/30' : 'bg-gray-200'}`}>
                <div className={`h-full rounded-full ${isUser ? 'bg-white' : 'bg-primary-500'} w-1/3`} />
              </div>
              <span className={`text-xs ${isUser ? 'text-white/80' : 'text-gray-500'}`}>
                0:15
              </span>
            </div>
          )}

          {/* Confidence indicator for AI messages */}
          {isAI && message.metadata?.confidence && message.metadata.confidence < 0.8 && (
            <div className="mt-2 text-xs text-gray-500 italic">
              I'm not entirely sure about this response.
            </div>
          )}
        </div>

        {/* Message Actions */}
        <div className={`
          flex items-center gap-1 mt-1 transition-opacity
          ${showActions ? 'opacity-100' : 'opacity-0'}
          ${isUser ? 'justify-end' : 'justify-start'}
        `}>
          <span className="text-xs text-gray-500 flex items-center gap-1">
            <Clock className="w-3 h-3" />
            {formatTime(message.timestamp)}
          </span>
          
          {isAI && (
            <Button
              variant="ghost"
              size="sm"
              icon={Volume2}
              onClick={handlePlayAudio}
              className="text-gray-400 hover:text-gray-600"
              disabled={isPlaying}
            />
          )}
          
          <Button
            variant="ghost"
            size="sm"
            icon={Copy}
            onClick={handleCopyMessage}
            className="text-gray-400 hover:text-gray-600"
          />
          
          <Button
            variant="ghost"
            size="sm"
            icon={MoreHorizontal}
            className="text-gray-400 hover:text-gray-600"
          />
        </div>

        {/* Context Tags */}
        {message.metadata?.context && message.metadata.context.length > 0 && (
          <div className={`mt-2 flex flex-wrap gap-1 ${isUser ? 'justify-end' : 'justify-start'}`}>
            {message.metadata.context.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
              >
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
};
