import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Send,
  Image,
  Mic,
  MicO<PERSON>,
  Camera,
  Paperclip,
  MoreHorizontal,
  Heart,
  Smile,
  Volume2,
  VolumeX,
} from "lucide-react";
import { But<PERSON> } from "../ui/Button";
import { Card } from "../ui/Card";
import { useAuthStore } from "../../store/authStore";
import { ChatMessage } from "./ChatMessage";
import { PhotoSelector } from "./PhotoSelector";
import { VoiceRecorder } from "./VoiceRecorder";
import { aiService } from "../../services/aiService";
import { photoConversationService } from "../../services/photoConversationService";
import type { Photo } from "../../types";
import toast from "react-hot-toast";

interface Message {
  id: string;
  content: string;
  sender: "user" | "ai";
  timestamp: Date;
  photos?: Photo[];
  audioUrl?: string;
  metadata?: {
    emotion?: string;
    confidence?: number;
    context?: string[];
  };
}

interface ChatInterfaceProps {
  photos: Photo[];
  onNewPhoto?: (photo: Photo) => void;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  photos,
  onNewPhoto,
}) => {
  const { user } = useAuthStore();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showPhotoSelector, setShowPhotoSelector] = useState(false);
  const [showVoiceRecorder, setShowVoiceRecorder] = useState(false);
  const [selectedPhotos, setSelectedPhotos] = useState<Photo[]>([]);
  const [isListening, setIsListening] = useState(false);
  const [speechEnabled, setSpeechEnabled] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Load conversation history
    loadConversationHistory();

    // Send welcome message if no messages
    if (messages.length === 0) {
      sendWelcomeMessage();
    }
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const loadConversationHistory = async () => {
    // In a real implementation, load from database
    // For now, we'll start with an empty conversation
    console.log("Loading conversation history...");
  };

  const sendWelcomeMessage = () => {
    const welcomeMessage: Message = {
      id: `msg-${Date.now()}`,
      content: `Hello ${
        user?.name || "there"
      }! I'm here to help you explore your precious memories. Would you like to share a photo and tell me about it?`,
      sender: "ai",
      timestamp: new Date(),
      metadata: {
        emotion: "friendly",
        confidence: 1.0,
        context: ["welcome", "introduction"],
      },
    };

    setMessages([welcomeMessage]);

    // Speak the welcome message if speech is enabled
    if (speechEnabled) {
      speakMessage(welcomeMessage.content);
    }
  };

  const handleSendMessage = async () => {
    if (!inputText.trim() && selectedPhotos.length === 0) return;

    const userMessage: Message = {
      id: `msg-${Date.now()}`,
      content: inputText.trim(),
      sender: "user",
      timestamp: new Date(),
      photos: selectedPhotos.length > 0 ? [...selectedPhotos] : undefined,
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputText("");
    setSelectedPhotos([]);
    setIsTyping(true);

    try {
      // Generate AI response
      const aiResponse = await generateAIResponse(userMessage);

      // Link photos to conversation if any were shared
      if (userMessage.photos && userMessage.photos.length > 0) {
        try {
          for (const photo of userMessage.photos) {
            await photoConversationService.linkPhotoToConversation(
              photo.id,
              `conversation-${Date.now()}`, // In real app, use actual conversation ID
              userMessage.id,
              userMessage.content || "Photo shared in conversation",
              "positive", // Could be determined from AI analysis
              ["memory", "photo-sharing"]
            );
          }
        } catch (linkError) {
          console.error("Error linking photos to conversation:", linkError);
        }
      }

      setTimeout(() => {
        setMessages((prev) => [...prev, aiResponse]);
        setIsTyping(false);

        // Speak AI response if speech is enabled
        if (speechEnabled) {
          speakMessage(aiResponse.content);
        }
      }, 1000 + Math.random() * 2000); // Simulate thinking time
    } catch (error) {
      console.error("Error generating AI response:", error);
      setIsTyping(false);
      toast.error("Sorry, I had trouble responding. Please try again.");
    }
  };

  const generateAIResponse = async (userMessage: Message): Promise<Message> => {
    try {
      const context = {
        userId: user?.id || "",
        userProfile: {
          name: user?.name || "friend",
          memoryStage: "early" as const, // Could be determined from user profile
        },
        recentPhotos: photos.slice(-10), // Last 10 photos for context
        conversationHistory: messages.slice(-10), // Last 10 messages for context
        currentSession: {
          startTime: new Date(),
          photoCount: userMessage.photos?.length || 0,
          topics: ["memory", "conversation"],
        },
      };

      const aiResponse = await aiService.generateResponse(
        userMessage.content,
        userMessage.photos || [],
        context
      );

      return {
        id: `msg-${Date.now()}-ai`,
        content: aiResponse.content,
        sender: "ai",
        timestamp: new Date(),
        metadata: {
          emotion: aiResponse.emotion,
          confidence: aiResponse.confidence,
          context: ["memory", "conversation"],
        },
      };
    } catch (error) {
      console.error("Error generating AI response:", error);

      // Fallback response
      return {
        id: `msg-${Date.now()}-ai`,
        content:
          "I'm having trouble responding right now, but I'm here to listen. Would you like to share a photo or tell me about a memory?",
        sender: "ai",
        timestamp: new Date(),
        metadata: {
          emotion: "caring",
          confidence: 0.5,
          context: ["error", "fallback"],
        },
      };
    }
  };

  const speakMessage = (text: string) => {
    if ("speechSynthesis" in window && speechEnabled) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.8;
      utterance.pitch = 1.0;
      utterance.volume = 0.8;

      // Use a gentle, friendly voice if available
      const voices = speechSynthesis.getVoices();
      const preferredVoice = voices.find(
        (voice) =>
          voice.name.includes("Female") || voice.name.includes("Samantha")
      );
      if (preferredVoice) {
        utterance.voice = preferredVoice;
      }

      speechSynthesis.speak(utterance);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handlePhotoSelect = (photos: Photo[]) => {
    setSelectedPhotos(photos);
    setShowPhotoSelector(false);
  };

  const handleVoiceInput = (audioBlob: Blob) => {
    // In a real implementation, convert speech to text
    console.log("Voice input received:", audioBlob);
    setShowVoiceRecorder(false);
    toast.success("Voice message recorded! (Speech-to-text coming soon)");
  };

  const removeSelectedPhoto = (photoId: string) => {
    setSelectedPhotos((prev) => prev.filter((p) => p.id !== photoId));
  };

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Chat Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
              <Heart className="w-5 h-5 text-primary-600" />
            </div>
            <div>
              <h2 className="font-semibold text-gray-900">Memory Companion</h2>
              <p className="text-sm text-gray-600">
                {isTyping ? "Thinking..." : "Ready to chat about your memories"}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              icon={speechEnabled ? Volume2 : VolumeX}
              onClick={() => setSpeechEnabled(!speechEnabled)}
              className={speechEnabled ? "text-primary-600" : "text-gray-400"}
            />
            <Button variant="ghost" size="sm" icon={MoreHorizontal} />
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {messages.map((message) => (
            <ChatMessage
              key={message.id}
              message={message}
              onPhotoClick={(photo) => {
                // Handle photo click - could open in modal
                console.log("Photo clicked:", photo);
              }}
            />
          ))}
        </AnimatePresence>

        {isTyping && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center gap-2 text-gray-500"
          >
            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <Heart className="w-4 h-4 text-primary-600" />
            </div>
            <div className="flex gap-1">
              <div
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "0ms" }}
              />
              <div
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "150ms" }}
              />
              <div
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "300ms" }}
              />
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Selected Photos Preview */}
      {selectedPhotos.length > 0 && (
        <div className="p-4 bg-white border-t border-gray-200">
          <div className="flex items-center gap-2 mb-2">
            <Image className="w-4 h-4 text-gray-600" />
            <span className="text-sm font-medium text-gray-700">
              {selectedPhotos.length} photo
              {selectedPhotos.length !== 1 ? "s" : ""} selected
            </span>
          </div>
          <div className="flex gap-2 overflow-x-auto">
            {selectedPhotos.map((photo) => (
              <div key={photo.id} className="relative flex-shrink-0">
                <img
                  src={photo.url}
                  alt={photo.filename}
                  className="w-16 h-16 object-cover rounded-lg"
                />
                <button
                  onClick={() => removeSelectedPhoto(photo.id)}
                  className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Input Area */}
      <div className="bg-white border-t border-gray-200 p-4">
        <div className="flex items-end gap-3">
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              icon={Image}
              onClick={() => setShowPhotoSelector(true)}
              className="text-gray-600 hover:text-primary-600"
            />
            <Button
              variant="ghost"
              size="sm"
              icon={Mic}
              onClick={() => setShowVoiceRecorder(true)}
              className="text-gray-600 hover:text-primary-600"
            />
            <Button
              variant="ghost"
              size="sm"
              icon={Camera}
              className="text-gray-600 hover:text-primary-600"
            />
          </div>

          <div className="flex-1">
            <textarea
              ref={inputRef}
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Share a memory or ask about your photos..."
              className="w-full px-4 py-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              rows={1}
              style={{ minHeight: "48px", maxHeight: "120px" }}
            />
          </div>

          <Button
            variant="primary"
            icon={Send}
            onClick={handleSendMessage}
            disabled={!inputText.trim() && selectedPhotos.length === 0}
            className="px-4 py-3"
          />
        </div>
      </div>

      {/* Photo Selector Modal */}
      {showPhotoSelector && (
        <PhotoSelector
          photos={photos}
          onSelect={handlePhotoSelect}
          onClose={() => setShowPhotoSelector(false)}
          maxSelection={3}
        />
      )}

      {/* Voice Recorder Modal */}
      {showVoiceRecorder && (
        <VoiceRecorder
          onRecordingComplete={handleVoiceInput}
          onClose={() => setShowVoiceRecorder(false)}
        />
      )}
    </div>
  );
};
