import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Send,
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  <PERSON><PERSON><PERSON>,
  Heart,
  Camera,
  Smile,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { Button } from "../ui/Button";
import { Card } from "../ui/Card";
import { VoiceButton } from "../ui/VoiceButton";
import { useSpeechRecognition } from "../../hooks/useSpeechRecognition";
import { useTextToSpeech } from "../../hooks/useTextToSpeech";
import { useAuthStore } from "../../store/authStore";
import { aiService } from "../../lib/ai";
import { db } from "../../lib/supabase";
import type { Photo, Message } from "../../types";
import toast from "react-hot-toast";

interface ModernChatInterfaceProps {
  selectedPhoto?: Photo;
  photos: Photo[];
  onPhotoSelect: (photo: Photo) => void;
}

export const ModernChatInterface: React.FC<ModernChatInterfaceProps> = ({
  selectedPhoto,
  photos,
  onPhotoSelect,
}) => {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [currentConversationId, setCurrentConversationId] = useState<
    string | null
  >(null);
  const [conversationStarters, setConversationStarters] = useState<string[]>(
    []
  );
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const {
    isListening,
    transcript,
    startListening,
    stopListening,
    resetTranscript,
  } = useSpeechRecognition();

  const { isSpeaking, speak, stop: stopSpeaking } = useTextToSpeech();

  // Initialize conversation when photo changes or component mounts
  useEffect(() => {
    const initializeConversation = async () => {
      if (!user) return;

      try {
        // Create or get conversation
        const { data: conversation, error } = await db.createConversation(
          user.id,
          selectedPhoto?.id
        );

        if (error) throw error;

        setCurrentConversationId(conversation.id);

        // Load existing messages
        const { data: existingMessages, error: messagesError } =
          await db.getMessages(conversation.id);
        if (!messagesError && existingMessages) {
          const formattedMessages: Message[] = existingMessages.map((msg) => ({
            id: msg.id,
            type: msg.type as "user" | "assistant",
            content: msg.content,
            timestamp: new Date(msg.created_at),
            audioUrl: msg.audio_url || undefined,
          }));
          setMessages(formattedMessages);
        }

        // Generate conversation starters
        const starters = await aiService.generateConversationStarters({
          userName: user.name,
          conversationStyle: user.preferences.conversationStyle,
          photo: selectedPhoto,
          previousMessages: [],
        });
        setConversationStarters(starters);
      } catch (error) {
        console.error("Failed to initialize conversation:", error);
        toast.error("Failed to start conversation");
      }
    };

    initializeConversation();
  }, [selectedPhoto, user]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (transcript) {
      setInputValue(transcript);
    }
  }, [transcript]);

  const generateAIResponse = async (userMessage: string): Promise<string> => {
    if (!user) throw new Error("User not authenticated");

    const context = {
      userName: user.name,
      conversationStyle: user.preferences.conversationStyle,
      photo: selectedPhoto,
      previousMessages: messages,
    };

    try {
      const response = await aiService.generateResponse(context, userMessage);
      return response;
    } catch (error) {
      console.error("AI response error:", error);
      // Fallback response
      return `I'm here to chat with you, ${user.name}. Could you tell me more about that?`;
    }
  };

  const handleSendMessage = async (messageText?: string) => {
    const textToSend = messageText || inputValue;
    if (!textToSend.trim() || !currentConversationId || !user) return;

    const userMessage: Message = {
      id: `msg-${Date.now()}`,
      type: "user",
      content: textToSend,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    resetTranscript();
    setIsTyping(true);
    setShowSuggestions(false);

    try {
      // Save user message to database
      await db.addMessage(currentConversationId, "user", textToSend);

      // Generate AI response
      const aiResponseContent = await generateAIResponse(textToSend);

      const aiResponse: Message = {
        id: `msg-${Date.now() + 1}`,
        type: "assistant",
        content: aiResponseContent,
        timestamp: new Date(),
      };

      // Save AI response to database
      await db.addMessage(
        currentConversationId,
        "assistant",
        aiResponseContent
      );

      setMessages((prev) => [...prev, aiResponse]);
      setIsTyping(false);

      // Auto-speak the response if voice is enabled
      if (user.preferences.voiceEnabled) {
        speak(aiResponse.content);
      }
    } catch (error) {
      console.error("Failed to send message:", error);
      toast.error("Failed to send message. Please try again.");
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleVoiceStart = () => {
    if (isSpeaking) {
      stopSpeaking();
    }
    startListening();
  };

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-primary-25 via-white to-warm-25">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="p-6 bg-white/80 backdrop-blur-sm border-b border-gray-100"
      >
        <div className="text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
              <Heart className="w-4 h-4 text-white" />
            </div>
            <h1 className="text-xl font-bold text-gray-900">
              {t("chat.title")}
            </h1>
          </div>
          <p className="text-gray-600 text-sm">{t("chat.subtitle")}</p>
        </div>
      </motion.div>

      {/* Photo Selection */}
      {photos.length > 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="p-4 bg-white/50"
        >
          <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
            <Camera className="w-4 h-4" />
            {t("chat.selectPhoto")}
          </h3>
          <div className="flex gap-3 overflow-x-auto pb-2">
            {photos.map((photo) => (
              <motion.button
                key={photo.id}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => onPhotoSelect(photo)}
                className={`
                  flex-shrink-0 w-20 h-20 rounded-2xl overflow-hidden border-3 transition-all duration-300
                  ${
                    selectedPhoto?.id === photo.id
                      ? "border-primary-500 ring-4 ring-primary-200 shadow-lg"
                      : "border-white hover:border-primary-300 shadow-md hover:shadow-lg"
                  }
                `}
              >
                <img
                  src={photo.url}
                  alt={photo.filename}
                  className="w-full h-full object-cover"
                />
              </motion.button>
            ))}
          </div>
        </motion.div>
      )}

      {/* Selected Photo Display */}
      <AnimatePresence>
        {selectedPhoto && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="mx-4 mb-4"
          >
            <Card
              className="bg-gradient-to-r from-primary-50 to-warm-50 border-primary-200"
              padding="sm"
            >
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 rounded-xl overflow-hidden bg-white shadow-md">
                  <img
                    src={selectedPhoto.url}
                    alt={selectedPhoto.filename}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                    <Sparkles className="w-4 h-4 text-primary-600" />
                    Talking about this memory
                  </h3>
                  <p className="text-sm text-gray-600">
                    {selectedPhoto.filename}
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  icon={isSpeaking ? VolumeX : Volume2}
                  onClick={() =>
                    isSpeaking
                      ? stopSpeaking()
                      : speak("Let's explore this beautiful memory together!")
                  }
                />
              </div>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-12"
          >
            <div className="w-20 h-20 bg-gradient-to-br from-primary-100 to-warm-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Heart className="w-10 h-10 text-primary-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">
              {selectedPhoto ? "Ready to chat!" : t("chat.noPhotos")}
            </h3>
            <p className="text-gray-600 max-w-md mx-auto mb-8">
              {selectedPhoto
                ? "I'm excited to hear about this wonderful photo. What would you like to share?"
                : "Add some photos and let's start exploring your precious memories together."}
            </p>

            {/* Conversation Starters */}
            {showSuggestions && selectedPhoto && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-3"
              >
                <p className="text-sm font-medium text-gray-700 mb-4">
                  Try asking:
                </p>
                <div className="flex flex-wrap gap-2 justify-center max-w-md mx-auto">
                  {conversationStarters.slice(0, 3).map((starter, index) => (
                    <motion.button
                      key={index}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleSendMessage(starter)}
                      className="px-4 py-2 bg-white border border-primary-200 rounded-full text-sm text-primary-700 hover:bg-primary-50 hover:border-primary-300 transition-colors shadow-sm"
                    >
                      {starter}
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            )}
          </motion.div>
        ) : (
          <AnimatePresence>
            {messages.map((message, index) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                className={`flex ${
                  message.type === "user" ? "justify-end" : "justify-start"
                }`}
              >
                <div
                  className={`
                    max-w-xs sm:max-w-md px-6 py-4 rounded-3xl shadow-lg
                    ${
                      message.type === "user"
                        ? "bg-gradient-to-br from-primary-500 to-primary-600 text-white"
                        : "bg-white text-gray-900 border border-gray-100"
                    }
                  `}
                >
                  <p className="text-sm leading-relaxed">{message.content}</p>
                  <div className="flex items-center justify-between mt-2">
                    <p
                      className={`text-xs ${
                        message.type === "user"
                          ? "text-primary-100"
                          : "text-gray-500"
                      }`}
                    >
                      {message.timestamp.toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </p>
                    {message.type === "assistant" && (
                      <button
                        onClick={() => speak(message.content)}
                        className="ml-2 p-1 rounded-full hover:bg-gray-100 transition-colors"
                      >
                        <Volume2 className="w-3 h-3 text-gray-500" />
                      </button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        )}

        {isTyping && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-start"
          >
            <div className="bg-white px-6 py-4 rounded-3xl shadow-lg border border-gray-100">
              <div className="flex items-center gap-2">
                <div className="flex space-x-1">
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ repeat: Infinity, duration: 1, delay: 0 }}
                    className="w-2 h-2 bg-primary-400 rounded-full"
                  />
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ repeat: Infinity, duration: 1, delay: 0.2 }}
                    className="w-2 h-2 bg-primary-400 rounded-full"
                  />
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ repeat: Infinity, duration: 1, delay: 0.4 }}
                    className="w-2 h-2 bg-primary-400 rounded-full"
                  />
                </div>
                <span className="text-xs text-gray-500 ml-2">
                  {t("chat.aiTyping")}
                </span>
              </div>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="p-4 bg-white/80 backdrop-blur-sm border-t border-gray-100"
      >
        <div className="flex items-end gap-3 max-w-4xl mx-auto">
          <div className="flex-1 relative">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={
                isListening ? t("chat.listening") : t("chat.typeMessage")
              }
              className={`
                w-full px-6 py-4 border-2 rounded-2xl resize-none transition-all duration-200
                focus:outline-none focus:ring-4 focus:ring-primary-200 focus:border-primary-400
                ${
                  isListening
                    ? "border-primary-400 bg-primary-50"
                    : "border-gray-200 bg-white hover:border-gray-300"
                }
                ${
                  user?.preferences.fontSize === "large"
                    ? "text-lg"
                    : "text-base"
                }
              `}
              rows={1}
              disabled={isListening}
              style={{
                minHeight: "56px",
                fontSize:
                  user?.preferences.fontSize === "large" ? "18px" : "16px",
              }}
            />
            {isListening && (
              <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ repeat: Infinity, duration: 1 }}
                className="absolute right-4 top-1/2 transform -translate-y-1/2"
              >
                <div className="w-3 h-3 bg-primary-500 rounded-full" />
              </motion.div>
            )}
          </div>

          <VoiceButton
            isListening={isListening}
            isSpeaking={isSpeaking}
            onStartListening={handleVoiceStart}
            onStopListening={stopListening}
            onStopSpeaking={stopSpeaking}
            size="lg"
          />

          <Button
            variant="primary"
            size="lg"
            icon={Send}
            onClick={() => handleSendMessage()}
            disabled={!inputValue.trim() || isTyping}
            className="shadow-lg hover:shadow-xl"
          />
        </div>
      </motion.div>
    </div>
  );
};
