import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  MessageCircle,
  Bell,
  Volume2,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Save,
  RotateCcw,
  Info,
  Zap,
  Moon,
  Sun,
  <PERSON><PERSON><PERSON>riangle,
} from "lucide-react";
import { Button } from "../ui/Button";
import { Card } from "../ui/Card";
import { useAuthStore } from "../../store/authStore";
import toast from "react-hot-toast";

interface ChatConfigurationProps {
  patientName?: string;
}

export const ChatConfiguration: React.FC<ChatConfigurationProps> = ({
  patientName = "Patient",
}) => {
  const { user } = useAuthStore();
  const [newTopic, setNewTopic] = useState("");

  // Configuration state
  const [config, setConfig] = useState({
    // Notification frequency
    photoPromptFrequency: 4, // hours
    conversationReminders: true,
    quietHours: {
      enabled: true,
      start: "21:00",
      end: "09:00",
    },

    // Chat behavior
    conversationStyle: "encouraging", // casual, formal, encouraging
    responseLength: "medium", // short, medium, long
    memoryDepth: "high", // low, medium, high
    emotionalSupport: true,

    // AI personality
    aiPersonality: {
      warmth: 8, // 1-10
      patience: 9, // 1-10
      enthusiasm: 7, // 1-10
      formality: 3, // 1-10
    },

    // Safety settings
    sensitiveTopics: true,
    emergencyDetection: true,
    confusionSupport: true,

    // Engagement
    photoSuggestions: true,
    memoryExercises: false,
    dailyCheckIns: true,

    // Topics to avoid
    topicsToAvoid: [] as string[],
  });

  const handleSave = async () => {
    try {
      // TODO: Save to database
      toast.success("Chat configuration saved successfully!");
      console.log("Saving config:", config);
    } catch (error) {
      toast.error("Failed to save configuration");
    }
  };

  const handleReset = () => {
    // Reset to defaults
    setConfig({
      photoPromptFrequency: 4,
      conversationReminders: true,
      quietHours: { enabled: true, start: "21:00", end: "09:00" },
      conversationStyle: "encouraging",
      responseLength: "medium",
      memoryDepth: "high",
      emotionalSupport: true,
      aiPersonality: { warmth: 8, patience: 9, enthusiasm: 7, formality: 3 },
      sensitiveTopics: true,
      emergencyDetection: true,
      confusionSupport: true,
      photoSuggestions: true,
      memoryExercises: false,
      dailyCheckIns: true,
      topicsToAvoid: [],
    });
    toast.info("Configuration reset to defaults");
  };

  const addTopicToAvoid = () => {
    if (newTopic.trim() && !config.topicsToAvoid.includes(newTopic.trim())) {
      setConfig((prev) => ({
        ...prev,
        topicsToAvoid: [...prev.topicsToAvoid, newTopic.trim()],
      }));
      setNewTopic("");
    }
  };

  const removeTopicToAvoid = (topic: string) => {
    setConfig((prev) => ({
      ...prev,
      topicsToAvoid: prev.topicsToAvoid.filter((t) => t !== topic),
    }));
  };

  const commonSensitiveTopics = [
    "Death and dying",
    "Financial problems",
    "Family conflicts",
    "Health decline",
    "Past trauma",
    "Political topics",
    "Religious debates",
    "War and violence",
  ];

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Chat Configuration for {patientName}
        </h1>
        <p className="text-gray-600">
          Customize how the AI companion interacts with your patient
        </p>
      </div>

      {/* Notification Settings */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <Bell className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Notification Settings
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Photo Prompt Frequency
            </label>
            <select
              value={config.photoPromptFrequency}
              onChange={(e) =>
                setConfig({
                  ...config,
                  photoPromptFrequency: parseInt(e.target.value),
                })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
            >
              <option value={1}>Every hour</option>
              <option value={2}>Every 2 hours</option>
              <option value={4}>Every 4 hours (Recommended)</option>
              <option value={6}>Every 6 hours</option>
              <option value={12}>Twice daily</option>
              <option value={24}>Once daily</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quiet Hours
            </label>
            <div className="flex items-center gap-2">
              <input
                type="time"
                value={config.quietHours.start}
                onChange={(e) =>
                  setConfig({
                    ...config,
                    quietHours: { ...config.quietHours, start: e.target.value },
                  })
                }
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
              />
              <span className="text-gray-500">to</span>
              <input
                type="time"
                value={config.quietHours.end}
                onChange={(e) =>
                  setConfig({
                    ...config,
                    quietHours: { ...config.quietHours, end: e.target.value },
                  })
                }
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>
        </div>

        <div className="mt-4 space-y-3">
          <label className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={config.conversationReminders}
              onChange={(e) =>
                setConfig({
                  ...config,
                  conversationReminders: e.target.checked,
                })
              }
              className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
            />
            <span className="text-sm text-gray-700">
              Send conversation reminders
            </span>
          </label>

          <label className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={config.dailyCheckIns}
              onChange={(e) =>
                setConfig({ ...config, dailyCheckIns: e.target.checked })
              }
              className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
            />
            <span className="text-sm text-gray-700">
              Daily wellness check-ins
            </span>
          </label>
        </div>
      </Card>

      {/* AI Personality */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <Brain className="w-5 h-5 text-purple-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            AI Personality
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Conversation Style
            </label>
            <select
              value={config.conversationStyle}
              onChange={(e) =>
                setConfig({ ...config, conversationStyle: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
            >
              <option value="casual">Casual & Friendly</option>
              <option value="encouraging">Encouraging & Supportive</option>
              <option value="formal">Formal & Professional</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Response Length
            </label>
            <select
              value={config.responseLength}
              onChange={(e) =>
                setConfig({ ...config, responseLength: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
            >
              <option value="short">Short & Concise</option>
              <option value="medium">Medium Length</option>
              <option value="long">Detailed & Thorough</option>
            </select>
          </div>
        </div>

        {/* Personality Sliders */}
        <div className="mt-6 space-y-4">
          {[
            {
              key: "warmth",
              label: "Warmth & Empathy",
              icon: Heart,
              color: "text-red-500",
            },
            {
              key: "patience",
              label: "Patience Level",
              icon: Clock,
              color: "text-blue-500",
            },
            {
              key: "enthusiasm",
              label: "Enthusiasm",
              icon: Zap,
              color: "text-yellow-500",
            },
            {
              key: "formality",
              label: "Formality",
              icon: Settings,
              color: "text-gray-500",
            },
          ].map(({ key, label, icon: Icon, color }) => (
            <div key={key}>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Icon className={`w-4 h-4 ${color}`} />
                  <span className="text-sm font-medium text-gray-700">
                    {label}
                  </span>
                </div>
                <span className="text-sm text-gray-500">
                  {
                    config.aiPersonality[
                      key as keyof typeof config.aiPersonality
                    ]
                  }
                  /10
                </span>
              </div>
              <input
                type="range"
                min="1"
                max="10"
                value={
                  config.aiPersonality[key as keyof typeof config.aiPersonality]
                }
                onChange={(e) =>
                  setConfig({
                    ...config,
                    aiPersonality: {
                      ...config.aiPersonality,
                      [key]: parseInt(e.target.value),
                    },
                  })
                }
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
            </div>
          ))}
        </div>
      </Card>

      {/* Safety & Support */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <Heart className="w-5 h-5 text-green-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Safety & Support
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <label className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={config.sensitiveTopics}
              onChange={(e) =>
                setConfig({ ...config, sensitiveTopics: e.target.checked })
              }
              className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
            />
            <div>
              <span className="text-sm font-medium text-gray-700">
                Sensitive Topic Detection
              </span>
              <p className="text-xs text-gray-500">
                Avoid potentially upsetting topics
              </p>
            </div>
          </label>

          <label className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={config.emergencyDetection}
              onChange={(e) =>
                setConfig({ ...config, emergencyDetection: e.target.checked })
              }
              className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
            />
            <div>
              <span className="text-sm font-medium text-gray-700">
                Emergency Detection
              </span>
              <p className="text-xs text-gray-500">
                Alert caregivers if help is needed
              </p>
            </div>
          </label>

          <label className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={config.confusionSupport}
              onChange={(e) =>
                setConfig({ ...config, confusionSupport: e.target.checked })
              }
              className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
            />
            <div>
              <span className="text-sm font-medium text-gray-700">
                Confusion Support
              </span>
              <p className="text-xs text-gray-500">
                Extra patience for memory difficulties
              </p>
            </div>
          </label>

          <label className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={config.emotionalSupport}
              onChange={(e) =>
                setConfig({ ...config, emotionalSupport: e.target.checked })
              }
              className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
            />
            <div>
              <span className="text-sm font-medium text-gray-700">
                Emotional Support
              </span>
              <p className="text-xs text-gray-500">
                Provide comfort during difficult moments
              </p>
            </div>
          </label>
        </div>
      </Card>

      {/* Engagement Features */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <MessageCircle className="w-5 h-5 text-indigo-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Engagement Features
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <label className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={config.photoSuggestions}
              onChange={(e) =>
                setConfig({ ...config, photoSuggestions: e.target.checked })
              }
              className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
            />
            <div>
              <span className="text-sm font-medium text-gray-700">
                Photo Conversation Prompts
              </span>
              <p className="text-xs text-gray-500">
                Suggest discussing uploaded photos
              </p>
            </div>
          </label>

          <label className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={config.memoryExercises}
              onChange={(e) =>
                setConfig({ ...config, memoryExercises: e.target.checked })
              }
              className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
            />
            <div>
              <span className="text-sm font-medium text-gray-700">
                Memory Exercises
              </span>
              <p className="text-xs text-gray-500">
                Gentle cognitive activities
              </p>
            </div>
          </label>
        </div>
      </Card>

      {/* Topics to Avoid */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <AlertTriangle className="w-5 h-5 text-red-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Topics to Avoid
          </h3>
        </div>

        <p className="text-sm text-gray-600 mb-4">
          Specify topics that the AI should avoid discussing to ensure
          comfortable conversations.
        </p>

        {/* Add new topic */}
        <div className="flex gap-2 mb-4">
          <input
            type="text"
            value={newTopic}
            onChange={(e) => setNewTopic(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && addTopicToAvoid()}
            placeholder="Enter a topic to avoid..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
          />
          <Button
            variant="outline"
            onClick={addTopicToAvoid}
            disabled={!newTopic.trim()}
          >
            Add
          </Button>
        </div>

        {/* Common sensitive topics */}
        <div className="mb-4">
          <p className="text-sm font-medium text-gray-700 mb-2">
            Common sensitive topics:
          </p>
          <div className="flex flex-wrap gap-2">
            {commonSensitiveTopics.map((topic) => (
              <button
                key={topic}
                onClick={() => {
                  if (!config.topicsToAvoid.includes(topic)) {
                    setConfig((prev) => ({
                      ...prev,
                      topicsToAvoid: [...prev.topicsToAvoid, topic],
                    }));
                  }
                }}
                disabled={config.topicsToAvoid.includes(topic)}
                className={`px-3 py-1 text-sm rounded-full border transition-colors ${
                  config.topicsToAvoid.includes(topic)
                    ? "bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed"
                    : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                }`}
              >
                {topic}
              </button>
            ))}
          </div>
        </div>

        {/* Current topics to avoid */}
        {config.topicsToAvoid.length > 0 && (
          <div>
            <p className="text-sm font-medium text-gray-700 mb-2">
              Currently avoiding ({config.topicsToAvoid.length}):
            </p>
            <div className="flex flex-wrap gap-2">
              {config.topicsToAvoid.map((topic) => (
                <div
                  key={topic}
                  className="flex items-center gap-2 px-3 py-1 bg-red-50 text-red-700 rounded-full border border-red-200"
                >
                  <span className="text-sm">{topic}</span>
                  <button
                    onClick={() => removeTopicToAvoid(topic)}
                    className="text-red-500 hover:text-red-700"
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-center gap-4 pt-6">
        <Button variant="outline" icon={RotateCcw} onClick={handleReset}>
          Reset to Defaults
        </Button>
        <Button variant="primary" icon={Save} onClick={handleSave}>
          Save Configuration
        </Button>
      </div>

      {/* Info Box */}
      <Card className="p-4 bg-blue-50 border-blue-200">
        <div className="flex items-start gap-3">
          <Info className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">Configuration Tips:</p>
            <ul className="space-y-1 text-xs">
              <li>
                • Higher warmth and patience settings work well for memory care
              </li>
              <li>• Enable quiet hours to respect sleep schedules</li>
              <li>
                • Emergency detection can alert you if the patient needs help
              </li>
              <li>• Photo prompts encourage engagement with memories</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};
