import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Activity,
  MessageCircle,
  Pill,
  Heart,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Calendar,
  Users,
  Brain,
  Eye,
  Bell,
  Settings,
  Download,
  Filter,
  RefreshCw,
} from "lucide-react";
import { Button } from "../ui/Button";
import { Card } from "../ui/Card";
import { useAuthStore } from "../../store/authStore";
import { supabase } from "../../lib/supabase";
import { insightsService } from "../../services/insightsService";
import { ConversationInsights } from "./ConversationInsights";
import { MedicationAdherenceChart } from "./MedicationAdherenceChart";
// import { ActivityTimeline } from './ActivityTimeline';
// import { AlertsPanel } from './AlertsPanel';
import type {
  Photo,
  Medication,
  MedicationReminder,
  CaregiverInsight,
} from "../../types";
import { format, startOfWeek, endOfWeek, subDays } from "date-fns";
import toast from "react-hot-toast";

interface EnhancedCaregiverDashboardProps {
  photos: Photo[];
  medications: Medication[];
  reminders: MedicationReminder[];
  insights: CaregiverInsight[];
  patientId?: string;
  patientName?: string;
}

interface DashboardMetrics {
  conversationTime: number;
  photoEngagement: number;
  medicationAdherence: number;
  moodScore: number;
  activityLevel: number;
  alertsCount: number;
  weeklyTrends: {
    conversations: number[];
    photos: number[];
    medications: number[];
    mood: number[];
  };
}

interface Alert {
  id: string;
  type: "medication" | "engagement" | "mood" | "emergency";
  severity: "low" | "medium" | "high";
  title: string;
  description: string;
  timestamp: Date;
  acknowledged: boolean;
}

export const EnhancedCaregiverDashboard: React.FC<
  EnhancedCaregiverDashboardProps
> = ({
  photos,
  medications,
  reminders,
  insights,
  patientId,
  patientName = "Patient",
}) => {
  const { user } = useAuthStore();
  const [activeTab, setActiveTab] = useState<
    "overview" | "insights" | "activity" | "alerts"
  >("overview");
  const [timeRange, setTimeRange] = useState<"7d" | "30d" | "90d">("7d");
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    loadDashboardData();

    // Auto-refresh every 5 minutes if enabled
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(loadDashboardData, 5 * 60 * 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [timeRange, patientId]);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);

      // Calculate metrics
      const calculatedMetrics = calculateMetrics();
      setMetrics(calculatedMetrics);

      // Load alerts
      const alertsData = await loadAlerts();
      setAlerts(alertsData);

      // Generate insights if needed
      await generateInsights(calculatedMetrics);
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      toast.error("Failed to load dashboard data");
    } finally {
      setIsLoading(false);
    }
  };

  const calculateMetrics = (): DashboardMetrics => {
    const now = new Date();
    const daysBack = timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90;
    const startDate = subDays(now, daysBack);

    // Filter data by time range
    const recentPhotos = photos.filter((p) => p.uploadedAt >= startDate);
    const recentReminders = reminders.filter(
      (r) => r.scheduledTime >= startDate
    );
    const recentInsights = insights.filter((i) => i.date >= startDate);

    // Calculate conversation time (mock - would come from chat logs)
    const conversationTime = recentPhotos.length * 3.5; // Avg 3.5 min per photo discussion

    // Calculate photo engagement rate
    const photoEngagement =
      recentPhotos.length > 0
        ? Math.round(
            (recentPhotos.filter((p) => p.description).length /
              recentPhotos.length) *
              100
          )
        : 0;

    // Calculate medication adherence
    const takenReminders = recentReminders.filter(
      (r) => r.status === "taken"
    ).length;
    const medicationAdherence =
      recentReminders.length > 0
        ? Math.round((takenReminders / recentReminders.length) * 100)
        : 100;

    // Calculate mood score (from insights)
    const moodInsights = recentInsights.filter((i) => i.type === "mood");
    const moodScore =
      moodInsights.length > 0
        ? Math.round(
            moodInsights.reduce((sum, insight) => {
              const value =
                typeof insight.value === "number" ? insight.value : 75;
              return sum + value;
            }, 0) / moodInsights.length
          )
        : 75;

    // Calculate activity level
    const activityLevel = Math.min(
      100,
      Math.round((recentPhotos.length * 10 + takenReminders * 5) / daysBack)
    );

    // Count active alerts
    const alertsCount = alerts.filter(
      (a) => !a.acknowledged && a.severity !== "low"
    ).length;

    // Generate weekly trends (simplified)
    const weeklyTrends = {
      conversations: Array.from(
        { length: 7 },
        (_, i) => Math.floor(Math.random() * 10) + 5
      ),
      photos: Array.from(
        { length: 7 },
        (_, i) => Math.floor(Math.random() * 5) + 1
      ),
      medications: Array.from(
        { length: 7 },
        (_, i) => Math.floor(Math.random() * 20) + 80
      ),
      mood: Array.from(
        { length: 7 },
        (_, i) => Math.floor(Math.random() * 30) + 60
      ),
    };

    return {
      conversationTime,
      photoEngagement,
      medicationAdherence,
      moodScore,
      activityLevel,
      alertsCount,
      weeklyTrends,
    };
  };

  const loadAlerts = async (): Promise<Alert[]> => {
    // In a real implementation, this would load from database
    const mockAlerts: Alert[] = [];

    // Check for medication adherence issues
    const missedToday = reminders.filter(
      (r) =>
        r.status === "missed" &&
        r.scheduledTime.toDateString() === new Date().toDateString()
    );

    if (missedToday.length > 0) {
      mockAlerts.push({
        id: "med-alert-1",
        type: "medication",
        severity: "high",
        title: "Missed Medications",
        description: `${missedToday.length} medication${
          missedToday.length > 1 ? "s" : ""
        } missed today`,
        timestamp: new Date(),
        acknowledged: false,
      });
    }

    // Check for low engagement
    const recentPhotos = photos.filter(
      (p) => p.uploadedAt >= subDays(new Date(), 3)
    );

    if (recentPhotos.length === 0) {
      mockAlerts.push({
        id: "engagement-alert-1",
        type: "engagement",
        severity: "medium",
        title: "Low Photo Activity",
        description: "No photos shared in the last 3 days",
        timestamp: new Date(),
        acknowledged: false,
      });
    }

    return mockAlerts;
  };

  const generateInsights = async (metrics: DashboardMetrics) => {
    if (!patientId || !user?.tenant_id) return;

    try {
      const context = {
        userId: patientId,
        photos,
        medications,
        reminders,
        timeRange,
      };

      const generatedInsights = await insightsService.generateInsights(context);

      // Save insights to database
      await insightsService.saveInsights(
        generatedInsights,
        patientId,
        user.tenant_id
      );

      console.log("Generated and saved insights:", generatedInsights);
    } catch (error) {
      console.error("Error generating insights:", error);
    }
  };

  const getMetricTrend = (current: number, previous: number) => {
    if (current > previous) return "up";
    if (current < previous) return "down";
    return "stable";
  };

  const getMetricColor = (value: number, type: "percentage" | "score") => {
    if (type === "percentage") {
      if (value >= 80) return "text-green-600";
      if (value >= 60) return "text-yellow-600";
      return "text-red-600";
    } else {
      if (value >= 75) return "text-green-600";
      if (value >= 50) return "text-yellow-600";
      return "text-red-600";
    }
  };

  const tabs = [
    { id: "overview", label: "Overview", icon: Activity },
    { id: "insights", label: "Insights", icon: Brain },
    { id: "activity", label: "Activity", icon: Calendar },
    { id: "alerts", label: "Alerts", icon: Bell, badge: metrics?.alertsCount },
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Caregiver Dashboard
          </h1>
          <p className="text-gray-600">
            Monitoring {patientName}'s wellbeing and progress
          </p>
        </div>

        <div className="flex items-center gap-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>

          <Button
            variant="outline"
            size="sm"
            icon={RefreshCw}
            onClick={loadDashboardData}
            className={isLoading ? "animate-spin" : ""}
          >
            Refresh
          </Button>

          <Button variant="outline" size="sm" icon={Settings}>
            Settings
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="p-6">
            <div className="flex items-center justify-between mb-2">
              <MessageCircle className="w-8 h-8 text-blue-600" />
              <TrendingUp className="w-4 h-4 text-green-600" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900">
              {metrics.conversationTime}min
            </h3>
            <p className="text-gray-600">Daily Conversations</p>
            <div className="text-sm text-green-600 mt-1">+12% this week</div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between mb-2">
              <Eye className="w-8 h-8 text-purple-600" />
              <TrendingUp className="w-4 h-4 text-green-600" />
            </div>
            <h3
              className={`text-2xl font-bold ${getMetricColor(
                metrics.photoEngagement,
                "percentage"
              )}`}
            >
              {metrics.photoEngagement}%
            </h3>
            <p className="text-gray-600">Photo Engagement</p>
            <div className="text-sm text-gray-500 mt-1">
              {photos.length} photos shared
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between mb-2">
              <Pill className="w-8 h-8 text-green-600" />
              {metrics.medicationAdherence >= 80 ? (
                <TrendingUp className="w-4 h-4 text-green-600" />
              ) : (
                <TrendingDown className="w-4 h-4 text-red-600" />
              )}
            </div>
            <h3
              className={`text-2xl font-bold ${getMetricColor(
                metrics.medicationAdherence,
                "percentage"
              )}`}
            >
              {metrics.medicationAdherence}%
            </h3>
            <p className="text-gray-600">Medication Adherence</p>
            <div className="text-sm text-gray-500 mt-1">
              {medications.length} active medications
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between mb-2">
              <Heart className="w-8 h-8 text-red-600" />
              <TrendingUp className="w-4 h-4 text-green-600" />
            </div>
            <h3
              className={`text-2xl font-bold ${getMetricColor(
                metrics.moodScore,
                "score"
              )}`}
            >
              {metrics.moodScore}
            </h3>
            <p className="text-gray-600">Mood Score</p>
            <div className="text-sm text-green-600 mt-1">Stable this week</div>
          </Card>
        </div>
      )}

      {/* Navigation Tabs */}
      <Card className="p-1">
        <nav className="flex space-x-1">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;

            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`
                  flex items-center gap-2 px-4 py-3 rounded-lg font-medium text-sm transition-all relative
                  ${
                    isActive
                      ? "bg-primary-100 text-primary-700 shadow-sm"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                  }
                `}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
                {tab.badge && tab.badge > 0 && (
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {tab.badge}
                  </span>
                )}
              </button>
            );
          })}
        </nav>
      </Card>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2 }}
      >
        {activeTab === "overview" && metrics && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <MedicationAdherenceChart
              reminders={reminders}
              timeRange={timeRange}
            />
            <ConversationInsights
              photos={photos}
              insights={insights}
              timeRange={timeRange}
            />
          </div>
        )}

        {activeTab === "insights" && (
          <ConversationInsights
            photos={photos}
            insights={insights}
            timeRange={timeRange}
            detailed={true}
          />
        )}

        {activeTab === "activity" && (
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Activity Timeline
            </h3>
            <p className="text-gray-600">
              Activity timeline component coming soon...
            </p>
          </Card>
        )}

        {activeTab === "alerts" && (
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Alerts & Notifications
            </h3>
            <div className="space-y-3">
              {alerts.map((alert) => (
                <div
                  key={alert.id}
                  className={`p-4 rounded-lg border ${
                    alert.severity === "high"
                      ? "border-red-200 bg-red-50"
                      : alert.severity === "medium"
                      ? "border-yellow-200 bg-yellow-50"
                      : "border-blue-200 bg-blue-50"
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {alert.title}
                      </h4>
                      <p className="text-sm text-gray-600 mt-1">
                        {alert.description}
                      </p>
                      <p className="text-xs text-gray-500 mt-2">
                        {alert.timestamp.toLocaleString()}
                      </p>
                    </div>
                    {!alert.acknowledged && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setAlerts((prev) =>
                            prev.map((a) =>
                              a.id === alert.id
                                ? { ...a, acknowledged: true }
                                : a
                            )
                          );
                        }}
                      >
                        Acknowledge
                      </Button>
                    )}
                  </div>
                </div>
              ))}
              {alerts.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <CheckCircle className="w-12 h-12 mx-auto mb-3 text-green-500" />
                  <p>No active alerts</p>
                </div>
              )}
            </div>
          </Card>
        )}
      </motion.div>
    </div>
  );
};
