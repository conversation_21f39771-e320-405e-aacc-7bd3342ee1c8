import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Upload, 
  Users, 
  Calendar, 
  TrendingUp, 
  MessageCircle, 
  Heart,
  Clock,
  AlertCircle,
  CheckCircle,
  BarChart3,
  Settings
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { PhotoManager } from '../photos/PhotoManager';
import type { Photo, CaregiverInsight } from '../../types';

interface CaregiverPhotoManagerProps {
  photos: Photo[];
  onPhotosUploaded: (photos: Photo[]) => void;
  insights: CaregiverInsight[];
  patientName?: string;
}

export const CaregiverPhotoManager: React.FC<CaregiverPhotoManagerProps> = ({
  photos,
  onPhotosUploaded,
  insights,
  patientName = 'Patient',
}) => {
  const [activeView, setActiveView] = useState<'overview' | 'photos' | 'insights'>('overview');

  // Calculate engagement metrics
  const getEngagementMetrics = () => {
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const recentPhotos = photos.filter(p => p.uploadedAt > weekAgo);
    const monthlyPhotos = photos.filter(p => p.uploadedAt > monthAgo);
    const favoritePhotos = photos.filter(p => p.metadata?.isFavorite);
    const photosWithConversations = photos.filter(p => p.metadata?.conversationCount && p.metadata.conversationCount > 0);

    return {
      totalPhotos: photos.length,
      recentPhotos: recentPhotos.length,
      monthlyPhotos: monthlyPhotos.length,
      favoritePhotos: favoritePhotos.length,
      photosWithConversations: photosWithConversations.length,
      engagementRate: photos.length > 0 ? Math.round((photosWithConversations.length / photos.length) * 100) : 0,
    };
  };

  const metrics = getEngagementMetrics();

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {patientName}'s Photo Library
          </h1>
          <p className="text-gray-600">
            Manage photos and track engagement for memory care
          </p>
        </div>
        <Button
          variant="primary"
          icon={Upload}
          onClick={() => setActiveView('photos')}
        >
          Upload Photos
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Photos</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.totalPhotos}</p>
            </div>
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Calendar className="w-5 h-5 text-blue-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            {metrics.recentPhotos} added this week
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Engagement Rate</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.engagementRate}%</p>
            </div>
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-green-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            {metrics.photosWithConversations} photos discussed
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Favorites</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.favoritePhotos}</p>
            </div>
            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
              <Heart className="w-5 h-5 text-red-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            Most loved memories
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">This Month</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.monthlyPhotos}</p>
            </div>
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <Clock className="w-5 h-5 text-purple-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            Photos added recently
          </div>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-4">
            {photos.slice(0, 5).map((photo, index) => (
              <div key={photo.id} className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                  {photo.url ? (
                    <img
                      src={photo.url}
                      alt={photo.filename}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                      📸
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {photo.filename}
                  </p>
                  <p className="text-xs text-gray-500">
                    Uploaded {photo.uploadedAt.toLocaleDateString()}
                  </p>
                </div>
                <div className="flex items-center gap-1">
                  {photo.metadata?.isFavorite && (
                    <Heart className="w-4 h-4 text-red-500" />
                  )}
                  {photo.metadata?.conversationCount && photo.metadata.conversationCount > 0 && (
                    <MessageCircle className="w-4 h-4 text-blue-500" />
                  )}
                </div>
              </div>
            ))}
            {photos.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No photos uploaded yet
              </div>
            )}
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Insights & Recommendations</h3>
          <div className="space-y-4">
            {metrics.engagementRate < 30 && (
              <div className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg">
                <AlertCircle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">
                    Low Engagement
                  </p>
                  <p className="text-xs text-yellow-700">
                    Consider uploading more family photos or photos from special events to encourage conversations.
                  </p>
                </div>
              </div>
            )}

            {metrics.recentPhotos === 0 && (
              <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                <Clock className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-blue-800">
                    No Recent Photos
                  </p>
                  <p className="text-xs text-blue-700">
                    Upload some recent photos to keep the conversation fresh and engaging.
                  </p>
                </div>
              </div>
            )}

            {metrics.favoritePhotos > 5 && (
              <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-green-800">
                    Great Photo Collection
                  </p>
                  <p className="text-xs text-green-700">
                    {patientName} has many favorite photos! This shows strong emotional connection.
                  </p>
                </div>
              </div>
            )}

            <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
              <BarChart3 className="w-5 h-5 text-gray-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-800">
                  Photo Tip
                </p>
                <p className="text-xs text-gray-700">
                  Photos with people and events tend to generate more meaningful conversations.
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <Button
            variant="outline"
            icon={Upload}
            onClick={() => setActiveView('photos')}
            className="justify-start"
          >
            Upload New Photos
          </Button>
          <Button
            variant="outline"
            icon={BarChart3}
            onClick={() => setActiveView('insights')}
            className="justify-start"
          >
            View Detailed Insights
          </Button>
          <Button
            variant="outline"
            icon={Settings}
            className="justify-start"
          >
            Photo Settings
          </Button>
        </div>
      </Card>
    </div>
  );

  const renderPhotos = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Button
          variant="ghost"
          onClick={() => setActiveView('overview')}
        >
          ← Back to Overview
        </Button>
        <h2 className="text-xl font-semibold">Photo Management</h2>
        <div></div>
      </div>
      <PhotoManager
        photos={photos}
        onPhotosUploaded={onPhotosUploaded}
        userRole="caregiver"
        showUpload={true}
      />
    </div>
  );

  const renderInsights = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Button
          variant="ghost"
          onClick={() => setActiveView('overview')}
        >
          ← Back to Overview
        </Button>
        <h2 className="text-xl font-semibold">Detailed Insights</h2>
        <div></div>
      </div>
      <Card className="p-6">
        <p className="text-gray-600">
          Detailed insights and analytics will be available here.
          This will include conversation patterns, mood analysis, and engagement trends.
        </p>
      </Card>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Tabs */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview', icon: BarChart3 },
              { id: 'photos', label: 'Photos', icon: Calendar },
              { id: 'insights', label: 'Insights', icon: TrendingUp },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveView(tab.id as any)}
                  className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                    activeView === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <motion.div
          key={activeView}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          {activeView === 'overview' && renderOverview()}
          {activeView === 'photos' && renderPhotos()}
          {activeView === 'insights' && renderInsights()}
        </motion.div>
      </div>
    </div>
  );
};
