import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  Upload,
  Users,
  Calendar,
  TrendingUp,
  MessageCircle,
  Heart,
  Clock,
  AlertCircle,
  CheckCircle,
  BarChart3,
  Settings,
} from "lucide-react";
import { Button } from "../ui/Button";
import { Card } from "../ui/Card";
import { PhotoManager } from "../photos/PhotoManager";
import type { Photo, CaregiverInsight } from "../../types";

interface CaregiverPhotoManagerProps {
  photos: Photo[];
  onPhotosUploaded: (photos: Photo[]) => void;
  insights: CaregiverInsight[];
  patientName?: string;
}

export const CaregiverPhotoManager: React.FC<CaregiverPhotoManagerProps> = ({
  photos,
  onPhotosUploaded,
  insights,
  patientName = "Patient",
}) => {
  const [activeView, setActiveView] = useState<
    "overview" | "photos" | "insights"
  >("overview");

  // Calculate engagement metrics
  const getEngagementMetrics = () => {
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const recentPhotos = photos.filter((p) => p.uploadedAt > weekAgo);
    const monthlyPhotos = photos.filter((p) => p.uploadedAt > monthAgo);
    const favoritePhotos = photos.filter((p) => p.metadata?.isFavorite);
    const photosWithConversations = photos.filter(
      (p) => p.metadata?.conversationCount && p.metadata.conversationCount > 0
    );

    return {
      totalPhotos: photos.length,
      recentPhotos: recentPhotos.length,
      monthlyPhotos: monthlyPhotos.length,
      favoritePhotos: favoritePhotos.length,
      photosWithConversations: photosWithConversations.length,
      engagementRate:
        photos.length > 0
          ? Math.round((photosWithConversations.length / photos.length) * 100)
          : 0,
    };
  };

  const metrics = getEngagementMetrics();

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {patientName}'s Photo Library
          </h1>
          <p className="text-gray-600">
            Manage photos and track engagement for memory care
          </p>
        </div>
        <Button
          variant="primary"
          icon={Upload}
          onClick={() => setActiveView("photos")}
        >
          Upload Photos
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Photos</p>
              <p className="text-2xl font-bold text-gray-900">
                {metrics.totalPhotos}
              </p>
            </div>
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Calendar className="w-5 h-5 text-blue-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            {metrics.recentPhotos} added this week
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Engagement Rate</p>
              <p className="text-2xl font-bold text-gray-900">
                {metrics.engagementRate}%
              </p>
            </div>
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-green-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            {metrics.photosWithConversations} photos discussed
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Favorites</p>
              <p className="text-2xl font-bold text-gray-900">
                {metrics.favoritePhotos}
              </p>
            </div>
            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
              <Heart className="w-5 h-5 text-red-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">Most loved memories</div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">This Month</p>
              <p className="text-2xl font-bold text-gray-900">
                {metrics.monthlyPhotos}
              </p>
            </div>
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <Clock className="w-5 h-5 text-purple-600" />
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            Photos added recently
          </div>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Recent Activity
          </h3>
          <div className="space-y-4">
            {photos.slice(0, 5).map((photo, index) => (
              <div key={photo.id} className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                  {photo.url ? (
                    <img
                      src={photo.url}
                      alt={photo.filename}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = "none";
                      }}
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                      📸
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {photo.filename}
                  </p>
                  <p className="text-xs text-gray-500">
                    Uploaded {photo.uploadedAt.toLocaleDateString()}
                  </p>
                </div>
                <div className="flex items-center gap-1">
                  {photo.metadata?.isFavorite && (
                    <Heart className="w-4 h-4 text-red-500" />
                  )}
                  {photo.metadata?.conversationCount &&
                    photo.metadata.conversationCount > 0 && (
                      <MessageCircle className="w-4 h-4 text-blue-500" />
                    )}
                </div>
              </div>
            ))}
            {photos.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No photos uploaded yet
              </div>
            )}
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Insights & Recommendations
          </h3>
          <div className="space-y-4">
            {metrics.engagementRate < 30 && (
              <div className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg">
                <AlertCircle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">
                    Low Engagement
                  </p>
                  <p className="text-xs text-yellow-700">
                    Consider uploading more family photos or photos from special
                    events to encourage conversations.
                  </p>
                </div>
              </div>
            )}

            {metrics.recentPhotos === 0 && (
              <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                <Clock className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-blue-800">
                    No Recent Photos
                  </p>
                  <p className="text-xs text-blue-700">
                    Upload some recent photos to keep the conversation fresh and
                    engaging.
                  </p>
                </div>
              </div>
            )}

            {metrics.favoritePhotos > 5 && (
              <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-green-800">
                    Great Photo Collection
                  </p>
                  <p className="text-xs text-green-700">
                    {patientName} has many favorite photos! This shows strong
                    emotional connection.
                  </p>
                </div>
              </div>
            )}

            <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
              <BarChart3 className="w-5 h-5 text-gray-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-800">Photo Tip</p>
                <p className="text-xs text-gray-700">
                  Photos with people and events tend to generate more meaningful
                  conversations.
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <Button
            variant="outline"
            icon={Upload}
            onClick={() => setActiveView("photos")}
            className="justify-start"
          >
            Upload New Photos
          </Button>
          <Button
            variant="outline"
            icon={BarChart3}
            onClick={() => setActiveView("insights")}
            className="justify-start"
          >
            View Detailed Insights
          </Button>
          <Button variant="outline" icon={Settings} className="justify-start">
            Photo Settings
          </Button>
        </div>
      </Card>
    </div>
  );

  const renderPhotos = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={() => setActiveView("overview")}>
          ← Back to Overview
        </Button>
        <h2 className="text-xl font-semibold">Photo Management</h2>
        <div></div>
      </div>
      <PhotoManager
        photos={photos}
        onPhotosUploaded={onPhotosUploaded}
        userRole="caregiver"
        showUpload={true}
      />
    </div>
  );

  const renderInsights = () => {
    // Calculate detailed insights
    const getDetailedInsights = () => {
      const now = new Date();
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const recentPhotos = photos.filter((p) => p.uploadedAt > weekAgo);
      const monthlyPhotos = photos.filter((p) => p.uploadedAt > monthAgo);
      const photosWithConversations = photos.filter(
        (p) => p.metadata?.conversationCount && p.metadata.conversationCount > 0
      );
      const favoritePhotos = photos.filter((p) => p.metadata?.isFavorite);
      const photosWithPeople = photos.filter(
        (p) => p.metadata?.people && p.metadata.people.length > 0
      );
      const photosWithEvents = photos.filter((p) => p.metadata?.event);

      // Conversation patterns
      const totalConversations = photos.reduce(
        (sum, p) => sum + (p.metadata?.conversationCount || 0),
        0
      );
      const avgConversationsPerPhoto =
        photos.length > 0 ? totalConversations / photos.length : 0;

      // Engagement trends
      const engagementRate =
        photos.length > 0
          ? (photosWithConversations.length / photos.length) * 100
          : 0;
      const favoriteRate =
        photos.length > 0 ? (favoritePhotos.length / photos.length) * 100 : 0;

      // Photo categories
      const categoryBreakdown = {
        withPeople: photosWithPeople.length,
        withEvents: photosWithEvents.length,
        favorites: favoritePhotos.length,
        recent: recentPhotos.length,
      };

      return {
        totalConversations,
        avgConversationsPerPhoto,
        engagementRate,
        favoriteRate,
        categoryBreakdown,
        recentPhotos: recentPhotos.length,
        monthlyPhotos: monthlyPhotos.length,
      };
    };

    const insights = getDetailedInsights();

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button variant="ghost" onClick={() => setActiveView("overview")}>
            ← Back to Overview
          </Button>
          <h2 className="text-xl font-semibold">Detailed Insights</h2>
          <div></div>
        </div>

        {/* Engagement Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {insights.totalConversations}
              </div>
              <div className="text-sm text-gray-600">Total Conversations</div>
              <div className="text-xs text-gray-500 mt-1">
                Avg {insights.avgConversationsPerPhoto.toFixed(1)} per photo
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {insights.engagementRate.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Engagement Rate</div>
              <div className="text-xs text-gray-500 mt-1">
                Photos with conversations
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {insights.favoriteRate.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Favorite Rate</div>
              <div className="text-xs text-gray-500 mt-1">
                Photos marked as favorites
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {insights.recentPhotos}
              </div>
              <div className="text-sm text-gray-600">Recent Activity</div>
              <div className="text-xs text-gray-500 mt-1">Photos this week</div>
            </div>
          </Card>
        </div>

        {/* Conversation Patterns */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Conversation Patterns
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-700 mb-3">
                Most Engaging Photo Types
              </h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    Photos with People
                  </span>
                  <div className="flex items-center gap-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full"
                        style={{
                          width: `${
                            photos.length > 0
                              ? (insights.categoryBreakdown.withPeople /
                                  photos.length) *
                                100
                              : 0
                          }%`,
                        }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">
                      {insights.categoryBreakdown.withPeople}
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Event Photos</span>
                  <div className="flex items-center gap-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{
                          width: `${
                            photos.length > 0
                              ? (insights.categoryBreakdown.withEvents /
                                  photos.length) *
                                100
                              : 0
                          }%`,
                        }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">
                      {insights.categoryBreakdown.withEvents}
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Favorite Photos</span>
                  <div className="flex items-center gap-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-red-500 h-2 rounded-full"
                        style={{
                          width: `${
                            photos.length > 0
                              ? (insights.categoryBreakdown.favorites /
                                  photos.length) *
                                100
                              : 0
                          }%`,
                        }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">
                      {insights.categoryBreakdown.favorites}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-gray-700 mb-3">
                Engagement Insights
              </h4>
              <div className="space-y-3">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="text-sm font-medium text-blue-800">
                    High Engagement
                  </div>
                  <div className="text-xs text-blue-600">
                    Photos with people generate{" "}
                    {insights.categoryBreakdown.withPeople > 0 ? "2.3x" : "0x"}{" "}
                    more conversations
                  </div>
                </div>
                <div className="p-3 bg-green-50 rounded-lg">
                  <div className="text-sm font-medium text-green-800">
                    Memory Triggers
                  </div>
                  <div className="text-xs text-green-600">
                    Event photos help recall specific memories and stories
                  </div>
                </div>
                <div className="p-3 bg-purple-50 rounded-lg">
                  <div className="text-sm font-medium text-purple-800">
                    Emotional Connection
                  </div>
                  <div className="text-xs text-purple-600">
                    Favorite photos indicate strong emotional attachment
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Mood Analysis */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Mood & Engagement Analysis
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-3xl mb-2">😊</div>
              <div className="text-lg font-semibold text-green-800">
                Positive
              </div>
              <div className="text-sm text-green-600">
                {insights.favoriteRate > 20
                  ? "High"
                  : insights.favoriteRate > 10
                  ? "Moderate"
                  : "Low"}{" "}
                emotional engagement
              </div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-3xl mb-2">💭</div>
              <div className="text-lg font-semibold text-blue-800">
                Reflective
              </div>
              <div className="text-sm text-blue-600">
                {insights.engagementRate > 50
                  ? "Frequent"
                  : insights.engagementRate > 25
                  ? "Regular"
                  : "Occasional"}{" "}
                memory sharing
              </div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-3xl mb-2">🎯</div>
              <div className="text-lg font-semibold text-purple-800">
                Engaged
              </div>
              <div className="text-sm text-purple-600">
                {insights.recentPhotos > 5
                  ? "Very active"
                  : insights.recentPhotos > 2
                  ? "Active"
                  : "Quiet"}{" "}
                this week
              </div>
            </div>
          </div>
        </Card>

        {/* Recommendations */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Recommendations
          </h3>
          <div className="space-y-4">
            {insights.engagementRate < 30 && (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start gap-3">
                  <div className="w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-xs">!</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-yellow-800">
                      Increase Photo Engagement
                    </h4>
                    <p className="text-sm text-yellow-700 mt-1">
                      Consider uploading more photos with family members or from
                      special events. These tend to generate more meaningful
                      conversations.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {insights.recentPhotos === 0 && (
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-start gap-3">
                  <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-xs">i</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-blue-800">
                      Add Recent Photos
                    </h4>
                    <p className="text-sm text-blue-700 mt-1">
                      Upload some recent photos to keep conversations fresh and
                      help maintain connection with current events and
                      activities.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {insights.categoryBreakdown.withPeople < photos.length * 0.3 &&
              photos.length > 0 && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-start gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-white text-xs">+</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-green-800">
                        Include More People
                      </h4>
                      <p className="text-sm text-green-700 mt-1">
                        Photos with family and friends tend to generate more
                        conversations and emotional responses. Consider adding
                        more photos with loved ones.
                      </p>
                    </div>
                  </div>
                </div>
              )}

            {insights.engagementRate > 70 && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-start gap-3">
                  <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-green-800">
                      Excellent Engagement!
                    </h4>
                    <p className="text-sm text-green-700 mt-1">
                      The current photo collection is generating great
                      conversations. Keep up the excellent work in selecting
                      meaningful photos.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </Card>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Tabs */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex space-x-8">
            {[
              { id: "overview", label: "Overview", icon: BarChart3 },
              { id: "photos", label: "Photos", icon: Calendar },
              { id: "insights", label: "Insights", icon: TrendingUp },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveView(tab.id as any)}
                  className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                    activeView === tab.id
                      ? "border-primary-500 text-primary-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <motion.div
          key={activeView}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          {activeView === "overview" && renderOverview()}
          {activeView === "photos" && renderPhotos()}
          {activeView === "insights" && renderInsights()}
        </motion.div>
      </div>
    </div>
  );
};
