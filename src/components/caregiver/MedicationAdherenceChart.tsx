import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  Pill, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Calendar
} from 'lucide-react';
import { Card } from '../ui/Card';
import type { MedicationReminder } from '../../types';
import { format, subDays, startOfDay, eachDayOfInterval } from 'date-fns';

interface MedicationAdherenceChartProps {
  reminders: MedicationReminder[];
  timeRange: '7d' | '30d' | '90d';
}

interface AdherenceData {
  date: string;
  taken: number;
  missed: number;
  total: number;
  percentage: number;
}

interface AdherenceMetrics {
  overallAdherence: number;
  trend: number;
  bestDay: string;
  worstDay: string;
  consistencyScore: number;
  totalMedications: number;
  dailyData: AdherenceData[];
}

export const MedicationAdherenceChart: React.FC<MedicationAdherenceChartProps> = ({
  reminders,
  timeRange
}) => {
  const metrics = useMemo(() => {
    const daysBack = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    const startDate = subDays(new Date(), daysBack);
    const endDate = new Date();
    
    const dateRange = eachDayOfInterval({ start: startDate, end: endDate });
    
    // Calculate daily adherence data
    const dailyData: AdherenceData[] = dateRange.map(date => {
      const dayReminders = reminders.filter(r => 
        startOfDay(r.scheduledTime).getTime() === startOfDay(date).getTime()
      );
      
      const taken = dayReminders.filter(r => r.status === 'taken').length;
      const missed = dayReminders.filter(r => r.status === 'missed').length;
      const total = dayReminders.length;
      const percentage = total > 0 ? Math.round((taken / total) * 100) : 0;
      
      return {
        date: format(date, 'yyyy-MM-dd'),
        taken,
        missed,
        total,
        percentage
      };
    });

    // Calculate overall metrics
    const totalReminders = reminders.length;
    const takenReminders = reminders.filter(r => r.status === 'taken').length;
    const overallAdherence = totalReminders > 0 
      ? Math.round((takenReminders / totalReminders) * 100) 
      : 0;

    // Calculate trend (simplified - comparing first half vs second half)
    const midPoint = Math.floor(dailyData.length / 2);
    const firstHalf = dailyData.slice(0, midPoint);
    const secondHalf = dailyData.slice(midPoint);
    
    const firstHalfAvg = firstHalf.length > 0 
      ? firstHalf.reduce((sum, day) => sum + day.percentage, 0) / firstHalf.length 
      : 0;
    const secondHalfAvg = secondHalf.length > 0 
      ? secondHalf.reduce((sum, day) => sum + day.percentage, 0) / secondHalf.length 
      : 0;
    
    const trend = secondHalfAvg - firstHalfAvg;

    // Find best and worst days
    const sortedDays = [...dailyData].sort((a, b) => b.percentage - a.percentage);
    const bestDay = sortedDays[0]?.date || '';
    const worstDay = sortedDays[sortedDays.length - 1]?.date || '';

    // Calculate consistency score (lower standard deviation = higher consistency)
    const percentages = dailyData.map(d => d.percentage);
    const mean = percentages.reduce((sum, p) => sum + p, 0) / percentages.length;
    const variance = percentages.reduce((sum, p) => sum + Math.pow(p - mean, 2), 0) / percentages.length;
    const stdDev = Math.sqrt(variance);
    const consistencyScore = Math.max(0, Math.round(100 - stdDev));

    // Count unique medications
    const uniqueMedications = new Set(reminders.map(r => r.medicationId)).size;

    return {
      overallAdherence,
      trend,
      bestDay,
      worstDay,
      consistencyScore,
      totalMedications: uniqueMedications,
      dailyData
    } as AdherenceMetrics;
  }, [reminders, timeRange]);

  const getAdherenceColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-green-500';
    if (percentage >= 70) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getAdherenceTextColor = (percentage: number) => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getTrendIcon = (trend: number) => {
    return trend > 0 
      ? <TrendingUp className="w-4 h-4 text-green-600" />
      : <TrendingDown className="w-4 h-4 text-red-600" />;
  };

  const maxBarHeight = 60; // pixels

  return (
    <Card className="p-6">
      <div className="flex items-center gap-3 mb-6">
        <Pill className="w-6 h-6 text-green-600" />
        <h3 className="text-lg font-semibold text-gray-900">Medication Adherence</h3>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="text-center">
          <div className={`text-2xl font-bold ${getAdherenceTextColor(metrics.overallAdherence)}`}>
            {metrics.overallAdherence}%
          </div>
          <div className="text-sm text-gray-600">Overall</div>
          <div className="flex items-center justify-center gap-1 mt-1">
            {getTrendIcon(metrics.trend)}
            <span className={`text-xs ${metrics.trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {Math.abs(Math.round(metrics.trend))}%
            </span>
          </div>
        </div>

        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">{metrics.consistencyScore}</div>
          <div className="text-sm text-gray-600">Consistency</div>
          <div className="text-xs text-gray-500 mt-1">Score</div>
        </div>

        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">{metrics.totalMedications}</div>
          <div className="text-sm text-gray-600">Medications</div>
          <div className="text-xs text-gray-500 mt-1">Active</div>
        </div>

        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">
            {reminders.filter(r => r.status === 'taken').length}
          </div>
          <div className="text-sm text-gray-600">Taken</div>
          <div className="text-xs text-gray-500 mt-1">Total doses</div>
        </div>
      </div>

      {/* Daily Adherence Chart */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-900 mb-3">Daily Adherence ({timeRange})</h4>
        
        <div className="flex items-end justify-between gap-1 h-20 mb-2">
          {metrics.dailyData.map((day, index) => (
            <motion.div
              key={day.date}
              initial={{ height: 0 }}
              animate={{ height: `${(day.percentage / 100) * maxBarHeight}px` }}
              transition={{ delay: index * 0.05 }}
              className={`
                flex-1 rounded-t-sm min-w-0 relative group cursor-pointer
                ${getAdherenceColor(day.percentage)}
              `}
              title={`${format(new Date(day.date), 'MMM d')}: ${day.percentage}% (${day.taken}/${day.total})`}
            >
              {/* Tooltip */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                {format(new Date(day.date), 'MMM d')}: {day.percentage}%
                <br />
                {day.taken}/{day.total} doses
              </div>
            </motion.div>
          ))}
        </div>

        {/* X-axis labels */}
        <div className="flex justify-between text-xs text-gray-500">
          {metrics.dailyData.map((day, index) => {
            // Show every nth label based on time range to avoid crowding
            const showLabel = timeRange === '7d' || 
              (timeRange === '30d' && index % 5 === 0) || 
              (timeRange === '90d' && index % 15 === 0);
            
            return (
              <span key={day.date} className={showLabel ? '' : 'invisible'}>
                {format(new Date(day.date), timeRange === '7d' ? 'EEE' : 'M/d')}
              </span>
            );
          })}
        </div>
      </div>

      {/* Status Legend */}
      <div className="flex items-center justify-center gap-6 mb-6">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-green-500 rounded"></div>
          <span className="text-sm text-gray-600">90%+ (Excellent)</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-yellow-500 rounded"></div>
          <span className="text-sm text-gray-600">70-89% (Good)</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-red-500 rounded"></div>
          <span className="text-sm text-gray-600">&lt;70% (Needs Attention)</span>
        </div>
      </div>

      {/* Insights */}
      <div className="space-y-3">
        {metrics.overallAdherence >= 90 && (
          <div className="flex items-start gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
            <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
            <div>
              <h5 className="font-medium text-green-800">Excellent Adherence</h5>
              <p className="text-sm text-green-700">
                Medication adherence is excellent. Keep up the great work!
              </p>
            </div>
          </div>
        )}

        {metrics.overallAdherence < 70 && (
          <div className="flex items-start gap-3 p-3 bg-red-50 border border-red-200 rounded-lg">
            <AlertTriangle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
            <div>
              <h5 className="font-medium text-red-800">Adherence Needs Attention</h5>
              <p className="text-sm text-red-700">
                Consider setting up additional reminders or discussing barriers with healthcare provider.
              </p>
            </div>
          </div>
        )}

        {metrics.trend < -10 && (
          <div className="flex items-start gap-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <TrendingDown className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
            <div>
              <h5 className="font-medium text-yellow-800">Declining Trend</h5>
              <p className="text-sm text-yellow-700">
                Adherence has decreased recently. Consider checking in with the patient.
              </p>
            </div>
          </div>
        )}

        {metrics.consistencyScore < 60 && (
          <div className="flex items-start gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <Clock className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
            <div>
              <h5 className="font-medium text-blue-800">Inconsistent Pattern</h5>
              <p className="text-sm text-blue-700">
                Medication timing varies significantly. Consider establishing a more consistent routine.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Best/Worst Days */}
      {metrics.bestDay && metrics.worstDay && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Best day: </span>
              <span className="font-medium text-green-600">
                {format(new Date(metrics.bestDay), 'MMM d')}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Needs focus: </span>
              <span className="font-medium text-red-600">
                {format(new Date(metrics.worstDay), 'MMM d')}
              </span>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};
