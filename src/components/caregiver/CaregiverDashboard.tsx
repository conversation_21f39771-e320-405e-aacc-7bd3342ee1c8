import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Heart, MessageCircle, Pill, TrendingUp, Calendar, Bell, User, Settings } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { format, startOfWeek, endOfWeek } from 'date-fns';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { useAuthStore } from '../../store/authStore';
import type { CaregiverInsight } from '../../types';

export const CaregiverDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const [selectedPatient, setSelectedPatient] = useState<string>('patient1');

  // Mock data - in production this would come from API
  const patients = [
    {
      id: 'patient1',
      name: '<PERSON>',
      avatar: '/api/placeholder/40/40',
      lastActive: new Date(),
      mood: 'positive',
    },
  ];

  const insights: CaregiverInsight[] = [
    {
      id: '1',
      type: 'engagement',
      title: 'Daily Conversations',
      description: 'Average conversation time increased this week',
      value: '15 min',
      trend: 'up',
      date: new Date(),
      severity: 'low',
    },
    {
      id: '2',
      type: 'medication',
      title: 'Medication Adherence',
      description: 'All medications taken on time today',
      value: '100%',
      trend: 'stable',
      date: new Date(),
      severity: 'low',
    },
  ];

  const weekStart = startOfWeek(new Date());
  const weekEnd = endOfWeek(new Date());

  const currentPatient = patients.find(p => p.id === selectedPatient);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Caregiver Dashboard</h1>
              <p className="text-gray-600">
                {format(weekStart, 'MMM d')} - {format(weekEnd, 'MMM d, yyyy')}
              </p>
            </div>
            <div className="flex items-center gap-4">
              <Button variant="ghost" icon={Bell}>
                Notifications
              </Button>
              <Button variant="ghost" icon={Settings}>
                Settings
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Patient Selector */}
        {patients.length > 1 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mb-8"
          >
            <Card padding="sm">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium text-gray-700">Viewing data for:</span>
                <div className="flex gap-2">
                  {patients.map((patient) => (
                    <button
                      key={patient.id}
                      onClick={() => setSelectedPatient(patient.id)}
                      className={`
                        flex items-center gap-2 px-4 py-2 rounded-lg transition-colors
                        ${selectedPatient === patient.id
                          ? 'bg-primary-100 text-primary-700 border border-primary-200'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }
                      `}
                    >
                      <div className="w-6 h-6 bg-primary-200 rounded-full flex items-center justify-center">
                        <User className="w-3 h-3" />
                      </div>
                      {patient.name}
                    </button>
                  ))}
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Stats Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          <Card className="text-center">
            <MessageCircle className="w-8 h-8 text-primary-600 mx-auto mb-2" />
            <h3 className="text-2xl font-bold text-gray-900">12 min</h3>
            <p className="text-gray-600">Daily Conversations</p>
            <div className="flex items-center justify-center gap-1 mt-1">
              <TrendingUp className="w-4 h-4 text-success-600" />
              <span className="text-xs text-success-600">+15%</span>
            </div>
          </Card>

          <Card className="text-center">
            <Pill className="w-8 h-8 text-primary-600 mx-auto mb-2" />
            <h3 className="text-2xl font-bold text-gray-900">95%</h3>
            <p className="text-gray-600">Medication Adherence</p>
            <div className="flex items-center justify-center gap-1 mt-1">
              <span className="text-xs text-gray-500">Stable</span>
            </div>
          </Card>

          <Card className="text-center">
            <Heart className="w-8 h-8 text-primary-600 mx-auto mb-2" />
            <h3 className="text-2xl font-bold text-gray-900">Positive</h3>
            <p className="text-gray-600">Overall Mood</p>
            <div className="flex items-center justify-center gap-1 mt-1">
              <TrendingUp className="w-4 h-4 text-success-600" />
              <span className="text-xs text-success-600">Improved</span>
            </div>
          </Card>

          <Card className="text-center">
            <Calendar className="w-8 h-8 text-primary-600 mx-auto mb-2" />
            <h3 className="text-2xl font-bold text-gray-900">8</h3>
            <p className="text-gray-600">Photos Discussed</p>
            <div className="flex items-center justify-center gap-1 mt-1">
              <TrendingUp className="w-4 h-4 text-success-600" />
              <span className="text-xs text-success-600">+3 this week</span>
            </div>
          </Card>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Recent Activity */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              <h2 className="text-xl font-semibold text-gray-900">Recent Activity</h2>
              
              <div className="space-y-4">
                <Card className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                    <MessageCircle className="w-5 h-5 text-primary-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">Photo Conversation</h3>
                    <p className="text-gray-600 text-sm">
                      Had a wonderful 15-minute conversation about family vacation photos
                    </p>
                    <p className="text-xs text-gray-500 mt-1">2 hours ago</p>
                  </div>
                </Card>

                <Card className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-success-100 rounded-full flex items-center justify-center">
                    <Pill className="w-5 h-5 text-success-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">Medication Taken</h3>
                    <p className="text-gray-600 text-sm">
                      Morning medications taken on time
                    </p>
                    <p className="text-xs text-gray-500 mt-1">8:00 AM</p>
                  </div>
                </Card>

                <Card className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-warm-100 rounded-full flex items-center justify-center">
                    <Heart className="w-5 h-5 text-warm-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">Mood Update</h3>
                    <p className="text-gray-600 text-sm">
                      Mood detected as positive during conversation
                    </p>
                    <p className="text-xs text-gray-500 mt-1">Yesterday</p>
                  </div>
                </Card>
              </div>
            </motion.div>
          </div>

          {/* Quick Actions & Insights */}
          <div className="space-y-6">
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
            >
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
              <div className="space-y-3">
                <Button variant="primary" icon={MessageCircle} fullWidth>
                  Start Conversation
                </Button>
                <Button variant="secondary" icon={Pill} fullWidth>
                  Check Medications
                </Button>
                <Button variant="ghost" icon={Calendar} fullWidth>
                  View Schedule
                </Button>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
            >
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Key Insights</h2>
              <div className="space-y-3">
                {insights.map((insight) => (
                  <Card key={insight.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-gray-900">{insight.title}</h3>
                      <span className="text-lg font-bold text-primary-600">{insight.value}</span>
                    </div>
                    <p className="text-sm text-gray-600">{insight.description}</p>
                    {insight.trend && (
                      <div className="flex items-center gap-1">
                        <TrendingUp className="w-4 h-4 text-success-600" />
                        <span className="text-xs text-success-600 capitalize">{insight.trend}</span>
                      </div>
                    )}
                  </Card>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};