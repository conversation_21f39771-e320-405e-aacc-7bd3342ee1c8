import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  MessageCircle, 
  Image, 
  Clock, 
  Heart, 
  TrendingUp, 
  TrendingDown,
  Users,
  Calendar,
  MapPin,
  Smile,
  Frown,
  Meh
} from 'lucide-react';
import { Card } from '../ui/Card';
import type { Photo, CaregiverInsight } from '../../types';
import { format, subDays, startOfDay } from 'date-fns';

interface ConversationInsightsProps {
  photos: Photo[];
  insights: CaregiverInsight[];
  timeRange: '7d' | '30d' | '90d';
  detailed?: boolean;
}

interface ConversationMetrics {
  totalConversations: number;
  averageLength: number;
  photoDiscussions: number;
  emotionalTone: 'positive' | 'neutral' | 'negative';
  topTopics: string[];
  engagementTrend: number;
  memoryRecall: number;
  socialConnections: number;
}

export const ConversationInsights: React.FC<ConversationInsightsProps> = ({
  photos,
  insights,
  timeRange,
  detailed = false
}) => {
  const metrics = useMemo(() => {
    const daysBack = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    const startDate = subDays(new Date(), daysBack);
    
    const recentPhotos = photos.filter(p => p.uploadedAt >= startDate);
    const recentInsights = insights.filter(i => i.date >= startDate);

    // Calculate conversation metrics
    const totalConversations = recentPhotos.length; // Assuming each photo leads to conversation
    const averageLength = totalConversations > 0 ? 4.2 : 0; // Average minutes per conversation
    const photoDiscussions = recentPhotos.filter(p => p.description).length;
    
    // Analyze emotional tone from photo descriptions and metadata
    const emotionalWords = {
      positive: ['happy', 'joy', 'love', 'wonderful', 'beautiful', 'amazing', 'great', 'fun'],
      negative: ['sad', 'difficult', 'hard', 'miss', 'gone', 'lost', 'worry'],
      neutral: ['remember', 'think', 'was', 'were', 'time', 'day', 'place']
    };

    let positiveCount = 0;
    let negativeCount = 0;
    let neutralCount = 0;

    recentPhotos.forEach(photo => {
      const text = (photo.description || '').toLowerCase();
      const hasPositive = emotionalWords.positive.some(word => text.includes(word));
      const hasNegative = emotionalWords.negative.some(word => text.includes(word));
      
      if (hasPositive) positiveCount++;
      else if (hasNegative) negativeCount++;
      else neutralCount++;
    });

    const emotionalTone = positiveCount > negativeCount && positiveCount > neutralCount 
      ? 'positive' 
      : negativeCount > positiveCount && negativeCount > neutralCount 
        ? 'negative' 
        : 'neutral';

    // Extract topics from photo metadata and descriptions
    const topTopics = ['Family', 'Travel', 'Holidays', 'Friends', 'Home'];
    
    // Calculate engagement trend (simplified)
    const engagementTrend = totalConversations > 0 ? 15 : -5; // Percentage change

    // Memory recall score (based on photo descriptions and details)
    const memoryRecall = photoDiscussions > 0 
      ? Math.round((photoDiscussions / totalConversations) * 100)
      : 0;

    // Social connections (people mentioned in photos)
    const socialConnections = new Set(
      recentPhotos.flatMap(p => p.metadata?.people || [])
    ).size;

    return {
      totalConversations,
      averageLength,
      photoDiscussions,
      emotionalTone,
      topTopics,
      engagementTrend,
      memoryRecall,
      socialConnections
    } as ConversationMetrics;
  }, [photos, insights, timeRange]);

  const getEmotionIcon = (tone: string) => {
    switch (tone) {
      case 'positive': return <Smile className="w-5 h-5 text-green-600" />;
      case 'negative': return <Frown className="w-5 h-5 text-red-600" />;
      default: return <Meh className="w-5 h-5 text-yellow-600" />;
    }
  };

  const getEmotionColor = (tone: string) => {
    switch (tone) {
      case 'positive': return 'text-green-600 bg-green-50 border-green-200';
      case 'negative': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
  };

  const getTrendIcon = (trend: number) => {
    return trend > 0 
      ? <TrendingUp className="w-4 h-4 text-green-600" />
      : <TrendingDown className="w-4 h-4 text-red-600" />;
  };

  if (!detailed) {
    return (
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <MessageCircle className="w-6 h-6 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Conversation Insights</h3>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{metrics.totalConversations}</div>
            <div className="text-sm text-gray-600">Conversations</div>
            <div className="flex items-center justify-center gap-1 mt-1">
              {getTrendIcon(metrics.engagementTrend)}
              <span className={`text-xs ${metrics.engagementTrend > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {Math.abs(metrics.engagementTrend)}%
              </span>
            </div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{metrics.averageLength}min</div>
            <div className="text-sm text-gray-600">Avg Length</div>
            <div className="text-xs text-gray-500 mt-1">Per conversation</div>
          </div>
        </div>

        <div className={`p-3 rounded-lg border ${getEmotionColor(metrics.emotionalTone)}`}>
          <div className="flex items-center gap-2 mb-1">
            {getEmotionIcon(metrics.emotionalTone)}
            <span className="font-medium capitalize">{metrics.emotionalTone} Tone</span>
          </div>
          <p className="text-sm">
            Overall emotional tone in recent conversations
          </p>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6 text-center">
          <MessageCircle className="w-8 h-8 text-blue-600 mx-auto mb-3" />
          <div className="text-2xl font-bold text-gray-900">{metrics.totalConversations}</div>
          <div className="text-gray-600">Total Conversations</div>
          <div className="flex items-center justify-center gap-1 mt-2">
            {getTrendIcon(metrics.engagementTrend)}
            <span className={`text-sm ${metrics.engagementTrend > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {Math.abs(metrics.engagementTrend)}% vs last period
            </span>
          </div>
        </Card>

        <Card className="p-6 text-center">
          <Clock className="w-8 h-8 text-purple-600 mx-auto mb-3" />
          <div className="text-2xl font-bold text-gray-900">{metrics.averageLength}min</div>
          <div className="text-gray-600">Average Length</div>
          <div className="text-sm text-gray-500 mt-2">
            {metrics.totalConversations * metrics.averageLength}min total
          </div>
        </Card>

        <Card className="p-6 text-center">
          <Image className="w-8 h-8 text-green-600 mx-auto mb-3" />
          <div className="text-2xl font-bold text-gray-900">{metrics.photoDiscussions}</div>
          <div className="text-gray-600">Photo Discussions</div>
          <div className="text-sm text-gray-500 mt-2">
            {metrics.totalConversations > 0 
              ? Math.round((metrics.photoDiscussions / metrics.totalConversations) * 100)
              : 0}% engagement rate
          </div>
        </Card>
      </div>

      {/* Emotional Tone Analysis */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Emotional Tone Analysis</h3>
        
        <div className={`p-4 rounded-lg border ${getEmotionColor(metrics.emotionalTone)} mb-4`}>
          <div className="flex items-center gap-3 mb-2">
            {getEmotionIcon(metrics.emotionalTone)}
            <span className="font-semibold text-lg capitalize">{metrics.emotionalTone} Overall Tone</span>
          </div>
          <p className="text-sm">
            {metrics.emotionalTone === 'positive' && 
              'Conversations show positive emotional engagement with memories and photos.'}
            {metrics.emotionalTone === 'neutral' && 
              'Conversations maintain a balanced emotional tone with mixed sentiments.'}
            {metrics.emotionalTone === 'negative' && 
              'Conversations may indicate some emotional challenges or difficult memories.'}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Memory Recall</h4>
            <div className="flex items-center gap-2">
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all"
                  style={{ width: `${metrics.memoryRecall}%` }}
                />
              </div>
              <span className="text-sm font-medium">{metrics.memoryRecall}%</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Ability to recall details about shared photos
            </p>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 mb-2">Social Connections</h4>
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4 text-gray-600" />
              <span className="text-lg font-semibold">{metrics.socialConnections}</span>
              <span className="text-sm text-gray-600">people mentioned</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Number of different people discussed in conversations
            </p>
          </div>
        </div>
      </Card>

      {/* Top Conversation Topics */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Popular Topics</h3>
        
        <div className="space-y-3">
          {metrics.topTopics.map((topic, index) => (
            <motion.div
              key={topic}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-primary-600">{index + 1}</span>
                </div>
                <span className="font-medium text-gray-900">{topic}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-16 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-primary-500 h-2 rounded-full"
                    style={{ width: `${Math.max(20, 100 - index * 15)}%` }}
                  />
                </div>
                <span className="text-sm text-gray-600">{Math.max(20, 100 - index * 15)}%</span>
              </div>
            </motion.div>
          ))}
        </div>
      </Card>

      {/* Recent Conversation Highlights */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Highlights</h3>
        
        <div className="space-y-4">
          {photos.slice(0, 3).map((photo, index) => (
            <motion.div
              key={photo.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg"
            >
              <img
                src={photo.url}
                alt={photo.filename}
                className="w-16 h-16 object-cover rounded-lg"
              />
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{photo.filename}</h4>
                <p className="text-sm text-gray-600 mt-1">
                  {photo.description || 'Photo shared for discussion'}
                </p>
                <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                  <span className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {format(photo.uploadedAt, 'MMM d')}
                  </span>
                  {photo.metadata?.location && (
                    <span className="flex items-center gap-1">
                      <MapPin className="w-3 h-3" />
                      {photo.metadata.location.city}
                    </span>
                  )}
                  {photo.metadata?.people && photo.metadata.people.length > 0 && (
                    <span className="flex items-center gap-1">
                      <Users className="w-3 h-3" />
                      {photo.metadata.people.length} people
                    </span>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </Card>
    </div>
  );
};
