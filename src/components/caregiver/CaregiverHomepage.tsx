import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Users, 
  Activity, 
  MessageCircle, 
  Camera, 
  Heart, 
  TrendingUp, 
  Clock, 
  AlertTriangle,
  CheckCircle,
  Calendar,
  Settings,
  Bell,
  BarChart3,
  Upload,
  Eye,
  Pill,
  Shield,
  Zap
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import type { Photo, Medication, MedicationReminder, CaregiverInsight } from '../../types';

interface CaregiverHomepageProps {
  photos: Photo[];
  medications: Medication[];
  reminders: MedicationReminder[];
  insights: CaregiverInsight[];
  patientName?: string;
  onNavigate: (tab: string) => void;
}

export const CaregiverHomepage: React.FC<CaregiverHomepageProps> = ({
  photos,
  medications,
  reminders,
  insights,
  patientName = 'Patient',
  onNavigate,
}) => {
  const [timeRange, setTimeRange] = useState<'today' | 'week' | 'month'>('week');

  // Calculate metrics
  const getMetrics = () => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const recentPhotos = photos.filter(p => p.uploadedAt > weekAgo);
    const activeMedications = medications.filter(m => m.isActive);
    const todayReminders = reminders.filter(r => 
      r.scheduledTime.toDateString() === today.toDateString()
    );
    const missedReminders = todayReminders.filter(r => r.status === 'missed');
    const takenReminders = todayReminders.filter(r => r.status === 'taken');
    
    const adherenceRate = todayReminders.length > 0 
      ? Math.round((takenReminders.length / todayReminders.length) * 100)
      : 100;

    const photoEngagement = photos.filter(p => 
      p.metadata?.conversationCount && p.metadata.conversationCount > 0
    ).length;

    const engagementRate = photos.length > 0 
      ? Math.round((photoEngagement / photos.length) * 100)
      : 0;

    return {
      totalPhotos: photos.length,
      recentPhotos: recentPhotos.length,
      activeMedications: activeMedications.length,
      todayReminders: todayReminders.length,
      missedReminders: missedReminders.length,
      adherenceRate,
      engagementRate,
      photoEngagement
    };
  };

  const metrics = getMetrics();

  const getStatusColor = (value: number, type: 'adherence' | 'engagement') => {
    if (type === 'adherence') {
      if (value >= 90) return 'text-green-600';
      if (value >= 70) return 'text-yellow-600';
      return 'text-red-600';
    } else {
      if (value >= 50) return 'text-green-600';
      if (value >= 30) return 'text-yellow-600';
      return 'text-red-600';
    }
  };

  const quickActions = [
    {
      title: 'Upload Photos',
      description: 'Add new memories to spark conversations',
      icon: Upload,
      color: 'bg-blue-500 hover:bg-blue-600',
      action: () => onNavigate('photos'),
      badge: metrics.recentPhotos === 0 ? 'Recommended' : null
    },
    {
      title: 'Photo Library',
      description: 'Manage and organize photos',
      icon: Eye,
      color: 'bg-green-500 hover:bg-green-600',
      action: () => onNavigate('photos'),
      badge: `${metrics.totalPhotos} photos`
    },
    {
      title: 'Chat Settings',
      description: 'Configure AI behavior and notifications',
      icon: Settings,
      color: 'bg-purple-500 hover:bg-purple-600',
      action: () => onNavigate('chat'),
      badge: 'Configure'
    },
    {
      title: 'Medications',
      description: 'Monitor adherence and reminders',
      icon: Pill,
      color: 'bg-orange-500 hover:bg-orange-600',
      action: () => onNavigate('medications'),
      badge: metrics.missedReminders > 0 ? `${metrics.missedReminders} missed` : null
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 px-4 py-8">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Welcome Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Shield className="w-6 h-6 text-white" />
            </div>
            <div className="text-left">
              <h1 className="text-2xl font-bold text-gray-900">
                Caregiver Portal
              </h1>
              <p className="text-gray-600">
                Caring for {patientName}
              </p>
            </div>
          </div>
          
          <p className="text-lg text-gray-700 max-w-2xl mx-auto">
            Monitor engagement, manage care settings, and support your loved one's memory journey
          </p>
        </motion.div>

        {/* Status Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-3">
                  <MessageCircle className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900">{metrics.engagementRate}%</h3>
                <p className="text-gray-600">Photo Engagement</p>
                <div className={`text-sm ${getStatusColor(metrics.engagementRate, 'engagement')} mt-1`}>
                  {metrics.photoEngagement} of {metrics.totalPhotos} photos discussed
                </div>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-3">
                  <Pill className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900">{metrics.adherenceRate}%</h3>
                <p className="text-gray-600">Medication Adherence</p>
                <div className={`text-sm ${getStatusColor(metrics.adherenceRate, 'adherence')} mt-1`}>
                  {metrics.todayReminders - metrics.missedReminders} of {metrics.todayReminders} taken today
                </div>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-3">
                  <Camera className="w-8 h-8 text-purple-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900">{metrics.recentPhotos}</h3>
                <p className="text-gray-600">Recent Photos</p>
                <div className="text-sm text-gray-500 mt-1">
                  Added this week
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <h2 className="text-xl font-semibold text-gray-900 mb-6 text-center">
            Quick Actions
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action, index) => (
              <motion.div
                key={action.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Card 
                  className="relative p-6 text-center cursor-pointer group hover:shadow-lg transition-all duration-200"
                  onClick={action.action}
                >
                  {action.badge && (
                    <div className="absolute -top-2 -right-2 px-2 py-1 bg-red-500 text-white text-xs rounded-full">
                      {action.badge}
                    </div>
                  )}
                  
                  <div className={`w-16 h-16 ${action.color} rounded-2xl flex items-center justify-center mx-auto mb-4 transition-colors duration-200`}>
                    <action.icon className="w-8 h-8 text-white" />
                  </div>
                  
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {action.title}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {action.description}
                  </p>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Alerts & Recommendations */}
        {(metrics.missedReminders > 0 || metrics.engagementRate < 30 || metrics.recentPhotos === 0) && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Alerts & Recommendations
            </h2>
            <div className="space-y-3">
              {metrics.missedReminders > 0 && (
                <Card className="p-4 border-l-4 border-red-500 bg-red-50">
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="w-5 h-5 text-red-600" />
                    <div className="flex-1">
                      <h4 className="font-medium text-red-800">
                        Missed Medications Today
                      </h4>
                      <p className="text-sm text-red-700">
                        {metrics.missedReminders} medication{metrics.missedReminders > 1 ? 's' : ''} missed. 
                        Consider checking in with {patientName}.
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onNavigate('medications')}
                    >
                      View
                    </Button>
                  </div>
                </Card>
              )}
              
              {metrics.engagementRate < 30 && (
                <Card className="p-4 border-l-4 border-yellow-500 bg-yellow-50">
                  <div className="flex items-center gap-3">
                    <Camera className="w-5 h-5 text-yellow-600" />
                    <div className="flex-1">
                      <h4 className="font-medium text-yellow-800">
                        Low Photo Engagement
                      </h4>
                      <p className="text-sm text-yellow-700">
                        Consider uploading more family photos or photos from special events to encourage conversations.
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onNavigate('photos')}
                    >
                      Upload
                    </Button>
                  </div>
                </Card>
              )}
              
              {metrics.recentPhotos === 0 && (
                <Card className="p-4 border-l-4 border-blue-500 bg-blue-50">
                  <div className="flex items-center gap-3">
                    <Upload className="w-5 h-5 text-blue-600" />
                    <div className="flex-1">
                      <h4 className="font-medium text-blue-800">
                        No Recent Photos
                      </h4>
                      <p className="text-sm text-blue-700">
                        Upload some recent photos to keep conversations fresh and engaging.
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onNavigate('photos')}
                    >
                      Add Photos
                    </Button>
                  </div>
                </Card>
              )}
            </div>
          </motion.div>
        )}

        {/* Care Tips */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="p-6 bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0">
                <Heart className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Memory Care Tips
                </h3>
                <div className="space-y-2 text-sm text-gray-700">
                  <p>• Photos with people and events tend to generate more meaningful conversations</p>
                  <p>• Regular photo discussions can help maintain cognitive engagement</p>
                  <p>• Consistent medication schedules support overall well-being</p>
                  <p>• Positive reinforcement during conversations encourages participation</p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};
