import React from 'react';
import { Mic, MicOff, Volume2 } from 'lucide-react';
import { Button } from './Button';

interface VoiceButtonProps {
  isListening?: boolean;
  isSpeaking?: boolean;
  onStartListening?: () => void;
  onStopListening?: () => void;
  onStopSpeaking?: () => void;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export const VoiceButton: React.FC<VoiceButtonProps> = ({
  isListening = false,
  isSpeaking = false,
  onStartListening,
  onStopListening,
  onStopSpeaking,
  disabled = false,
  size = 'lg',
}) => {
  const handleClick = () => {
    if (isSpeaking) {
      onStopSpeaking?.();
    } else if (isListening) {
      onStopListening?.();
    } else {
      onStartListening?.();
    }
  };

  const getIcon = () => {
    if (isSpeaking) return Volume2;
    if (isListening) return MicOff;
    return Mic;
  };

  const getVariant = () => {
    if (isSpeaking) return 'secondary' as const;
    if (isListening) return 'danger' as const;
    return 'primary' as const;
  };

  const getButtonContent = () => {
    if (isSpeaking) return 'Stop Speaking';
    if (isListening) return 'Stop Listening';
    return 'Tap to Speak';
  };

  return (
    <Button
      variant={getVariant()}
      size={size}
      icon={getIcon()}
      onClick={handleClick}
      disabled={disabled}
      className={`
        relative
        ${isListening ? 'animate-pulse-soft' : ''}
        ${isSpeaking ? 'bg-warm-500 hover:bg-warm-600' : ''}
      `}
    >
      {getButtonContent()}
      {isListening && (
        <span className="absolute -top-1 -right-1 w-3 h-3 bg-error-500 rounded-full animate-ping" />
      )}
    </Button>
  );
};