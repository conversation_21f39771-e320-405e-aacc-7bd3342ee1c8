import React from 'react';
import { Globe } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Button } from './Button';

export const LanguageSelector: React.FC = () => {
  const { i18n } = useTranslation();

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'fr', name: 'Français', flag: '🇫🇷' },
    { code: 'ar', name: 'العربية', flag: '🇸🇦' },
  ];

  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0];

  const handleLanguageChange = (languageCode: string) => {
    i18n.changeLanguage(languageCode);
    // Update document direction for RTL languages
    document.dir = languageCode === 'ar' ? 'rtl' : 'ltr';
  };

  return (
    <div className="relative group">
      <Button
        variant="ghost"
        size="sm"
        icon={Globe}
        className="flex items-center gap-2"
      >
        <span className="text-lg">{currentLanguage.flag}</span>
        <span className="hidden sm:inline">{currentLanguage.name}</span>
      </Button>
      
      <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
        <div className="py-2">
          {languages.map((language) => (
            <button
              key={language.code}
              onClick={() => handleLanguageChange(language.code)}
              className={`
                w-full px-4 py-2 text-left flex items-center gap-3 hover:bg-gray-50 transition-colors
                ${i18n.language === language.code ? 'bg-primary-50 text-primary-700' : 'text-gray-700'}
              `}
            >
              <span className="text-lg">{language.flag}</span>
              <span>{language.name}</span>
              {i18n.language === language.code && (
                <div className="ml-auto w-2 h-2 bg-primary-500 rounded-full" />
              )}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};