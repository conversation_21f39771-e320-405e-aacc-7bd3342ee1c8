import React, { useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Upload,
  Camera,
  Image,
  Folder,
  X,
  Check,
  AlertCircle,
  FileImage,
  Smartphone,
  Download,
} from "lucide-react";
import { Button } from "../ui/Button";
import { Card } from "../ui/Card";
import { CameraCapture } from "./CameraCapture";
import {
  iOSPhotosIntegration,
  LivePhotoProcessor,
  BatchImportProcessor,
} from "../../utils/livePhotoSupport";
import toast from "react-hot-toast";

interface PhotoImportProps {
  onImport: (files: File[]) => void;
  maxFiles?: number;
  acceptedTypes?: string[];
}

interface ImportedFile {
  file: File;
  preview: string;
  id: string;
  status: "pending" | "processing" | "ready" | "error";
  error?: string;
}

export const PhotoImport: React.FC<PhotoImportProps> = ({
  onImport,
  maxFiles = 20,
  acceptedTypes = [
    "image/jpeg",
    "image/png",
    "image/heic",
    "image/heif",
    "image/webp",
  ],
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [showCamera, setShowCamera] = useState(false);
  const [importedFiles, setImportedFiles] = useState<ImportedFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  const processFiles = async (files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const validFiles = fileArray.filter((file) => {
      if (!acceptedTypes.includes(file.type)) {
        toast.error(`${file.name} is not a supported image format`);
        return false;
      }
      if (file.size > 50 * 1024 * 1024) {
        // 50MB limit
        toast.error(`${file.name} is too large (max 50MB)`);
        return false;
      }
      return true;
    });

    if (importedFiles.length + validFiles.length > maxFiles) {
      toast.error(`Maximum ${maxFiles} files allowed`);
      return;
    }

    setIsProcessing(true);

    const newImportedFiles: ImportedFile[] = [];

    for (const file of validFiles) {
      try {
        const preview = await createPreview(file);
        const importedFile: ImportedFile = {
          file,
          preview,
          id: `${Date.now()}-${Math.random()}`,
          status: "ready",
        };
        newImportedFiles.push(importedFile);
      } catch (error) {
        console.error("Error processing file:", error);
        const importedFile: ImportedFile = {
          file,
          preview: "",
          id: `${Date.now()}-${Math.random()}`,
          status: "error",
          error: "Failed to process image",
        };
        newImportedFiles.push(importedFile);
      }
    }

    setImportedFiles((prev) => [...prev, ...newImportedFiles]);
    setIsProcessing(false);
  };

  const createPreview = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          resolve(e.target.result as string);
        } else {
          reject(new Error("Failed to read file"));
        }
      };
      reader.onerror = () => reject(new Error("Failed to read file"));
      reader.readAsDataURL(file);
    });
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      processFiles(e.target.files);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      processFiles(e.dataTransfer.files);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleCameraCapture = (file: File) => {
    processFiles([file]);
    setShowCamera(false);
  };

  const removeFile = (id: string) => {
    setImportedFiles((prev) => prev.filter((f) => f.id !== id));
  };

  const handleImport = () => {
    const readyFiles = importedFiles
      .filter((f) => f.status === "ready")
      .map((f) => f.file);

    if (readyFiles.length === 0) {
      toast.error("No files ready for import");
      return;
    }

    onImport(readyFiles);
    setImportedFiles([]);
    toast.success(`Importing ${readyFiles.length} photos...`);
  };

  const clearAll = () => {
    setImportedFiles([]);
  };

  // Check if device supports camera
  const hasCameraSupport =
    navigator.mediaDevices && navigator.mediaDevices.getUserMedia;

  // Check if device is iOS for special handling
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

  return (
    <div className="space-y-6">
      {/* Import Options */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Add Photos</h3>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Camera Capture */}
          {hasCameraSupport && (
            <Button
              variant="outline"
              className="h-24 flex-col gap-2"
              onClick={() => setShowCamera(true)}
            >
              <Camera className="w-8 h-8 text-primary-600" />
              <span className="text-sm font-medium">Take Photo</span>
              <span className="text-xs text-gray-500">Use camera</span>
            </Button>
          )}

          {/* File Browser */}
          <Button
            variant="outline"
            className="h-24 flex-col gap-2"
            onClick={() => fileInputRef.current?.click()}
          >
            <Folder className="w-8 h-8 text-blue-600" />
            <span className="text-sm font-medium">Browse Files</span>
            <span className="text-xs text-gray-500">Select from device</span>
          </Button>

          {/* iOS Photos Integration */}
          {iOSPhotosIntegration.isIOSDevice() && (
            <Button
              variant="outline"
              className="h-24 flex-col gap-2"
              onClick={async () => {
                try {
                  const files = await iOSPhotosIntegration.openPhotosApp({
                    multiple: true,
                    acceptedTypes: ["image/*", "video/*"],
                  });
                  if (files.length > 0) {
                    processFiles(files);
                  }
                } catch (error) {
                  console.error("Error opening Photos app:", error);
                  toast.error("Failed to open Photos app");
                }
              }}
            >
              <Smartphone className="w-8 h-8 text-green-600" />
              <span className="text-sm font-medium">Photos App</span>
              <span className="text-xs text-gray-500">Import from Photos</span>
            </Button>
          )}
        </div>
      </Card>

      {/* Drag & Drop Area */}
      <Card
        className={`
          border-2 border-dashed transition-all duration-200 cursor-pointer
          ${
            dragActive
              ? "border-primary-400 bg-primary-50"
              : "border-gray-300 hover:border-primary-300 hover:bg-primary-25"
          }
        `}
        padding="lg"
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
      >
        <div className="text-center">
          <div className="mx-auto w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mb-4">
            {isProcessing ? (
              <div className="animate-spin w-8 h-8 border-2 border-primary-600 border-t-transparent rounded-full" />
            ) : (
              <Upload className="w-8 h-8 text-primary-600" />
            )}
          </div>

          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {isProcessing ? "Processing Photos..." : "Drag & Drop Photos Here"}
          </h3>

          <p className="text-gray-600 mb-4">
            Or click to browse your device for photos
          </p>

          <div className="text-sm text-gray-500">
            <p>Supports: JPEG, PNG, HEIC, HEIF, WebP</p>
            <p>Max file size: 50MB • Max files: {maxFiles}</p>
          </div>
        </div>
      </Card>

      {/* Imported Files Preview */}
      {importedFiles.length > 0 && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Selected Photos ({importedFiles.length})
            </h3>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={clearAll}>
                Clear All
              </Button>
              <Button
                variant="primary"
                size="sm"
                onClick={handleImport}
                disabled={
                  importedFiles.filter((f) => f.status === "ready").length === 0
                }
              >
                Import{" "}
                {importedFiles.filter((f) => f.status === "ready").length}{" "}
                Photos
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <AnimatePresence>
              {importedFiles.map((importedFile) => (
                <motion.div
                  key={importedFile.id}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="relative group"
                >
                  <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 border-2 border-gray-200">
                    {importedFile.status === "error" ? (
                      <div className="w-full h-full flex items-center justify-center bg-red-50">
                        <AlertCircle className="w-8 h-8 text-red-500" />
                      </div>
                    ) : importedFile.preview ? (
                      <img
                        src={importedFile.preview}
                        alt={importedFile.file.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <FileImage className="w-8 h-8 text-gray-400" />
                      </div>
                    )}

                    {/* Status Indicator */}
                    <div className="absolute top-2 right-2">
                      {importedFile.status === "ready" && (
                        <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                          <Check className="w-4 h-4 text-white" />
                        </div>
                      )}
                      {importedFile.status === "error" && (
                        <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                          <AlertCircle className="w-4 h-4 text-white" />
                        </div>
                      )}
                      {importedFile.status === "processing" && (
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                          <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin" />
                        </div>
                      )}
                    </div>

                    {/* Remove Button */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removeFile(importedFile.id);
                      }}
                      className="absolute top-2 left-2 w-6 h-6 bg-black/50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <X className="w-4 h-4 text-white" />
                    </button>
                  </div>

                  {/* File Info */}
                  <div className="mt-2 text-xs text-gray-600 truncate">
                    {importedFile.file.name}
                  </div>
                  {importedFile.error && (
                    <div className="text-xs text-red-600 truncate">
                      {importedFile.error}
                    </div>
                  )}
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </Card>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={acceptedTypes.join(",")}
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Camera Modal */}
      <CameraCapture
        isOpen={showCamera}
        onCapture={handleCameraCapture}
        onClose={() => setShowCamera(false)}
      />
    </div>
  );
};
