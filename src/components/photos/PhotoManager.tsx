import React, { useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Search,
  Filter,
  Grid,
  List,
  Calendar,
  MapPin,
  Tag,
  Heart,
  MessageCircle,
  MoreVertical,
  Download,
  Share2,
  Trash2,
  Star,
  Clock,
  Users,
} from "lucide-react";
import { Button } from "../ui/Button";
import { Card } from "../ui/Card";
import { EnhancedPhotoUpload } from "./EnhancedPhotoUpload";
import { AlbumManager } from "./AlbumManager";
import { PhotoOrganizer } from "./PhotoOrganizer";
import type { Photo } from "../../types";
import toast from "react-hot-toast";

interface PhotoManagerProps {
  photos: Photo[];
  onPhotosUploaded: (photos: Photo[]) => void;
  userRole?: "patient" | "caregiver" | "admin";
  showUpload?: boolean;
}

type ViewMode = "grid" | "list";
type SortBy = "date" | "name" | "favorites";
type FilterBy = "all" | "favorites" | "recent" | "events" | "people";

export const PhotoManager: React.FC<PhotoManagerProps> = ({
  photos,
  onPhotosUploaded,
  userRole = "patient",
  showUpload = true,
}) => {
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [sortBy, setSortBy] = useState<SortBy>("date");
  const [filterBy, setFilterBy] = useState<FilterBy>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPhotos, setSelectedPhotos] = useState<Set<string>>(new Set());

  // Filter and sort photos
  const filteredAndSortedPhotos = useMemo(() => {
    let filtered = photos;

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        (photo) =>
          photo.filename.toLowerCase().includes(searchQuery.toLowerCase()) ||
          photo.description
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          photo.metadata?.keywords?.some((keyword) =>
            keyword.toLowerCase().includes(searchQuery.toLowerCase())
          )
      );
    }

    // Apply category filter
    switch (filterBy) {
      case "favorites":
        filtered = filtered.filter((photo) => photo.metadata?.isFavorite);
        break;
      case "recent":
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        filtered = filtered.filter((photo) => photo.uploadedAt > weekAgo);
        break;
      case "events":
        filtered = filtered.filter((photo) => photo.metadata?.event);
        break;
      case "people":
        filtered = filtered.filter((photo) => photo.metadata?.people?.length);
        break;
    }

    // Apply sorting
    switch (sortBy) {
      case "date":
        filtered.sort(
          (a, b) => b.uploadedAt.getTime() - a.uploadedAt.getTime()
        );
        break;
      case "name":
        filtered.sort((a, b) => a.filename.localeCompare(b.filename));
        break;
      case "favorites":
        filtered.sort((a, b) => {
          const aFav = a.metadata?.isFavorite ? 1 : 0;
          const bFav = b.metadata?.isFavorite ? 1 : 0;
          return bFav - aFav;
        });
        break;
    }

    return filtered;
  }, [photos, searchQuery, filterBy, sortBy]);

  const handlePhotoAction = (photoId: string, action: string) => {
    switch (action) {
      case "chat":
        window.dispatchEvent(
          new CustomEvent("navigate-to-chat", {
            detail: {
              photoId,
              photoUrl: photos.find((p) => p.id === photoId)?.url,
            },
          })
        );
        break;
      case "favorite":
        toast.success("Added to favorites!");
        break;
      case "share":
        if (navigator.share) {
          const photo = photos.find((p) => p.id === photoId);
          navigator.share({
            title: photo?.filename,
            text: photo?.description,
            url: photo?.url,
          });
        } else {
          toast.info("Share feature not available");
        }
        break;
      case "download":
        const photo = photos.find((p) => p.id === photoId);
        if (photo) {
          const link = document.createElement("a");
          link.href = photo.url;
          link.download = photo.filename;
          link.click();
        }
        break;
      case "delete":
        toast.error("Delete functionality coming soon");
        break;
    }
  };

  const togglePhotoSelection = (photoId: string) => {
    const newSelection = new Set(selectedPhotos);
    if (newSelection.has(photoId)) {
      newSelection.delete(photoId);
    } else {
      newSelection.add(photoId);
    }
    setSelectedPhotos(newSelection);
  };

  const getFilterStats = () => {
    const recent = photos.filter((p) => {
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return p.uploadedAt > weekAgo;
    }).length;

    const favorites = photos.filter((p) => p.metadata?.isFavorite).length;
    const withEvents = photos.filter((p) => p.metadata?.event).length;
    const withPeople = photos.filter((p) => p.metadata?.people?.length).length;

    return { recent, favorites, withEvents, withPeople };
  };

  const stats = getFilterStats();

  return (
    <div className="space-y-6">
      {/* Upload Section */}
      {showUpload && (
        <EnhancedPhotoUpload
          onPhotosUploaded={onPhotosUploaded}
          existingPhotos={photos}
          userRole={userRole}
        />
      )}

      {/* Photo Management Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            Photo Library ({photos.length})
          </h2>

          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === "grid" ? "primary" : "ghost"}
              size="sm"
              icon={Grid}
              onClick={() => setViewMode("grid")}
            />
            <Button
              variant={viewMode === "list" ? "primary" : "ghost"}
              size="sm"
              icon={List}
              onClick={() => setViewMode("list")}
            />
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search photos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          <div className="flex gap-2">
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value as FilterBy)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">All Photos ({photos.length})</option>
              <option value="recent">Recent ({stats.recent})</option>
              <option value="favorites">Favorites ({stats.favorites})</option>
              <option value="events">Events ({stats.withEvents})</option>
              <option value="people">People ({stats.withPeople})</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as SortBy)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
            >
              <option value="date">Sort by Date</option>
              <option value="name">Sort by Name</option>
              <option value="favorites">Sort by Favorites</option>
            </select>
          </div>
        </div>

        {/* Quick Stats for Caregivers */}
        {userRole === "caregiver" && (
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <Card className="text-center p-3">
              <Clock className="w-5 h-5 text-blue-500 mx-auto mb-1" />
              <div className="text-lg font-semibold">{stats.recent}</div>
              <div className="text-xs text-gray-600">This Week</div>
            </Card>
            <Card className="text-center p-3">
              <Heart className="w-5 h-5 text-red-500 mx-auto mb-1" />
              <div className="text-lg font-semibold">{stats.favorites}</div>
              <div className="text-xs text-gray-600">Favorites</div>
            </Card>
            <Card className="text-center p-3">
              <Calendar className="w-5 h-5 text-green-500 mx-auto mb-1" />
              <div className="text-lg font-semibold">{stats.withEvents}</div>
              <div className="text-xs text-gray-600">Events</div>
            </Card>
            <Card className="text-center p-3">
              <Users className="w-5 h-5 text-purple-500 mx-auto mb-1" />
              <div className="text-lg font-semibold">{stats.withPeople}</div>
              <div className="text-xs text-gray-600">With People</div>
            </Card>
          </div>
        )}
      </div>

      {/* Enhanced Photo Display with Organizer */}
      <PhotoOrganizer
        photos={photos}
        viewMode={viewMode}
        onPhotoSelect={(photo) => handlePhotoAction(photo.id, "chat")}
        onPhotosUpdate={onPhotosUploaded}
      />
    </div>
  );
};

// Photo Card Component
interface PhotoCardProps {
  photo: Photo;
  viewMode: ViewMode;
  userRole: string;
  isSelected: boolean;
  onAction: (photoId: string, action: string) => void;
  onToggleSelection: (photoId: string) => void;
}

const PhotoCard: React.FC<PhotoCardProps> = ({
  photo,
  viewMode,
  userRole,
  isSelected,
  onAction,
  onToggleSelection,
}) => {
  const [showActions, setShowActions] = useState(false);

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={{ duration: 0.2 }}
    >
      <Card
        className={`relative group overflow-hidden ${
          isSelected ? "ring-2 ring-primary-500" : ""
        } ${viewMode === "list" ? "flex items-center gap-4" : ""}`}
        onMouseEnter={() => setShowActions(true)}
        onMouseLeave={() => setShowActions(false)}
      >
        {/* Photo Display */}
        <div
          className={`${
            viewMode === "grid" ? "aspect-square" : "w-20 h-20 flex-shrink-0"
          } rounded-lg overflow-hidden bg-gray-100`}
        >
          {photo.url ? (
            <img
              src={photo.url}
              alt={photo.filename}
              className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = "none";
                target.parentElement!.innerHTML = `
                  <div class="w-full h-full flex flex-col items-center justify-center text-gray-500">
                    <div class="text-xs text-center">${photo.filename}</div>
                    <div class="text-xs text-center mt-1">Preview not available</div>
                  </div>
                `;
              }}
            />
          ) : (
            <div className="w-full h-full flex flex-col items-center justify-center text-gray-500">
              <div className="text-xs text-center">{photo.filename}</div>
              <div className="text-xs text-center mt-1">
                Preview not available
              </div>
            </div>
          )}
        </div>

        {/* Photo Info */}
        <div className={`${viewMode === "grid" ? "mt-3" : "flex-1"} space-y-2`}>
          <h4 className="font-medium text-gray-900 truncate">
            {photo.filename}
          </h4>

          {photo.description && (
            <p className="text-sm text-gray-600 line-clamp-2">
              {photo.description}
            </p>
          )}

          {/* Metadata Tags */}
          <div className="flex flex-wrap gap-1">
            {photo.metadata?.event && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                <Calendar className="w-3 h-3" />
                {photo.metadata.event}
              </span>
            )}

            {photo.metadata?.location?.address && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
                <MapPin className="w-3 h-3" />
                {photo.metadata.location.address}
              </span>
            )}

            {photo.metadata?.people && photo.metadata.people.length > 0 && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">
                <Tag className="w-3 h-3" />
                {photo.metadata.people.join(", ")}
              </span>
            )}
          </div>

          <div className="text-xs text-gray-500">
            Uploaded {photo.uploadedAt.toLocaleDateString()}
          </div>
        </div>

        {/* Action Buttons */}
        <AnimatePresence>
          {showActions && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              className={`absolute ${
                viewMode === "grid"
                  ? "bottom-3 left-3 right-3"
                  : "right-3 top-3"
              } flex gap-1`}
            >
              <Button
                variant="primary"
                size="sm"
                icon={MessageCircle}
                onClick={() => onAction(photo.id, "chat")}
                className="flex-1"
              >
                Chat
              </Button>
              <Button
                variant="ghost"
                size="sm"
                icon={Heart}
                onClick={() => onAction(photo.id, "favorite")}
              />
              {userRole === "caregiver" && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    icon={Share2}
                    onClick={() => onAction(photo.id, "share")}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    icon={Download}
                    onClick={() => onAction(photo.id, "download")}
                  />
                </>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Selection Checkbox for Caregivers */}
        {userRole === "caregiver" && (
          <div className="absolute top-2 left-2">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={() => onToggleSelection(photo.id)}
              className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
            />
          </div>
        )}
      </Card>
    </motion.div>
  );
};
