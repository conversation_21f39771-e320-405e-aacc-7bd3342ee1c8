import React, { useRef, useState } from 'react';
import { Upload, Camera, Image, X } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import type { Photo } from '../../types';

interface PhotoUploadProps {
  onPhotosUploaded: (photos: Photo[]) => void;
  existingPhotos: Photo[];
}

export const PhotoUpload: React.FC<PhotoUploadProps> = ({
  onPhotosUploaded,
  existingPhotos,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);

  const handleFiles = async (files: FileList) => {
    setUploading(true);
    const newPhotos: Photo[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      if (file.type.startsWith('image/')) {
        const url = URL.createObjectURL(file);
        const photo: Photo = {
          id: `photo-${Date.now()}-${i}`,
          url,
          filename: file.name,
          uploadedAt: new Date(),
          description: '',
        };
        newPhotos.push(photo);
      }
    }

    onPhotosUploaded(newPhotos);
    setUploading(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    if (e.dataTransfer.files) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = () => {
    setDragActive(false);
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files);
    }
  };

  const removePhoto = (photoId: string) => {
    const updatedPhotos = existingPhotos.filter(photo => photo.id !== photoId);
    onPhotosUploaded(updatedPhotos);
  };

  return (
    <div className="space-y-6">
      <Card
        className={`
          border-2 border-dashed transition-all duration-200 cursor-pointer
          ${dragActive 
            ? 'border-primary-400 bg-primary-50' 
            : 'border-gray-300 hover:border-primary-300 hover:bg-primary-25'
          }
        `}
        padding="lg"
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
      >
        <div className="text-center">
          <div className="mx-auto w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mb-4">
            {uploading ? (
              <div className="animate-spin w-8 h-8 border-2 border-primary-600 border-t-transparent rounded-full" />
            ) : (
              <Upload className="w-8 h-8 text-primary-600" />
            )}
          </div>
          
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {uploading ? 'Uploading Photos...' : 'Add Your Precious Memories'}
          </h3>
          
          <p className="text-gray-600 mb-6">
            Drag and drop photos here, or click to browse your device
          </p>
          
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              variant="primary"
              icon={Image}
              loading={uploading}
              disabled={uploading}
            >
              Choose Photos
            </Button>
            <Button
              variant="secondary"
              icon={Camera}
              disabled={uploading}
            >
              Take Photo
            </Button>
          </div>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileInput}
          className="hidden"
        />
      </Card>

      {existingPhotos.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Your Photos ({existingPhotos.length})
          </h3>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {existingPhotos.map((photo) => (
              <div key={photo.id} className="relative group">
                <div className="aspect-square rounded-xl overflow-hidden bg-gray-100">
                  <img
                    src={photo.url}
                    alt={photo.filename}
                    className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                  />
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    removePhoto(photo.id);
                  }}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-error-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-error-600"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};