import React, { useRef, useState } from "react";
import {
  Upload,
  Camera,
  Image,
  X,
  Tag,
  MapPin,
  Calendar,
  Info,
  MessageCircle,
  Heart,
} from "lucide-react";
import { But<PERSON> } from "../ui/Button";
import { Card } from "../ui/Card";
import { PhotoImport } from "./PhotoImport";
import { useAuthStore } from "../../store/authStore";
import { db } from "../../lib/supabase";
import { PhotoMetadataExtractor } from "../../utils/photoMetadata";
import type { Photo } from "../../types";
import toast from "react-hot-toast";

interface PhotoUploadProps {
  onPhotosUploaded: (photos: Photo[]) => void;
  existingPhotos: Photo[];
  userRole?: "patient" | "caregiver" | "admin";
}

export const PhotoUpload: React.FC<PhotoUploadProps> = ({
  onPhotosUploaded,
  existingPhotos,
  userRole = "patient",
}) => {
  const [uploading, setUploading] = useState(false);
  const [showImport, setShowImport] = useState(false);
  const { user } = useAuthStore();

  const handleFiles = async (files: FileList) => {
    if (!user) {
      toast.error("Please sign in to upload photos");
      return;
    }

    console.log(`Starting upload of ${files.length} files`);
    setUploading(true);
    const newPhotos: Photo[] = [];

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        console.log(`Processing file ${i + 1}/${files.length}: ${file.name}`);

        if (file.type.startsWith("image/")) {
          toast.loading(`Processing ${file.name}...`, { id: `upload-${i}` });

          // Check for HEIC format and warn user
          if (
            file.name.toLowerCase().includes(".heic") ||
            file.type.includes("heic")
          ) {
            toast.warning(
              `${file.name} is in HEIC format. It may not display properly in browsers. Consider converting to JPG for better compatibility.`,
              {
                id: `heic-warning-${i}`,
                duration: 8000,
              }
            );
          }

          try {
            // Extract metadata from the photo
            console.log("Extracting metadata...");
            const metadata = await PhotoMetadataExtractor.extractMetadata(file);
            console.log("Metadata extracted:", metadata);

            // Generate a smart description
            const description =
              PhotoMetadataExtractor.generateDescription(metadata);
            console.log("Generated description:", description);

            // Upload to Supabase
            console.log("Uploading to Supabase...");
            const { data: photoData, error } = await db.uploadPhoto(
              file,
              user.id,
              description,
              metadata
            );

            if (error) {
              console.error("Upload error:", error);
              toast.error(`Failed to upload ${file.name}: ${error.message}`, {
                id: `upload-${i}`,
              });
              continue;
            }

            if (photoData) {
              console.log("Upload successful:", photoData);
              const photo: Photo = {
                id: photoData.id,
                url: photoData.url,
                filename: photoData.filename,
                uploadedAt: new Date(photoData.created_at),
                description: photoData.description || undefined,
                metadata: photoData.metadata as Photo["metadata"],
              };
              newPhotos.push(photo);
              toast.success(`${file.name} uploaded successfully!`, {
                id: `upload-${i}`,
              });
            }
          } catch (fileError) {
            console.error(`Error processing ${file.name}:`, fileError);
            toast.error(`Failed to process ${file.name}`, {
              id: `upload-${i}`,
            });
          }
        } else {
          console.log(`Skipping non-image file: ${file.name}`);
          toast.error(`${file.name} is not an image file`, {
            id: `upload-${i}`,
          });
        }
      }

      if (newPhotos.length > 0) {
        console.log(`Successfully uploaded ${newPhotos.length} photos`);
        onPhotosUploaded([...existingPhotos, ...newPhotos]);

        // Show summary message for multiple uploads
        if (newPhotos.length > 1) {
          toast.success(
            `🎉 Successfully uploaded ${newPhotos.length} photos! Click "Chat About This" on any photo to start a conversation.`,
            {
              duration: 6000,
            }
          );
        }
      }
    } catch (error) {
      console.error("Upload process error:", error);
      toast.error("Failed to upload photos");
    } finally {
      setUploading(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    if (e.dataTransfer.files) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = () => {
    setDragActive(false);
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files);
    }
  };

  const removePhoto = (photoId: string) => {
    const updatedPhotos = existingPhotos.filter(
      (photo) => photo.id !== photoId
    );
    onPhotosUploaded(updatedPhotos);
  };

  return (
    <div className="space-y-6">
      <Card
        className={`
          border-2 border-dashed transition-all duration-200 cursor-pointer
          ${
            dragActive
              ? "border-primary-400 bg-primary-50"
              : "border-gray-300 hover:border-primary-300 hover:bg-primary-25"
          }
        `}
        padding="lg"
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
      >
        <div className="text-center">
          <div className="mx-auto w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mb-4">
            {uploading ? (
              <div className="animate-spin w-8 h-8 border-2 border-primary-600 border-t-transparent rounded-full" />
            ) : (
              <Upload className="w-8 h-8 text-primary-600" />
            )}
          </div>

          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {uploading ? "Uploading Photos..." : "Add Your Precious Memories"}
          </h3>

          <p className="text-gray-600 mb-6">
            Drag and drop photos here, or click to browse your device
          </p>

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              variant="primary"
              icon={Image}
              loading={uploading}
              disabled={uploading}
              onClick={(e) => {
                e.stopPropagation();
                fileInputRef.current?.click();
              }}
            >
              Choose Photos
            </Button>
            <Button
              variant="secondary"
              icon={Camera}
              disabled={uploading}
              onClick={(e) => {
                e.stopPropagation();
                // TODO: Implement camera functionality
                toast.info("Camera feature coming soon!");
              }}
            >
              Take Photo
            </Button>
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileInput}
          className="hidden"
        />
      </Card>

      {existingPhotos.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Your Photos ({existingPhotos.length})
            </h3>
            <div className="text-sm text-gray-600 bg-blue-50 px-3 py-1 rounded-full">
              💬 Click "Chat About This" to start conversations
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {existingPhotos.map((photo) => (
              <Card key={photo.id} className="relative group overflow-hidden">
                <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 mb-3">
                  {photo.url ? (
                    <img
                      src={photo.url}
                      alt={photo.filename}
                      className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                      onError={(e) => {
                        console.error("Image failed to load:", photo.url);
                        // Show a placeholder for unsupported formats
                        const target = e.target as HTMLImageElement;
                        target.style.display = "none";
                        target.parentElement!.innerHTML = `
                          <div class="w-full h-full flex flex-col items-center justify-center text-gray-500">
                            <Image class="w-8 h-8 mb-2" />
                            <span class="text-xs text-center">${photo.filename}</span>
                            <span class="text-xs text-center mt-1">Preview not available</span>
                          </div>
                        `;
                      }}
                    />
                  ) : (
                    <div className="w-full h-full flex flex-col items-center justify-center text-gray-500">
                      <Image className="w-8 h-8 mb-2" />
                      <span className="text-xs text-center">
                        {photo.filename}
                      </span>
                      <span className="text-xs text-center mt-1">
                        Preview not available
                      </span>
                    </div>
                  )}
                </div>

                {/* Photo Info */}
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900 truncate">
                    {photo.filename}
                  </h4>

                  {photo.description && (
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {photo.description}
                    </p>
                  )}

                  {/* Metadata Tags */}
                  <div className="flex flex-wrap gap-1">
                    {photo.metadata?.event && (
                      <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                        <Calendar className="w-3 h-3" />
                        {photo.metadata.event}
                      </span>
                    )}

                    {photo.metadata?.location?.address && (
                      <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
                        <MapPin className="w-3 h-3" />
                        {photo.metadata.location.address}
                      </span>
                    )}

                    {photo.metadata?.people &&
                      photo.metadata.people.length > 0 && (
                        <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">
                          <Tag className="w-3 h-3" />
                          {photo.metadata.people.join(", ")}
                        </span>
                      )}
                  </div>

                  <div className="text-xs text-gray-500">
                    Uploaded {photo.uploadedAt.toLocaleDateString()}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 mt-3">
                    <Button
                      variant="primary"
                      size="sm"
                      icon={MessageCircle}
                      onClick={(e) => {
                        e.stopPropagation();
                        // Navigate to chat with this photo
                        window.dispatchEvent(
                          new CustomEvent("navigate-to-chat", {
                            detail: { photoId: photo.id, photoUrl: photo.url },
                          })
                        );
                      }}
                      className="flex-1"
                    >
                      Chat About This
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      icon={Heart}
                      onClick={(e) => {
                        e.stopPropagation();
                        // TODO: Toggle favorite
                        toast.success("Added to favorites!");
                      }}
                    >
                      ♥
                    </Button>
                  </div>
                </div>

                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    removePhoto(photo.id);
                  }}
                  className="absolute top-2 right-2 w-6 h-6 bg-error-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-error-600"
                >
                  <X className="w-4 h-4" />
                </button>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
