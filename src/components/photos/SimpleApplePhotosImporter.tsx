import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Smartphone, 
  Image,
  Info,
  CheckCircle,
  Upload,
  Users
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { iOSPhotosIntegration } from '../../utils/livePhotoSupport';
import toast from 'react-hot-toast';

interface SimpleApplePhotosImporterProps {
  onPhotosImported: (files: File[]) => void;
  onClose: () => void;
}

export const SimpleApplePhotosImporter: React.FC<SimpleApplePhotosImporterProps> = ({
  onPhotosImported,
  onClose
}) => {
  const [isImporting, setIsImporting] = useState(false);
  const [step, setStep] = useState<'intro' | 'importing' | 'complete'>('intro');

  const handleImportFromPhotos = async () => {
    setIsImporting(true);
    setStep('importing');
    
    try {
      toast.loading('Opening Apple Photos...', { id: 'import' });

      // Simple approach: just open the iOS Photos app for selection
      const files = await iOSPhotosIntegration.openPhotosApp({
        multiple: true,
        acceptedTypes: ['image/*']
      });

      if (files.length > 0) {
        toast.success(`Selected ${files.length} photos`, { id: 'import' });
        setStep('complete');
        
        // Pass the files to the parent component
        onPhotosImported(files);
        
        // Auto-close after a moment
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        toast.info('No photos selected', { id: 'import' });
        setStep('intro');
      }
    } catch (error) {
      console.error('Error importing from Apple Photos:', error);
      toast.error('Please try selecting photos manually', { id: 'import' });
      setStep('intro');
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="text-center">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Smartphone className="w-8 h-8 text-blue-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Import from Apple Photos</h2>
        <p className="text-gray-600">
          {step === 'intro' && 'Select photos from your Apple Photos library'}
          {step === 'importing' && 'Opening Apple Photos for selection...'}
          {step === 'complete' && 'Photos imported successfully!'}
        </p>
      </div>

      {/* Content based on step */}
      {step === 'intro' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* iOS Check */}
          {!iOSPhotosIntegration.isIOSDevice() && (
            <Card className="p-4 bg-yellow-50 border-yellow-200">
              <div className="flex items-start gap-3">
                <Info className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-medium text-yellow-800">iOS Device Required</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    This feature works best on iOS devices with Apple Photos.
                  </p>
                </div>
              </div>
            </Card>
          )}

          {/* Instructions */}
          <Card className="p-6">
            <h3 className="font-semibold text-gray-900 mb-4">How it works:</h3>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-medium text-blue-600">1</span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Open Apple Photos</h4>
                  <p className="text-sm text-gray-600">We'll open your Photos app for selection</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-medium text-blue-600">2</span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Select Photos</h4>
                  <p className="text-sm text-gray-600">Choose photos from any album (People, Places, Favorites, etc.)</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-medium text-blue-600">3</span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Import Complete</h4>
                  <p className="text-sm text-gray-600">Photos will be imported with their metadata preserved</p>
                </div>
              </div>
            </div>
          </Card>

          {/* Benefits */}
          <Card className="p-4 bg-green-50 border-green-200">
            <div className="flex items-start gap-3">
              <Users className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-medium text-green-800 mb-2">Perfect for People Albums</h4>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>• Import photos of family members that Apple Photos has already organized</li>
                  <li>• Face recognition data helps with conversations about specific people</li>
                  <li>• Maintains photo quality and metadata</li>
                  <li>• Works with any album type (People, Places, Favorites, etc.)</li>
                </ul>
              </div>
            </div>
          </Card>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              fullWidth
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              icon={Image}
              onClick={handleImportFromPhotos}
              disabled={isImporting}
              fullWidth
            >
              Select Photos from Apple Photos
            </Button>
          </div>
        </motion.div>
      )}

      {step === 'importing' && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center py-12"
        >
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Upload className="w-8 h-8 text-blue-600 animate-pulse" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Opening Apple Photos</h3>
          <p className="text-gray-600">Select the photos you'd like to import...</p>
        </motion.div>
      )}

      {step === 'complete' && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center py-12"
        >
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Import Complete!</h3>
          <p className="text-gray-600">Your photos are being processed and will appear shortly.</p>
        </motion.div>
      )}
    </div>
  );
};
