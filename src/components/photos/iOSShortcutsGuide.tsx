import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Smartphone, 
  Download, 
  ExternalLink, 
  CheckCircle, 
  ArrowRight,
  Users,
  MapPin,
  Heart,
  Calendar,
  Folder,
  Info
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';

interface iOSShortcutsGuideProps {
  onClose: () => void;
}

export const iOSShortcutsGuide: React.FC<iOSShortcutsGuideProps> = ({ onClose }) => {
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: 'Install iOS Shortcuts App',
      description: 'Make sure you have the Shortcuts app installed on your iPhone',
      icon: Smartphone,
      action: 'Download from App Store',
      link: 'https://apps.apple.com/app/shortcuts/id915249334'
    },
    {
      title: 'Download Memory Companion Shortcut',
      description: 'Install our custom shortcut for exporting People albums',
      icon: Download,
      action: 'Get Shortcut',
      link: 'https://www.icloud.com/shortcuts/memory-companion-export'
    },
    {
      title: 'Run the Shortcut',
      description: 'Open Shortcuts app and run "Memory Companion Export"',
      icon: Users,
      action: 'Run Shortcut',
      details: [
        'Open Shortcuts app',
        'Find "Memory Companion Export"',
        'Tap to run the shortcut',
        'Grant Photos access when prompted'
      ]
    },
    {
      title: 'Select Albums to Export',
      description: 'Choose which People albums you want to export',
      icon: Folder,
      action: 'Select Albums',
      details: [
        'People albums (Mom, Dad, Grandma, etc.)',
        'Favorites album',
        'Places albums (Home, Work, etc.)',
        'Custom albums you\'ve created'
      ]
    },
    {
      title: 'Export and Import',
      description: 'The shortcut will create organized folders ready for import',
      icon: ArrowRight,
      action: 'Complete Export',
      details: [
        'Shortcut creates organized folders',
        'Each person gets their own folder',
        'Metadata is preserved',
        'Ready to import into Memory Companion'
      ]
    }
  ];

  const albumTypes = [
    {
      name: 'People Albums',
      icon: Users,
      description: 'Face-recognized albums from Apple Photos',
      examples: ['Mom', 'Dad', 'Grandma', 'Sister', 'Best Friend'],
      color: 'bg-blue-100 text-blue-600'
    },
    {
      name: 'Places Albums',
      icon: MapPin,
      description: 'Location-based photo collections',
      examples: ['Home', 'Work', 'Vacation Spots', 'Restaurants'],
      color: 'bg-green-100 text-green-600'
    },
    {
      name: 'Favorites',
      icon: Heart,
      description: 'Your manually favorited photos',
      examples: ['Special moments', 'Best shots', 'Memorable events'],
      color: 'bg-red-100 text-red-600'
    },
    {
      name: 'Events',
      icon: Calendar,
      description: 'Time-based photo groupings',
      examples: ['Birthdays', 'Holidays', 'Trips', 'Celebrations'],
      color: 'bg-purple-100 text-purple-600'
    }
  ];

  const currentStepData = steps[currentStep];
  const StepIcon = currentStepData.icon;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">iOS Shortcuts Integration</h2>
          <p className="text-gray-600">Import your Apple Photos People albums seamlessly</p>
        </div>
        <Button variant="outline" onClick={onClose}>
          Close
        </Button>
      </div>

      {/* Progress Steps */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          {steps.map((step, index) => (
            <div key={index} className="flex items-center">
              <div
                className={`
                  w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                  ${index <= currentStep
                    ? 'bg-primary-500 text-white'
                    : 'bg-gray-200 text-gray-600'
                  }
                `}
              >
                {index < currentStep ? (
                  <CheckCircle className="w-4 h-4" />
                ) : (
                  index + 1
                )}
              </div>
              {index < steps.length - 1 && (
                <div
                  className={`
                    w-16 h-0.5 mx-2
                    ${index < currentStep ? 'bg-primary-500' : 'bg-gray-200'}
                  `}
                />
              )}
            </div>
          ))}
        </div>

        {/* Current Step */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="text-center"
        >
          <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <StepIcon className="w-8 h-8 text-primary-600" />
          </div>
          
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            {currentStepData.title}
          </h3>
          
          <p className="text-gray-600 mb-6">
            {currentStepData.description}
          </p>

          {currentStepData.link && (
            <Button
              variant="primary"
              icon={ExternalLink}
              onClick={() => window.open(currentStepData.link, '_blank')}
              className="mb-4"
            >
              {currentStepData.action}
            </Button>
          )}

          {currentStepData.details && (
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <ul className="text-left space-y-2">
                {currentStepData.details.map((detail, index) => (
                  <li key={index} className="flex items-center gap-2 text-sm text-gray-700">
                    <div className="w-1.5 h-1.5 bg-primary-500 rounded-full" />
                    {detail}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Navigation */}
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
              disabled={currentStep === 0}
            >
              Previous
            </Button>
            
            {currentStep < steps.length - 1 ? (
              <Button
                variant="primary"
                onClick={() => setCurrentStep(currentStep + 1)}
              >
                Next Step
              </Button>
            ) : (
              <Button
                variant="primary"
                onClick={onClose}
              >
                Start Importing
              </Button>
            )}
          </div>
        </motion.div>
      </Card>

      {/* Album Types Overview */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">What You Can Import</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {albumTypes.map((type) => {
            const TypeIcon = type.icon;
            
            return (
              <Card key={type.name} className="p-4">
                <div className="flex items-start gap-3">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${type.color}`}>
                    <TypeIcon className="w-5 h-5" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 mb-1">{type.name}</h4>
                    <p className="text-sm text-gray-600 mb-2">{type.description}</p>
                    <div className="flex flex-wrap gap-1">
                      {type.examples.map((example, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                        >
                          {example}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Benefits */}
      <Card className="p-4 bg-blue-50 border-blue-200">
        <div className="flex items-start gap-3">
          <Info className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-800 mb-2">Why Use iOS Shortcuts?</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Leverages Apple's advanced face recognition technology</li>
              <li>• Preserves all photo metadata and organization</li>
              <li>• Maintains privacy - no photos leave your device during recognition</li>
              <li>• Automatically organizes photos by people, places, and events</li>
              <li>• One-time setup for ongoing photo organization</li>
              <li>• Works with your existing Apple Photos library</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* Technical Note */}
      <Card className="p-4 bg-yellow-50 border-yellow-200">
        <div className="flex items-start gap-3">
          <Smartphone className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
          <div>
            <h4 className="font-medium text-yellow-800 mb-2">Technical Requirements</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• iOS 13 or later with Shortcuts app</li>
              <li>• Apple Photos with People albums set up</li>
              <li>• Sufficient iCloud storage for export process</li>
              <li>• Memory Companion app installed on same device</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};
