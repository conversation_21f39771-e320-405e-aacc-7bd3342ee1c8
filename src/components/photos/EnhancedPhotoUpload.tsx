import React, { useState } from "react";
import {
  Upload,
  Camera,
  Image,
  Tag,
  MapPin,
  Calendar,
  Info,
  MessageCircle,
  Heart,
} from "lucide-react";
import { Button } from "../ui/Button";
import { Card } from "../ui/Card";
import { PhotoImport } from "./PhotoImport";
import { useAuthStore } from "../../store/authStore";
import { db } from "../../lib/supabase";
import {
  photoMetadataService,
  type PhotoMetadata,
} from "../../services/photoMetadataService";
import type { Photo } from "../../types";
import toast from "react-hot-toast";

// Helper function to generate smart descriptions from metadata
const generateSmartDescription = (
  metadata: PhotoMetadata,
  filename: string
): string => {
  const parts: string[] = [];

  // Add date information
  if (metadata.dateTime?.taken) {
    const date = metadata.dateTime.taken;
    const dateStr = date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
    parts.push(`Photo taken on ${dateStr}`);
  }

  // Add location information
  if (metadata.location?.city && metadata.location?.state) {
    parts.push(`in ${metadata.location.city}, ${metadata.location.state}`);
  } else if (metadata.location?.city) {
    parts.push(`in ${metadata.location.city}`);
  }

  // Add time of day
  if (metadata.scene?.timeOfDay) {
    parts.push(`during the ${metadata.scene.timeOfDay}`);
  }

  // Add camera information for photography enthusiasts
  if (metadata.camera?.make && metadata.camera?.model) {
    parts.push(`using a ${metadata.camera.make} ${metadata.camera.model}`);
  }

  // Add tags as context
  if (metadata.tags && metadata.tags.length > 0) {
    const relevantTags = metadata.tags
      .filter(
        (tag) =>
          !["jpeg", "png", "heic", "landscape", "portrait"].includes(
            tag.toLowerCase()
          )
      )
      .slice(0, 3);

    if (relevantTags.length > 0) {
      parts.push(`Tagged: ${relevantTags.join(", ")}`);
    }
  }

  return parts.length > 0 ? parts.join(" ") + "." : `Photo: ${filename}`;
};

interface EnhancedPhotoUploadProps {
  onPhotosUploaded: (photos: Photo[]) => void;
  existingPhotos: Photo[];
  userRole?: "patient" | "caregiver" | "admin";
}

export const EnhancedPhotoUpload: React.FC<EnhancedPhotoUploadProps> = ({
  onPhotosUploaded,
  existingPhotos,
  userRole = "patient",
}) => {
  const [uploading, setUploading] = useState(false);
  const [showImport, setShowImport] = useState(false);
  const { user } = useAuthStore();

  const handleImport = async (files: File[]) => {
    if (!user) {
      toast.error("Please sign in to upload photos");
      return;
    }

    console.log(`Starting upload of ${files.length} files`);
    setUploading(true);
    const newPhotos: Photo[] = [];

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        console.log(`Processing file ${i + 1}/${files.length}: ${file.name}`);

        // Check for duplicates
        const isDuplicate = existingPhotos.some(
          (photo) =>
            photo.filename === file.name &&
            Math.abs(photo.uploadedAt.getTime() - Date.now()) < 60000 // Within 1 minute
        );

        if (isDuplicate) {
          console.log(`Skipping duplicate: ${file.name}`);
          toast.error(`${file.name} appears to be a duplicate`, {
            id: `upload-${i}`,
          });
          continue;
        }

        try {
          toast.loading(`Processing ${file.name}...`, { id: `upload-${i}` });

          // Check for HEIC format and warn user
          if (
            file.name.toLowerCase().includes(".heic") ||
            file.type.includes("heic")
          ) {
            toast(
              `${file.name} is in HEIC format. It may not display properly in browsers. Consider converting to JPG for better compatibility.`,
              {
                id: `heic-warning-${i}`,
                duration: 8000,
                icon: "⚠️",
              }
            );
          }

          // Extract metadata from the photo
          console.log("Extracting metadata...");
          const metadata = await photoMetadataService.extractMetadata(file);
          console.log("Metadata extracted:", metadata);

          // Generate a smart description based on metadata
          const description = generateSmartDescription(metadata, file.name);
          console.log("Generated description:", description);

          // Upload to Supabase
          console.log("Uploading to Supabase...");
          const { data: photoData, error } = await db.uploadPhoto(
            file,
            user.id,
            description,
            metadata
          );

          if (error) {
            console.error("Upload error:", error);
            toast.error(`Failed to upload ${file.name}: ${error.message}`, {
              id: `upload-${i}`,
            });
            continue;
          }

          if (photoData) {
            console.log("Upload successful:", photoData);
            const photo: Photo = {
              id: photoData.id,
              url: photoData.url,
              filename: photoData.filename,
              uploadedAt: new Date(photoData.created_at),
              description: photoData.description || undefined,
              metadata: photoData.metadata as Photo["metadata"],
            };
            newPhotos.push(photo);
            toast.success(`${file.name} uploaded successfully!`, {
              id: `upload-${i}`,
            });
          }
        } catch (fileError) {
          console.error(`Error processing ${file.name}:`, fileError);
          toast.error(`Failed to process ${file.name}`, {
            id: `upload-${i}`,
          });
        }
      }

      if (newPhotos.length > 0) {
        console.log(`Successfully uploaded ${newPhotos.length} photos`);
        onPhotosUploaded([...existingPhotos, ...newPhotos]);

        // Show summary message for multiple uploads
        if (newPhotos.length > 1) {
          toast.success(
            `Successfully uploaded ${newPhotos.length} photos! 📸`,
            {
              duration: 5000,
            }
          );
        }
      }
    } catch (error) {
      console.error("Upload process error:", error);
      toast.error("An error occurred during upload");
    } finally {
      setUploading(false);
      setShowImport(false);
    }
  };

  return (
    <div className="space-y-6">
      {showImport ? (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Import Photos
            </h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowImport(false)}
            >
              Cancel
            </Button>
          </div>
          <PhotoImport
            onImport={handleImport}
            maxFiles={20}
            acceptedTypes={[
              "image/jpeg",
              "image/png",
              "image/heic",
              "image/heif",
              "image/webp",
            ]}
          />
        </div>
      ) : (
        <Card className="p-6">
          <div className="text-center">
            <div className="mx-auto w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mb-4">
              {uploading ? (
                <div className="animate-spin w-8 h-8 border-2 border-primary-600 border-t-transparent rounded-full" />
              ) : (
                <Upload className="w-8 h-8 text-primary-600" />
              )}
            </div>

            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {uploading ? "Uploading Photos..." : "Add Your Precious Memories"}
            </h3>

            <p className="text-gray-600 mb-6">
              Share your favorite photos and let's explore the memories together
            </p>

            <Button
              variant="primary"
              icon={Image}
              loading={uploading}
              disabled={uploading}
              onClick={() => setShowImport(true)}
              className="px-8"
            >
              Add Photos
            </Button>
          </div>
        </Card>
      )}

      {/* Upload Tips */}
      {!showImport && (
        <Card className="p-4 bg-blue-50 border-blue-200">
          <div className="flex items-start gap-3">
            <Info className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-800 mb-2">
                Photo Upload Tips
              </h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Use the camera to take new photos instantly</li>
                <li>• Import multiple photos at once from your device</li>
                <li>• HEIC photos from iPhone are supported</li>
                <li>
                  • Photos are automatically organized by date and location
                </li>
                <li>
                  • Each photo gets smart descriptions for better conversations
                </li>
              </ul>
            </div>
          </div>
        </Card>
      )}

      {/* Recent Upload Summary */}
      {existingPhotos.length > 0 && !showImport && (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">Your Photo Library</h4>
              <p className="text-sm text-gray-600">
                {existingPhotos.length} photo
                {existingPhotos.length !== 1 ? "s" : ""} ready for conversations
              </p>
            </div>
            <div className="flex items-center gap-2">
              {existingPhotos.slice(0, 3).map((photo, index) => (
                <div
                  key={photo.id}
                  className="w-10 h-10 rounded-lg overflow-hidden bg-gray-100 border-2 border-white shadow-sm"
                  style={{ marginLeft: index > 0 ? "-8px" : "0" }}
                >
                  <img
                    src={photo.url}
                    alt={photo.filename}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
              {existingPhotos.length > 3 && (
                <div className="w-10 h-10 rounded-lg bg-gray-200 border-2 border-white shadow-sm flex items-center justify-center text-xs font-medium text-gray-600 ml-[-8px]">
                  +{existingPhotos.length - 3}
                </div>
              )}
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};
