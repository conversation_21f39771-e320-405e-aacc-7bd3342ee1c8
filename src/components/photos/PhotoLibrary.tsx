import React, { useState } from "react";
import { motion } from "framer-motion";
import { Image, Upload, Folder, BarChart3, Grid3X3, List } from "lucide-react";
import { Button } from "../ui/Button";
import { Card } from "../ui/Card";
import { PhotoManager } from "./PhotoManager";
import { AlbumManager } from "./AlbumManager";
import { EnhancedPhotoUpload } from "./EnhancedPhotoUpload";
import { SimpleApplePhotosImporter } from "./SimpleApplePhotosImporter";
import type { Photo } from "../../types";

interface PhotoLibraryProps {
  photos: Photo[];
  onPhotosUploaded: (photos: Photo[]) => void;
  userRole?: "patient" | "caregiver" | "admin";
}

type ActiveTab = "photos" | "albums" | "upload" | "insights";

export const PhotoLibrary: React.FC<PhotoLibraryProps> = ({
  photos,
  onPhotosUploaded,
  userRole = "patient",
}) => {
  const [activeTab, setActiveTab] = useState<ActiveTab>("photos");
  const [showAppleImporter, setShowAppleImporter] = useState(false);

  const tabs = [
    {
      id: "photos" as const,
      label: "Photos",
      icon: Image,
      count: photos.length,
    },
    {
      id: "albums" as const,
      label: "Albums",
      icon: Folder,
      count: null,
    },
    {
      id: "upload" as const,
      label: "Upload",
      icon: Upload,
      count: null,
    },
  ];

  // Add insights tab for caregivers
  if (userRole === "caregiver") {
    tabs.push({
      id: "insights" as const,
      label: "Insights",
      icon: BarChart3,
      count: null,
    });
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case "photos":
        return (
          <PhotoManager
            photos={photos}
            onPhotosUploaded={onPhotosUploaded}
            userRole={userRole}
            showUpload={false} // We have a separate upload tab
          />
        );

      case "albums":
        return (
          <AlbumManager photos={photos} onPhotosUpdated={onPhotosUploaded} />
        );

      case "upload":
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Upload Photos
              </h2>
              <p className="text-gray-600">
                Add new photos to your memory collection
              </p>
            </div>
            <EnhancedPhotoUpload
              onPhotosUploaded={onPhotosUploaded}
              existingPhotos={photos}
              userRole={userRole}
            />

            {/* Apple Photos Import Option */}
            <Card className="p-6 bg-blue-50 border-blue-200">
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Import from Apple Photos
                </h3>
                <p className="text-gray-600 mb-4">
                  Import photos from your Apple Photos library, including People
                  albums
                </p>
                <Button
                  variant="primary"
                  icon={Upload}
                  onClick={() => setShowAppleImporter(true)}
                >
                  Import from Apple Photos
                </Button>
              </div>
            </Card>
          </div>
        );

      case "insights":
        return (
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Photo Insights
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {photos.length}
                </div>
                <div className="text-sm text-gray-600">Total Photos</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {
                    photos.filter((p) => {
                      const weekAgo = new Date();
                      weekAgo.setDate(weekAgo.getDate() - 7);
                      return p.uploadedAt > weekAgo;
                    }).length
                  }
                </div>
                <div className="text-sm text-gray-600">This Week</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">
                  {photos.filter((p) => p.metadata?.isFavorite).length}
                </div>
                <div className="text-sm text-gray-600">Favorites</div>
              </div>
            </div>

            <div className="mt-8">
              <h4 className="font-medium text-gray-900 mb-4">
                Recent Activity
              </h4>
              <div className="space-y-3">
                {photos.slice(0, 5).map((photo) => (
                  <div
                    key={photo.id}
                    className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-200">
                      <img
                        src={photo.url}
                        alt={photo.filename}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1">
                      <h5 className="font-medium text-gray-900">
                        {photo.filename}
                      </h5>
                      <p className="text-sm text-gray-500">
                        Uploaded {photo.uploadedAt.toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Photo Library</h1>
          <p className="text-gray-600">Manage your precious memories</p>
        </div>
      </div>

      {/* Navigation Tabs */}
      <Card className="p-1">
        <nav className="flex space-x-1">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;

            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center gap-2 px-4 py-3 rounded-lg font-medium text-sm transition-all
                  ${
                    isActive
                      ? "bg-primary-100 text-primary-700 shadow-sm"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                  }
                `}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
                {tab.count !== null && (
                  <span
                    className={`
                    px-2 py-0.5 text-xs rounded-full
                    ${
                      isActive
                        ? "bg-primary-200 text-primary-800"
                        : "bg-gray-200 text-gray-600"
                    }
                  `}
                  >
                    {tab.count}
                  </span>
                )}
              </button>
            );
          })}
        </nav>
      </Card>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2 }}
      >
        {renderTabContent()}
      </motion.div>

      {/* Quick Actions (Floating) */}
      {activeTab !== "upload" && (
        <div className="fixed bottom-6 right-6 z-40">
          <div className="flex flex-col gap-3">
            {activeTab !== "albums" && (
              <Button
                variant="primary"
                icon={Folder}
                onClick={() => setActiveTab("albums")}
                className="shadow-lg"
              >
                Albums
              </Button>
            )}
            <Button
              variant="primary"
              icon={Upload}
              onClick={() => setActiveTab("upload")}
              className="shadow-lg"
            >
              Upload
            </Button>
          </div>
        </div>
      )}

      {/* Apple Photos Importer Modal */}
      {showAppleImporter && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden"
          >
            <SimpleApplePhotosImporter
              onPhotosImported={(files) => {
                // Convert files to photos and add them
                console.log("Imported photos from Apple Photos:", files);
                setShowAppleImporter(false);

                // Here you would typically process the files and add them to the photo library
                // For now, just show a success message
                toast.success(
                  `Imported ${files.length} photos from Apple Photos!`
                );
              }}
              onClose={() => setShowAppleImporter(false)}
            />
          </motion.div>
        </div>
      )}
    </div>
  );
};
