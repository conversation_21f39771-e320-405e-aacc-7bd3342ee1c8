import React, { useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Camera, 
  X, 
  RotateCcw, 
  Check, 
  FlashOff, 
  Flash, 
  SwitchCamera,
  Download,
  Repeat
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import toast from 'react-hot-toast';

interface CameraCaptureProps {
  onCapture: (file: File) => void;
  onClose: () => void;
  isOpen: boolean;
}

export const CameraCapture: React.FC<CameraCaptureProps> = ({
  onCapture,
  onClose,
  isOpen
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  
  const [isStreaming, setIsStreaming] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [facingMode, setFacingMode] = useState<'user' | 'environment'>('environment');
  const [flashMode, setFlashMode] = useState<'off' | 'on'>('off');
  const [isLoading, setIsLoading] = useState(false);

  const startCamera = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Stop any existing stream
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }

      const constraints: MediaStreamConstraints = {
        video: {
          facingMode: facingMode,
          width: { ideal: 1920 },
          height: { ideal: 1080 },
          aspectRatio: { ideal: 16/9 }
        },
        audio: false
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      streamRef.current = stream;

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
        setIsStreaming(true);
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      toast.error('Unable to access camera. Please check permissions.');
    } finally {
      setIsLoading(false);
    }
  }, [facingMode]);

  const stopCamera = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setIsStreaming(false);
  }, []);

  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Apply flash effect if enabled
    if (flashMode === 'on') {
      // Create flash overlay
      const flashOverlay = document.createElement('div');
      flashOverlay.style.position = 'fixed';
      flashOverlay.style.top = '0';
      flashOverlay.style.left = '0';
      flashOverlay.style.width = '100%';
      flashOverlay.style.height = '100%';
      flashOverlay.style.backgroundColor = 'white';
      flashOverlay.style.zIndex = '9999';
      flashOverlay.style.opacity = '0.8';
      document.body.appendChild(flashOverlay);

      setTimeout(() => {
        document.body.removeChild(flashOverlay);
      }, 100);
    }

    // Draw video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert to data URL
    const imageDataUrl = canvas.toDataURL('image/jpeg', 0.9);
    setCapturedImage(imageDataUrl);
    stopCamera();
  }, [flashMode, stopCamera]);

  const retakePhoto = useCallback(() => {
    setCapturedImage(null);
    startCamera();
  }, [startCamera]);

  const confirmCapture = useCallback(() => {
    if (!capturedImage) return;

    // Convert data URL to File
    fetch(capturedImage)
      .then(res => res.blob())
      .then(blob => {
        const file = new File([blob], `camera-${Date.now()}.jpg`, { type: 'image/jpeg' });
        onCapture(file);
        setCapturedImage(null);
        onClose();
      })
      .catch(error => {
        console.error('Error converting image:', error);
        toast.error('Failed to process captured image');
      });
  }, [capturedImage, onCapture, onClose]);

  const switchCamera = useCallback(() => {
    setFacingMode(prev => prev === 'user' ? 'environment' : 'user');
  }, []);

  const toggleFlash = useCallback(() => {
    setFlashMode(prev => prev === 'off' ? 'on' : 'off');
  }, []);

  // Start camera when component opens
  React.useEffect(() => {
    if (isOpen && !capturedImage) {
      startCamera();
    }
    
    return () => {
      stopCamera();
    };
  }, [isOpen, capturedImage, startCamera, stopCamera]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black z-50 flex flex-col"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-black/50 text-white">
          <Button
            variant="ghost"
            size="sm"
            icon={X}
            onClick={onClose}
            className="text-white hover:bg-white/20"
          />
          
          <h2 className="text-lg font-semibold">Take Photo</h2>
          
          <div className="flex items-center gap-2">
            {/* Flash Toggle */}
            <Button
              variant="ghost"
              size="sm"
              icon={flashMode === 'on' ? Flash : FlashOff}
              onClick={toggleFlash}
              className={`text-white hover:bg-white/20 ${
                flashMode === 'on' ? 'bg-yellow-500/30' : ''
              }`}
            />
            
            {/* Camera Switch */}
            {!capturedImage && (
              <Button
                variant="ghost"
                size="sm"
                icon={SwitchCamera}
                onClick={switchCamera}
                className="text-white hover:bg-white/20"
              />
            )}
          </div>
        </div>

        {/* Camera View */}
        <div className="flex-1 relative overflow-hidden">
          {capturedImage ? (
            /* Captured Image Preview */
            <div className="w-full h-full flex items-center justify-center bg-black">
              <img
                src={capturedImage}
                alt="Captured"
                className="max-w-full max-h-full object-contain"
              />
            </div>
          ) : (
            /* Live Camera Feed */
            <div className="w-full h-full relative">
              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-black">
                  <div className="text-white text-center">
                    <div className="animate-spin w-8 h-8 border-2 border-white border-t-transparent rounded-full mx-auto mb-2"></div>
                    <p>Starting camera...</p>
                  </div>
                </div>
              )}
              
              <video
                ref={videoRef}
                className="w-full h-full object-cover"
                playsInline
                muted
                autoPlay
              />
              
              {/* Camera Overlay */}
              <div className="absolute inset-0 pointer-events-none">
                {/* Grid Lines */}
                <div className="absolute inset-0 grid grid-cols-3 grid-rows-3 opacity-30">
                  {Array.from({ length: 9 }).map((_, i) => (
                    <div key={i} className="border border-white/50"></div>
                  ))}
                </div>
                
                {/* Center Focus Circle */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="w-20 h-20 border-2 border-white rounded-full opacity-50"></div>
                </div>
              </div>
            </div>
          )}
          
          {/* Hidden Canvas for Capture */}
          <canvas ref={canvasRef} className="hidden" />
        </div>

        {/* Controls */}
        <div className="p-6 bg-black/50">
          {capturedImage ? (
            /* Captured Image Controls */
            <div className="flex items-center justify-center gap-6">
              <Button
                variant="secondary"
                icon={Repeat}
                onClick={retakePhoto}
                className="bg-white/20 text-white border-white/30 hover:bg-white/30"
              >
                Retake
              </Button>
              
              <Button
                variant="primary"
                icon={Check}
                onClick={confirmCapture}
                className="bg-green-600 hover:bg-green-700 px-8"
              >
                Use Photo
              </Button>
            </div>
          ) : (
            /* Camera Controls */
            <div className="flex items-center justify-center">
              <motion.button
                whileTap={{ scale: 0.9 }}
                onClick={capturePhoto}
                disabled={!isStreaming}
                className="w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <div className="w-16 h-16 bg-white border-4 border-gray-300 rounded-full flex items-center justify-center">
                  <Camera className="w-8 h-8 text-gray-600" />
                </div>
              </motion.button>
            </div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
