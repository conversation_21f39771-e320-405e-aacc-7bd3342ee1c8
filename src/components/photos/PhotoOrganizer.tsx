import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, 
  Filter, 
  Calendar, 
  MapPin, 
  Tag, 
  Users, 
  Camera,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  X,
  Star,
  Heart
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import type { Photo } from '../../types';
import { format, startOfYear, endOfYear, startOfMonth, endOfMonth } from 'date-fns';

interface PhotoOrganizerProps {
  photos: Photo[];
  onPhotoSelect?: (photo: Photo) => void;
  onPhotosUpdate?: (photos: Photo[]) => void;
  viewMode?: 'grid' | 'list';
}

interface FilterState {
  searchQuery: string;
  dateRange: 'all' | 'thisYear' | 'thisMonth' | 'lastMonth' | 'custom';
  location: string;
  tags: string[];
  people: string[];
  camera: string;
  favorites: boolean;
}

type SortOption = 'date-desc' | 'date-asc' | 'name-asc' | 'name-desc' | 'size-desc';

export const PhotoOrganizer: React.FC<PhotoOrganizerProps> = ({
  photos,
  onPhotoSelect,
  onPhotosUpdate,
  viewMode = 'grid'
}) => {
  const [filters, setFilters] = useState<FilterState>({
    searchQuery: '',
    dateRange: 'all',
    location: '',
    tags: [],
    people: [],
    camera: '',
    favorites: false
  });
  
  const [sortBy, setSortBy] = useState<SortOption>('date-desc');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedPhotos, setSelectedPhotos] = useState<Set<string>>(new Set());

  // Extract unique values for filter options
  const filterOptions = useMemo(() => {
    const locations = new Set<string>();
    const tags = new Set<string>();
    const people = new Set<string>();
    const cameras = new Set<string>();

    photos.forEach(photo => {
      if (photo.metadata?.location?.city) {
        locations.add(photo.metadata.location.city);
      }
      if (photo.metadata?.tags) {
        photo.metadata.tags.forEach(tag => tags.add(tag));
      }
      if (photo.metadata?.people) {
        photo.metadata.people.forEach(person => people.add(person));
      }
      if (photo.metadata?.camera?.make && photo.metadata?.camera?.model) {
        cameras.add(`${photo.metadata.camera.make} ${photo.metadata.camera.model}`);
      }
    });

    return {
      locations: Array.from(locations).sort(),
      tags: Array.from(tags).sort(),
      people: Array.from(people).sort(),
      cameras: Array.from(cameras).sort()
    };
  }, [photos]);

  // Filter and sort photos
  const filteredAndSortedPhotos = useMemo(() => {
    let filtered = [...photos];

    // Apply search filter
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      filtered = filtered.filter(photo =>
        photo.filename.toLowerCase().includes(query) ||
        photo.description?.toLowerCase().includes(query) ||
        photo.metadata?.tags?.some(tag => tag.toLowerCase().includes(query)) ||
        photo.metadata?.people?.some(person => person.toLowerCase().includes(query)) ||
        photo.metadata?.location?.city?.toLowerCase().includes(query)
      );
    }

    // Apply date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      let startDate: Date;
      let endDate: Date;

      switch (filters.dateRange) {
        case 'thisYear':
          startDate = startOfYear(now);
          endDate = endOfYear(now);
          break;
        case 'thisMonth':
          startDate = startOfMonth(now);
          endDate = endOfMonth(now);
          break;
        case 'lastMonth':
          const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
          startDate = startOfMonth(lastMonth);
          endDate = endOfMonth(lastMonth);
          break;
        default:
          startDate = new Date(0);
          endDate = new Date();
      }

      filtered = filtered.filter(photo => {
        const photoDate = photo.metadata?.dateTime?.taken || photo.uploadedAt;
        return photoDate >= startDate && photoDate <= endDate;
      });
    }

    // Apply location filter
    if (filters.location) {
      filtered = filtered.filter(photo =>
        photo.metadata?.location?.city === filters.location
      );
    }

    // Apply tags filter
    if (filters.tags.length > 0) {
      filtered = filtered.filter(photo =>
        filters.tags.every(tag =>
          photo.metadata?.tags?.includes(tag)
        )
      );
    }

    // Apply people filter
    if (filters.people.length > 0) {
      filtered = filtered.filter(photo =>
        filters.people.every(person =>
          photo.metadata?.people?.includes(person)
        )
      );
    }

    // Apply camera filter
    if (filters.camera) {
      filtered = filtered.filter(photo => {
        const photoCamera = photo.metadata?.camera?.make && photo.metadata?.camera?.model
          ? `${photo.metadata.camera.make} ${photo.metadata.camera.model}`
          : '';
        return photoCamera === filters.camera;
      });
    }

    // Apply favorites filter
    if (filters.favorites) {
      filtered = filtered.filter(photo => photo.metadata?.isFavorite);
    }

    // Sort photos
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date-desc':
          const dateA = a.metadata?.dateTime?.taken || a.uploadedAt;
          const dateB = b.metadata?.dateTime?.taken || b.uploadedAt;
          return dateB.getTime() - dateA.getTime();
        case 'date-asc':
          const dateA2 = a.metadata?.dateTime?.taken || a.uploadedAt;
          const dateB2 = b.metadata?.dateTime?.taken || b.uploadedAt;
          return dateA2.getTime() - dateB2.getTime();
        case 'name-asc':
          return a.filename.localeCompare(b.filename);
        case 'name-desc':
          return b.filename.localeCompare(a.filename);
        case 'size-desc':
          const sizeA = a.metadata?.fileInfo?.size || 0;
          const sizeB = b.metadata?.fileInfo?.size || 0;
          return sizeB - sizeA;
        default:
          return 0;
      }
    });

    return filtered;
  }, [photos, filters, sortBy]);

  const handleFilterChange = (key: keyof FilterState, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      searchQuery: '',
      dateRange: 'all',
      location: '',
      tags: [],
      people: [],
      camera: '',
      favorites: false
    });
  };

  const togglePhotoSelection = (photoId: string) => {
    setSelectedPhotos(prev => {
      const newSet = new Set(prev);
      if (newSet.has(photoId)) {
        newSet.delete(photoId);
      } else {
        newSet.add(photoId);
      }
      return newSet;
    });
  };

  const toggleFavorite = (photo: Photo) => {
    if (onPhotosUpdate) {
      const updatedPhotos = photos.map(p =>
        p.id === photo.id
          ? {
              ...p,
              metadata: {
                ...p.metadata,
                isFavorite: !p.metadata?.isFavorite
              }
            }
          : p
      );
      onPhotosUpdate(updatedPhotos);
    }
  };

  const activeFilterCount = Object.values(filters).filter(value => {
    if (typeof value === 'string') return value !== '' && value !== 'all';
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'boolean') return value;
    return false;
  }).length;

  return (
    <div className="space-y-6">
      {/* Search and Filter Bar */}
      <Card className="p-4">
        <div className="flex items-center gap-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search photos by name, description, tags, or people..."
              value={filters.searchQuery}
              onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
            />
          </div>

          {/* Filter Toggle */}
          <Button
            variant={showFilters ? 'primary' : 'outline'}
            icon={Filter}
            onClick={() => setShowFilters(!showFilters)}
            className="relative"
          >
            Filters
            {activeFilterCount > 0 && (
              <span className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                {activeFilterCount}
              </span>
            )}
          </Button>

          {/* Sort */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as SortOption)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
          >
            <option value="date-desc">Newest First</option>
            <option value="date-asc">Oldest First</option>
            <option value="name-asc">Name A-Z</option>
            <option value="name-desc">Name Z-A</option>
            <option value="size-desc">Largest First</option>
          </select>
        </div>

        {/* Advanced Filters */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 pt-4 border-t border-gray-200"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Date Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date Range
                  </label>
                  <select
                    value={filters.dateRange}
                    onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                  >
                    <option value="all">All Time</option>
                    <option value="thisYear">This Year</option>
                    <option value="thisMonth">This Month</option>
                    <option value="lastMonth">Last Month</option>
                  </select>
                </div>

                {/* Location */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Location
                  </label>
                  <select
                    value={filters.location}
                    onChange={(e) => handleFilterChange('location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                  >
                    <option value="">All Locations</option>
                    {filterOptions.locations.map(location => (
                      <option key={location} value={location}>{location}</option>
                    ))}
                  </select>
                </div>

                {/* Camera */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Camera
                  </label>
                  <select
                    value={filters.camera}
                    onChange={(e) => handleFilterChange('camera', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                  >
                    <option value="">All Cameras</option>
                    {filterOptions.cameras.map(camera => (
                      <option key={camera} value={camera}>{camera}</option>
                    ))}
                  </select>
                </div>

                {/* Favorites */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Favorites
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.favorites}
                      onChange={(e) => handleFilterChange('favorites', e.target.checked)}
                      className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Show only favorites</span>
                  </label>
                </div>
              </div>

              {/* Clear Filters */}
              {activeFilterCount > 0 && (
                <div className="mt-4 flex justify-end">
                  <Button variant="outline" size="sm" onClick={clearFilters}>
                    Clear All Filters
                  </Button>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-gray-600">
          {filteredAndSortedPhotos.length} of {photos.length} photos
          {selectedPhotos.size > 0 && ` • ${selectedPhotos.size} selected`}
        </p>
        
        {selectedPhotos.size > 0 && (
          <div className="flex items-center gap-2">
            <Button size="sm" variant="outline">
              Add to Album
            </Button>
            <Button size="sm" variant="outline">
              Download
            </Button>
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => setSelectedPhotos(new Set())}
            >
              Clear Selection
            </Button>
          </div>
        )}
      </div>

      {/* Photo Grid */}
      <div className={`
        grid gap-4
        ${viewMode === 'grid' 
          ? 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5' 
          : 'grid-cols-1'
        }
      `}>
        <AnimatePresence>
          {filteredAndSortedPhotos.map((photo, index) => (
            <motion.div
              key={photo.id}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ delay: index * 0.02 }}
              className={`
                relative group cursor-pointer
                ${viewMode === 'grid' ? 'aspect-square' : 'aspect-video'}
              `}
              onClick={() => onPhotoSelect?.(photo)}
            >
              <img
                src={photo.url}
                alt={photo.filename}
                className="w-full h-full object-cover rounded-lg"
              />
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 rounded-lg transition-all" />
              
              {/* Selection Checkbox */}
              <div className="absolute top-2 left-2">
                <input
                  type="checkbox"
                  checked={selectedPhotos.has(photo.id)}
                  onChange={(e) => {
                    e.stopPropagation();
                    togglePhotoSelection(photo.id);
                  }}
                  className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
                />
              </div>

              {/* Favorite Button */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleFavorite(photo);
                }}
                className="absolute top-2 right-2 p-1 rounded-full bg-black bg-opacity-50 text-white opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <Heart 
                  className={`w-4 h-4 ${photo.metadata?.isFavorite ? 'fill-current text-red-500' : ''}`} 
                />
              </button>

              {/* Photo Info */}
              <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/60 to-transparent rounded-b-lg opacity-0 group-hover:opacity-100 transition-opacity">
                <p className="text-white text-sm font-medium truncate">
                  {photo.filename}
                </p>
                {photo.metadata?.location?.city && (
                  <p className="text-white/80 text-xs truncate">
                    <MapPin className="w-3 h-3 inline mr-1" />
                    {photo.metadata.location.city}
                  </p>
                )}
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      {filteredAndSortedPhotos.length === 0 && (
        <Card className="p-12 text-center">
          <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Photos Found</h3>
          <p className="text-gray-600 mb-4">
            Try adjusting your search terms or filters to find more photos.
          </p>
          {activeFilterCount > 0 && (
            <Button variant="outline" onClick={clearFilters}>
              Clear All Filters
            </Button>
          )}
        </Card>
      )}
    </div>
  );
};
