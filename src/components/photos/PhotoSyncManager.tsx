import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Cloud, 
  Download, 
  Upload, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle,
  Wifi,
  WifiOff,
  HardDrive,
  Smartphone,
  Settings,
  Shield,
  Clock
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { useAuthStore } from '../../store/authStore';
import { supabase } from '../../lib/supabase';
import type { Photo } from '../../types';
import toast from 'react-hot-toast';

interface PhotoSyncManagerProps {
  photos: Photo[];
  onPhotosUpdated: (photos: Photo[]) => void;
}

interface SyncStatus {
  isOnline: boolean;
  lastSync: Date | null;
  pendingUploads: number;
  pendingDownloads: number;
  totalStorage: number;
  usedStorage: number;
  autoSync: boolean;
  syncInProgress: boolean;
}

export const PhotoSyncManager: React.FC<PhotoSyncManagerProps> = ({
  photos,
  onPhotosUpdated
}) => {
  const { user } = useAuthStore();
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isOnline: navigator.onLine,
    lastSync: null,
    pendingUploads: 0,
    pendingDownloads: 0,
    totalStorage: 5 * 1024 * 1024 * 1024, // 5GB default
    usedStorage: 0,
    autoSync: true,
    syncInProgress: false
  });
  const [showSettings, setShowSettings] = useState(false);

  useEffect(() => {
    // Monitor online status
    const handleOnline = () => setSyncStatus(prev => ({ ...prev, isOnline: true }));
    const handleOffline = () => setSyncStatus(prev => ({ ...prev, isOnline: false }));

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Load sync settings
    loadSyncSettings();

    // Auto-sync if enabled and online
    if (syncStatus.autoSync && syncStatus.isOnline) {
      performAutoSync();
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const loadSyncSettings = async () => {
    try {
      const settings = localStorage.getItem('photoSyncSettings');
      if (settings) {
        const parsed = JSON.parse(settings);
        setSyncStatus(prev => ({
          ...prev,
          autoSync: parsed.autoSync ?? true,
          lastSync: parsed.lastSync ? new Date(parsed.lastSync) : null
        }));
      }

      // Calculate storage usage
      const storageUsed = photos.reduce((total, photo) => {
        // Estimate file size based on metadata or use default
        const estimatedSize = photo.metadata?.fileSize || 2 * 1024 * 1024; // 2MB default
        return total + estimatedSize;
      }, 0);

      setSyncStatus(prev => ({ ...prev, usedStorage: storageUsed }));
    } catch (error) {
      console.error('Error loading sync settings:', error);
    }
  };

  const performAutoSync = async () => {
    if (syncStatus.syncInProgress) return;

    setSyncStatus(prev => ({ ...prev, syncInProgress: true }));
    
    try {
      await syncPhotos();
    } catch (error) {
      console.error('Auto-sync failed:', error);
    } finally {
      setSyncStatus(prev => ({ ...prev, syncInProgress: false }));
    }
  };

  const syncPhotos = async () => {
    if (!user || !syncStatus.isOnline) {
      toast.error('Sync requires internet connection');
      return;
    }

    try {
      toast.loading('Syncing photos...', { id: 'sync' });

      // Fetch latest photos from server
      const { data: serverPhotos, error } = await supabase
        .from('photos')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Convert server data to Photo objects
      const syncedPhotos: Photo[] = serverPhotos?.map(photo => ({
        id: photo.id,
        url: photo.url,
        filename: photo.filename,
        uploadedAt: new Date(photo.created_at),
        description: photo.description || undefined,
        metadata: photo.metadata as Photo['metadata']
      })) || [];

      // Update local photos
      onPhotosUpdated(syncedPhotos);

      // Update sync status
      const now = new Date();
      setSyncStatus(prev => ({ ...prev, lastSync: now }));
      
      // Save sync timestamp
      const settings = {
        autoSync: syncStatus.autoSync,
        lastSync: now.toISOString()
      };
      localStorage.setItem('photoSyncSettings', JSON.stringify(settings));

      toast.success(`Synced ${syncedPhotos.length} photos`, { id: 'sync' });
    } catch (error) {
      console.error('Sync error:', error);
      toast.error('Sync failed', { id: 'sync' });
    }
  };

  const toggleAutoSync = () => {
    const newAutoSync = !syncStatus.autoSync;
    setSyncStatus(prev => ({ ...prev, autoSync: newAutoSync }));
    
    // Save setting
    const settings = {
      autoSync: newAutoSync,
      lastSync: syncStatus.lastSync?.toISOString()
    };
    localStorage.setItem('photoSyncSettings', JSON.stringify(settings));
    
    toast.success(`Auto-sync ${newAutoSync ? 'enabled' : 'disabled'}`);
  };

  const createBackup = async () => {
    if (!user) return;

    try {
      toast.loading('Creating backup...', { id: 'backup' });

      // Create backup metadata
      const backupData = {
        user_id: user.id,
        tenant_id: user.tenantId,
        photo_count: photos.length,
        backup_size: syncStatus.usedStorage,
        created_at: new Date().toISOString(),
        metadata: {
          app_version: '1.0.0',
          device_info: navigator.userAgent,
          photo_ids: photos.map(p => p.id)
        }
      };

      // In a real implementation, this would:
      // 1. Create a backup archive
      // 2. Upload to cloud storage
      // 3. Store backup metadata
      
      console.log('Backup data:', backupData);
      
      toast.success('Backup created successfully', { id: 'backup' });
    } catch (error) {
      console.error('Backup error:', error);
      toast.error('Backup failed', { id: 'backup' });
    }
  };

  const restoreFromBackup = async () => {
    if (!user) return;

    try {
      toast.loading('Restoring from backup...', { id: 'restore' });

      // In a real implementation, this would:
      // 1. List available backups
      // 2. Allow user to select backup
      // 3. Download and restore photos
      
      toast.success('Restore completed', { id: 'restore' });
    } catch (error) {
      console.error('Restore error:', error);
      toast.error('Restore failed', { id: 'restore' });
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const storagePercentage = (syncStatus.usedStorage / syncStatus.totalStorage) * 100;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Photo Sync & Backup</h2>
          <p className="text-gray-600">Manage your photo synchronization and backups</p>
        </div>
        <Button
          variant="outline"
          icon={Settings}
          onClick={() => setShowSettings(!showSettings)}
        >
          Settings
        </Button>
      </div>

      {/* Connection Status */}
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {syncStatus.isOnline ? (
              <Wifi className="w-5 h-5 text-green-600" />
            ) : (
              <WifiOff className="w-5 h-5 text-red-600" />
            )}
            <div>
              <h3 className="font-medium text-gray-900">
                {syncStatus.isOnline ? 'Connected' : 'Offline'}
              </h3>
              <p className="text-sm text-gray-600">
                {syncStatus.lastSync 
                  ? `Last sync: ${syncStatus.lastSync.toLocaleString()}`
                  : 'Never synced'
                }
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {syncStatus.syncInProgress && (
              <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />
            )}
            <Button
              variant="primary"
              icon={RefreshCw}
              onClick={syncPhotos}
              disabled={!syncStatus.isOnline || syncStatus.syncInProgress}
              size="sm"
            >
              Sync Now
            </Button>
          </div>
        </div>
      </Card>

      {/* Storage Usage */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <HardDrive className="w-5 h-5 text-gray-600" />
          <h3 className="font-semibold text-gray-900">Storage Usage</h3>
        </div>
        
        <div className="space-y-4">
          <div>
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Used: {formatBytes(syncStatus.usedStorage)}</span>
              <span>Total: {formatBytes(syncStatus.totalStorage)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all ${
                  storagePercentage > 90 ? 'bg-red-500' :
                  storagePercentage > 70 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(storagePercentage, 100)}%` }}
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {storagePercentage.toFixed(1)}% used
            </p>
          </div>

          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600">{photos.length}</div>
              <div className="text-xs text-gray-600">Photos</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">{syncStatus.pendingUploads}</div>
              <div className="text-xs text-gray-600">Pending Uploads</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">{syncStatus.pendingDownloads}</div>
              <div className="text-xs text-gray-600">Pending Downloads</div>
            </div>
          </div>
        </div>
      </Card>

      {/* Backup & Restore */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <Shield className="w-5 h-5 text-gray-600" />
          <h3 className="font-semibold text-gray-900">Backup & Restore</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Button
            variant="outline"
            icon={Upload}
            onClick={createBackup}
            disabled={!syncStatus.isOnline}
            fullWidth
          >
            Create Backup
          </Button>
          <Button
            variant="outline"
            icon={Download}
            onClick={restoreFromBackup}
            disabled={!syncStatus.isOnline}
            fullWidth
          >
            Restore from Backup
          </Button>
        </div>
        
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <div className="flex items-start gap-2">
            <Clock className="w-4 h-4 text-blue-600 flex-shrink-0 mt-0.5" />
            <div className="text-sm text-blue-700">
              <p className="font-medium">Automatic Backups</p>
              <p>Your photos are automatically backed up when you're connected to WiFi.</p>
            </div>
          </div>
        </div>
      </Card>

      {/* Settings Panel */}
      {showSettings && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
        >
          <Card className="p-6">
            <h3 className="font-semibold text-gray-900 mb-4">Sync Settings</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Auto-Sync</h4>
                  <p className="text-sm text-gray-600">Automatically sync photos when online</p>
                </div>
                <button
                  onClick={toggleAutoSync}
                  className={`
                    relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                    ${syncStatus.autoSync ? 'bg-primary-600' : 'bg-gray-200'}
                  `}
                >
                  <span
                    className={`
                      inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                      ${syncStatus.autoSync ? 'translate-x-6' : 'translate-x-1'}
                    `}
                  />
                </button>
              </div>

              <div className="border-t pt-4">
                <h4 className="font-medium text-gray-900 mb-2">Sync Frequency</h4>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-lg">
                  <option value="realtime">Real-time</option>
                  <option value="hourly">Every hour</option>
                  <option value="daily">Daily</option>
                  <option value="manual">Manual only</option>
                </select>
              </div>

              <div className="border-t pt-4">
                <h4 className="font-medium text-gray-900 mb-2">Backup Retention</h4>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-lg">
                  <option value="7">7 days</option>
                  <option value="30">30 days</option>
                  <option value="90">90 days</option>
                  <option value="365">1 year</option>
                </select>
              </div>
            </div>
          </Card>
        </motion.div>
      )}
    </div>
  );
};
