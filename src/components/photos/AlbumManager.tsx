import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Plus,
  Folder,
  FolderOpen,
  Image,
  MoreHorizontal,
  Edit,
  Trash2,
  Share,
  Calendar,
  MapPin,
  Users,
  Heart,
  Star,
  Grid3X3,
  List,
  Search,
  Filter,
  Download,
  Eye,
} from "lucide-react";
import { Button } from "../ui/Button";
import { Card } from "../ui/Card";
import { useAuthStore } from "../../store/authStore";
import { supabase } from "../../lib/supabase";
import type { Photo } from "../../types";
import toast from "react-hot-toast";

interface Album {
  id: string;
  name: string;
  description?: string;
  cover_photo_url?: string;
  photo_count: number;
  created_at: string;
  updated_at: string;
  user_id: string;
  tenant_id: string;
  is_smart: boolean;
  smart_criteria?: {
    date_range?: { start: string; end: string };
    location?: string;
    tags?: string[];
    people?: string[];
  };
  shared_with?: string[];
}

interface AlbumManagerProps {
  photos: Photo[];
  onPhotosUpdated: (photos: Photo[]) => void;
}

export const AlbumManager: React.FC<AlbumManagerProps> = ({
  photos,
  onPhotosUpdated,
}) => {
  const { user } = useAuthStore();
  const [albums, setAlbums] = useState<Album[]>([]);
  const [selectedAlbum, setSelectedAlbum] = useState<Album | null>(null);
  const [albumPhotos, setAlbumPhotos] = useState<Photo[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingAlbum, setEditingAlbum] = useState<Album | null>(null);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadAlbums();
    }
  }, [user]);

  const loadAlbums = async () => {
    try {
      setIsLoading(true);

      // Load user's albums
      const { data: albumsData, error } = await supabase
        .from("albums")
        .select(
          `
          *,
          album_photos(count)
        `
        )
        .eq("user_id", user?.id)
        .order("updated_at", { ascending: false });

      if (error) throw error;

      const albumsWithCounts =
        albumsData?.map((album) => ({
          ...album,
          photo_count: album.album_photos?.[0]?.count || 0,
        })) || [];

      setAlbums(albumsWithCounts);

      // Create default smart albums if they don't exist
      await createDefaultSmartAlbums();
    } catch (error) {
      console.error("Error loading albums:", error);
      toast.error("Failed to load albums");
    } finally {
      setIsLoading(false);
    }
  };

  const createDefaultSmartAlbums = async () => {
    if (!user) return;

    const defaultAlbums = [
      {
        name: "Recent Photos",
        description: "Photos from the last 30 days",
        is_smart: true,
        smart_criteria: {
          date_range: {
            start: new Date(
              Date.now() - 30 * 24 * 60 * 60 * 1000
            ).toISOString(),
            end: new Date().toISOString(),
          },
        },
      },
      {
        name: "Favorites",
        description: "Your favorite photos",
        is_smart: true,
        smart_criteria: {
          tags: ["favorite"],
        },
      },
      {
        name: "Family Events",
        description: "Special family moments",
        is_smart: true,
        smart_criteria: {
          tags: ["family", "event", "celebration"],
        },
      },
    ];

    for (const albumData of defaultAlbums) {
      // Check if album already exists
      const { data: existing } = await supabase
        .from("albums")
        .select("id")
        .eq("user_id", user.id)
        .eq("name", albumData.name)
        .single();

      if (!existing) {
        await supabase.from("albums").insert([
          {
            ...albumData,
            user_id: user.id,
            tenant_id: user.tenantId || "default",
          },
        ]);
      }
    }
  };

  const loadAlbumPhotos = async (album: Album) => {
    try {
      if (album.is_smart) {
        // Load photos based on smart criteria
        const smartPhotos = getSmartAlbumPhotos(album);
        setAlbumPhotos(smartPhotos);
      } else {
        // Load photos from album_photos table
        const { data, error } = await supabase
          .from("album_photos")
          .select(
            `
            photo_id,
            photos(*)
          `
          )
          .eq("album_id", album.id);

        if (error) throw error;

        const albumPhotosList =
          data?.map((item) => item.photos).filter(Boolean) || [];
        setAlbumPhotos(albumPhotosList as Photo[]);
      }
    } catch (error) {
      console.error("Error loading album photos:", error);
      toast.error("Failed to load album photos");
    }
  };

  const getSmartAlbumPhotos = (album: Album): Photo[] => {
    if (!album.smart_criteria) return [];

    let filteredPhotos = [...photos];

    // Filter by date range
    if (album.smart_criteria.date_range) {
      const { start, end } = album.smart_criteria.date_range;
      filteredPhotos = filteredPhotos.filter((photo) => {
        const photoDate = photo.uploadedAt.getTime();
        return (
          photoDate >= new Date(start).getTime() &&
          photoDate <= new Date(end).getTime()
        );
      });
    }

    // Filter by tags
    if (album.smart_criteria.tags) {
      filteredPhotos = filteredPhotos.filter((photo) => {
        const photoTags = photo.metadata?.tags || [];
        return album.smart_criteria!.tags!.some(
          (tag) =>
            photoTags.includes(tag) ||
            photo.description?.toLowerCase().includes(tag.toLowerCase())
        );
      });
    }

    // Filter by location
    if (album.smart_criteria.location) {
      filteredPhotos = filteredPhotos.filter((photo) => {
        const location =
          photo.metadata?.location?.city || photo.metadata?.location?.country;
        return location
          ?.toLowerCase()
          .includes(album.smart_criteria!.location!.toLowerCase());
      });
    }

    return filteredPhotos;
  };

  const handleAlbumSelect = (album: Album) => {
    setSelectedAlbum(album);
    loadAlbumPhotos(album);
  };

  const handleCreateAlbum = async (albumData: Partial<Album>) => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("albums")
        .insert([
          {
            ...albumData,
            user_id: user.id,
            tenant_id: user.tenantId || "default",
            is_smart: false,
          },
        ])
        .select()
        .single();

      if (error) throw error;

      toast.success("Album created successfully");
      loadAlbums();
      setShowCreateModal(false);
    } catch (error) {
      console.error("Error creating album:", error);
      toast.error("Failed to create album");
    }
  };

  const handleEditAlbum = async (albumId: string, updates: Partial<Album>) => {
    try {
      const { error } = await supabase
        .from("albums")
        .update(updates)
        .eq("id", albumId);

      if (error) throw error;

      toast.success("Album updated successfully");
      loadAlbums();
      setShowEditModal(false);
      setEditingAlbum(null);
    } catch (error) {
      console.error("Error updating album:", error);
      toast.error("Failed to update album");
    }
  };

  const handleDeleteAlbum = async (albumId: string) => {
    if (
      !confirm(
        "Are you sure you want to delete this album? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      // Delete album photos first
      await supabase.from("album_photos").delete().eq("album_id", albumId);

      // Delete album
      const { error } = await supabase
        .from("albums")
        .delete()
        .eq("id", albumId);

      if (error) throw error;

      toast.success("Album deleted successfully");
      loadAlbums();
      setSelectedAlbum(null);
    } catch (error) {
      console.error("Error deleting album:", error);
      toast.error("Failed to delete album");
    }
  };

  const addPhotosToAlbum = async (albumId: string, photoIds: string[]) => {
    try {
      const albumPhotosData = photoIds.map((photoId) => ({
        album_id: albumId,
        photo_id: photoId,
      }));

      const { error } = await supabase
        .from("album_photos")
        .insert(albumPhotosData);

      if (error) throw error;

      toast.success(`Added ${photoIds.length} photos to album`);
      loadAlbums();
      if (selectedAlbum) {
        loadAlbumPhotos(selectedAlbum);
      }
    } catch (error) {
      console.error("Error adding photos to album:", error);
      toast.error("Failed to add photos to album");
    }
  };

  const filteredAlbums = albums.filter(
    (album) =>
      album.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      album.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Photo Albums</h2>
          <p className="text-gray-600">
            Organize your memories into collections
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            icon={viewMode === "grid" ? List : Grid3X3}
            onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
          />
          <Button
            variant="primary"
            icon={Plus}
            onClick={() => setShowCreateModal(true)}
          >
            Create Album
          </Button>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Search albums..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Albums List */}
        <div className="lg:col-span-1">
          <Card className="p-4">
            <h3 className="font-semibold text-gray-900 mb-4">
              Albums ({filteredAlbums.length})
            </h3>
            <div className="space-y-2">
              {filteredAlbums.map((album) => (
                <motion.div
                  key={album.id}
                  whileHover={{ scale: 1.02 }}
                  className={`p-3 rounded-lg cursor-pointer transition-all ${
                    selectedAlbum?.id === album.id
                      ? "bg-primary-50 border border-primary-200"
                      : "hover:bg-gray-50 border border-transparent"
                  }`}
                  onClick={() => handleAlbumSelect(album)}
                >
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                      {album.is_smart ? (
                        <Star className="w-5 h-5 text-yellow-600" />
                      ) : (
                        <Folder className="w-5 h-5 text-blue-600" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-gray-900 truncate">
                        {album.name}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {album.photo_count} photo
                        {album.photo_count !== 1 ? "s" : ""}
                      </p>
                    </div>
                    {!album.is_smart && (
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          icon={Edit}
                          onClick={(e) => {
                            e.stopPropagation();
                            setEditingAlbum(album);
                            setShowEditModal(true);
                          }}
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          icon={Trash2}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteAlbum(album.id);
                          }}
                          className="text-red-600 hover:text-red-700"
                        />
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}

              {filteredAlbums.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  {searchQuery ? "No albums found" : "No albums yet"}
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Album Content */}
        <div className="lg:col-span-2">
          {selectedAlbum ? (
            <AlbumContent
              album={selectedAlbum}
              photos={albumPhotos}
              viewMode={viewMode}
              onAddPhotos={(photoIds) =>
                addPhotosToAlbum(selectedAlbum.id, photoIds)
              }
              allPhotos={photos}
            />
          ) : (
            <Card className="p-12 text-center">
              <FolderOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Select an Album
              </h3>
              <p className="text-gray-600">
                Choose an album from the list to view its photos
              </p>
            </Card>
          )}
        </div>
      </div>

      {/* Create Album Modal */}
      {showCreateModal && (
        <CreateAlbumModal
          onClose={() => setShowCreateModal(false)}
          onSuccess={handleCreateAlbum}
        />
      )}

      {/* Edit Album Modal */}
      {showEditModal && editingAlbum && (
        <EditAlbumModal
          album={editingAlbum}
          onClose={() => {
            setShowEditModal(false);
            setEditingAlbum(null);
          }}
          onSuccess={(updates) => handleEditAlbum(editingAlbum.id, updates)}
        />
      )}
    </div>
  );
};

// Album Content Component
interface AlbumContentProps {
  album: Album;
  photos: Photo[];
  viewMode: "grid" | "list";
  onAddPhotos: (photoIds: string[]) => void;
  allPhotos: Photo[];
}

const AlbumContent: React.FC<AlbumContentProps> = ({
  album,
  photos,
  viewMode,
  onAddPhotos,
  allPhotos,
}) => {
  const [showAddPhotos, setShowAddPhotos] = useState(false);
  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([]);

  const handleAddPhotos = () => {
    if (selectedPhotos.length > 0) {
      onAddPhotos(selectedPhotos);
      setSelectedPhotos([]);
      setShowAddPhotos(false);
    }
  };

  const availablePhotos = allPhotos.filter(
    (photo) => !photos.some((albumPhoto) => albumPhoto.id === photo.id)
  );

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{album.name}</h3>
          <p className="text-sm text-gray-600">
            {photos.length} photo{photos.length !== 1 ? "s" : ""}
            {album.is_smart && (
              <span className="ml-2 text-yellow-600">• Smart Album</span>
            )}
          </p>
        </div>
        {!album.is_smart && (
          <Button
            variant="outline"
            icon={Plus}
            onClick={() => setShowAddPhotos(true)}
          >
            Add Photos
          </Button>
        )}
      </div>

      {photos.length === 0 ? (
        <div className="text-center py-12">
          <Image className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-semibold text-gray-900 mb-2">
            No Photos Yet
          </h4>
          <p className="text-gray-600 mb-4">
            {album.is_smart
              ? "No photos match the criteria for this smart album"
              : "Add some photos to get started"}
          </p>
          {!album.is_smart && (
            <Button variant="primary" onClick={() => setShowAddPhotos(true)}>
              Add Photos
            </Button>
          )}
        </div>
      ) : (
        <div
          className={`
          ${
            viewMode === "grid"
              ? "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4"
              : "space-y-4"
          }
        `}
        >
          {photos.map((photo) => (
            <motion.div
              key={photo.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className={`
                ${
                  viewMode === "grid"
                    ? "aspect-square rounded-lg overflow-hidden bg-gray-100"
                    : "flex items-center gap-4 p-4 bg-gray-50 rounded-lg"
                }
              `}
            >
              <img
                src={photo.url}
                alt={photo.filename}
                className={`
                  ${
                    viewMode === "grid"
                      ? "w-full h-full object-cover"
                      : "w-16 h-16 object-cover rounded-lg"
                  }
                `}
              />
              {viewMode === "list" && (
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">
                    {photo.filename}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {photo.uploadedAt.toLocaleDateString()}
                  </p>
                  {photo.description && (
                    <p className="text-sm text-gray-500 mt-1">
                      {photo.description}
                    </p>
                  )}
                </div>
              )}
            </motion.div>
          ))}
        </div>
      )}

      {/* Add Photos Modal */}
      {showAddPhotos && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-lg max-w-4xl w-full max-h-[80vh] overflow-hidden"
          >
            <div className="p-6 border-b">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Add Photos to Album
                </h3>
                <Button variant="ghost" onClick={() => setShowAddPhotos(false)}>
                  ×
                </Button>
              </div>
            </div>

            <div className="p-6 overflow-y-auto max-h-96">
              <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-4">
                {availablePhotos.map((photo) => (
                  <div
                    key={photo.id}
                    className={`
                      aspect-square rounded-lg overflow-hidden cursor-pointer border-2 transition-all
                      ${
                        selectedPhotos.includes(photo.id)
                          ? "border-primary-500 ring-2 ring-primary-200"
                          : "border-gray-200 hover:border-gray-300"
                      }
                    `}
                    onClick={() => {
                      setSelectedPhotos((prev) =>
                        prev.includes(photo.id)
                          ? prev.filter((id) => id !== photo.id)
                          : [...prev, photo.id]
                      );
                    }}
                  >
                    <img
                      src={photo.url}
                      alt={photo.filename}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>

              {availablePhotos.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  All photos are already in this album
                </div>
              )}
            </div>

            <div className="p-6 border-t bg-gray-50">
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-600">
                  {selectedPhotos.length} photo
                  {selectedPhotos.length !== 1 ? "s" : ""} selected
                </p>
                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    onClick={() => setShowAddPhotos(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleAddPhotos}
                    disabled={selectedPhotos.length === 0}
                  >
                    Add {selectedPhotos.length} Photo
                    {selectedPhotos.length !== 1 ? "s" : ""}
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </Card>
  );
};

// Create Album Modal Component
interface CreateAlbumModalProps {
  onClose: () => void;
  onSuccess: (albumData: Partial<Album>) => void;
}

const CreateAlbumModal: React.FC<CreateAlbumModalProps> = ({
  onClose,
  onSuccess,
}) => {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.name.trim()) {
      onSuccess(formData);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white rounded-lg max-w-md w-full p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Create New Album
        </h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Album Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
              placeholder="Enter album name"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description (Optional)
            </label>
            <textarea
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
              placeholder="Describe this album"
              rows={3}
            />
          </div>

          <div className="flex gap-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose} fullWidth>
              Cancel
            </Button>
            <Button type="submit" variant="primary" fullWidth>
              Create Album
            </Button>
          </div>
        </form>
      </motion.div>
    </div>
  );
};

// Edit Album Modal Component
interface EditAlbumModalProps {
  album: Album;
  onClose: () => void;
  onSuccess: (updates: Partial<Album>) => void;
}

const EditAlbumModal: React.FC<EditAlbumModalProps> = ({
  album,
  onClose,
  onSuccess,
}) => {
  const [formData, setFormData] = useState({
    name: album.name,
    description: album.description || "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSuccess(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white rounded-lg max-w-md w-full p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Edit Album</h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Album Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
              rows={3}
            />
          </div>

          <div className="flex gap-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose} fullWidth>
              Cancel
            </Button>
            <Button type="submit" variant="primary" fullWidth>
              Save Changes
            </Button>
          </div>
        </form>
      </motion.div>
    </div>
  );
};
