import React from "react";
import {
  Camera,
  MessageCircle,
  Pill,
  BarChart3,
  Home,
  Settings,
} from "lucide-react";
import { useAuthStore } from "../../store/authStore";

interface NavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export const Navigation: React.FC<NavigationProps> = ({
  activeTab,
  onTabChange,
}) => {
  const { user } = useAuthStore();

  const patientTabs = [
    { id: "home", label: "Home", icon: Home },
    { id: "photos", label: "Photos", icon: Camera },
    { id: "chat", label: "Chat", icon: MessageCircle },
    { id: "medications", label: "Meds", icon: Pill },
    { id: "insights", label: "Insights", icon: BarChart3 },
  ];

  const caregiverTabs = [
    { id: "home", label: "Dashboard", icon: Home },
    { id: "photos", label: "Photos", icon: Camera },
    { id: "chat", label: "Settings", icon: Settings },
    { id: "medications", label: "Meds", icon: Pill },
    { id: "insights", label: "Insights", icon: BarChart3 },
  ];

  const tabs = user?.role === "caregiver" ? caregiverTabs : patientTabs;

  return (
    <nav className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 px-4 py-2 safe-area-bottom">
      <div className="max-w-md mx-auto">
        <div className="flex justify-around">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;

            return (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                className={`
                  flex flex-col items-center justify-center py-2 px-3 rounded-xl transition-all duration-200
                  ${
                    isActive
                      ? "text-primary-600 bg-primary-50"
                      : "text-gray-500 hover:text-primary-600 hover:bg-gray-50"
                  }
                `}
              >
                <Icon
                  className={`w-6 h-6 mb-1 ${
                    isActive ? "text-primary-600" : ""
                  }`}
                />
                <span className="text-xs font-medium">{tab.label}</span>
              </button>
            );
          })}
        </div>
      </div>
    </nav>
  );
};
