import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useAuthStore } from '../../store/authStore';
import { AdminDashboard } from '../admin/AdminDashboard';
import { CaregiverDashboard } from '../caregiver/CaregiverDashboard';
import App from '../../App';

export const AppRouter: React.FC = () => {
  const { user, isAuthenticated } = useAuthStore();

  if (!isAuthenticated) {
    return <App />; // Show login/main app
  }

  return (
    <Router>
      <Routes>
        {user?.role === 'admin' && (
          <Route path="/admin/*" element={<AdminDashboard />} />
        )}
        {user?.role === 'caregiver' && (
          <Route path="/caregiver/*" element={<CaregiverDashboard />} />
        )}
        <Route path="/*" element={<App />} />
        <Route 
          path="/" 
          element={
            <Navigate 
              to={
                user?.role === 'admin' ? '/admin' :
                user?.role === 'caregiver' ? '/caregiver' :
                '/app'
              } 
              replace 
            />
          } 
        />
      </Routes>
    </Router>
  );
};