import React from 'react';
import { <PERSON>, <PERSON>u, <PERSON>ting<PERSON>, User } from 'lucide-react';
import { Button } from '../ui/Button';

interface HeaderProps {
  onMenuClick?: () => void;
  onProfileClick?: () => void;
  onSettingsClick?: () => void;
  userName?: string;
  children?: React.ReactNode;
}

export const Header: React.FC<HeaderProps> = ({
  onMenuClick,
  onProfileClick,
  onSettingsClick,
  userName = 'Friend',
  children,
}) => {
  return (
    <header className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-100 px-4 py-3">
      <div className="max-w-6xl mx-auto flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            icon={Menu}
            onClick={onMenuClick}
            className="md:hidden"
          />
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
              <Heart className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900">Memory Companion</h1>
              <p className="text-sm text-gray-500 hidden sm:block">
                Hello, {userName}
              </p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {children}
          <Button
            variant="ghost"
            size="sm"
            icon={Settings}
            onClick={onSettingsClick}
          />
          <Button
            variant="ghost"
            size="sm"
            icon={User}
            onClick={onProfileClick}
          />
        </div>
      </div>
    </header>
  );
};