import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Shield, 
  Check, 
  X, 
  Info, 
  AlertTriangle, 
  Download,
  Trash2,
  Eye,
  EyeOff,
  FileText,
  Clock,
  Globe
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { useAuthStore } from '../../store/authStore';
import { useTenantStore } from '../../store/tenantStore';
import { securityService } from '../../services/securityService';
import type { ConsentType, DataSubjectRequestType } from '../../types/security';
import toast from 'react-hot-toast';

interface ConsentItem {
  type: ConsentType;
  title: string;
  description: string;
  required: boolean;
  dataCategories: string[];
  purpose: string;
  retention: string;
  thirdParties?: string[];
}

const CONSENT_ITEMS: ConsentItem[] = [
  {
    type: 'data_processing',
    title: 'Essential Data Processing',
    description: 'Process your personal information to provide core app functionality',
    required: true,
    dataCategories: ['Personal Information', 'Conversation Data', 'Photos'],
    purpose: 'Provide memory care services, AI conversations, and photo management',
    retention: '7 years (HIPAA requirement)',
    thirdParties: []
  },
  {
    type: 'analytics',
    title: 'Analytics & Improvement',
    description: 'Analyze app usage to improve features and user experience',
    required: false,
    dataCategories: ['Usage Data', 'Device Information', 'Performance Metrics'],
    purpose: 'Improve app performance, identify popular features, and fix bugs',
    retention: '2 years',
    thirdParties: ['Google Analytics (anonymized)']
  },
  {
    type: 'marketing',
    title: 'Marketing Communications',
    description: 'Send you updates about new features and relevant health information',
    required: false,
    dataCategories: ['Email Address', 'Name', 'Preferences'],
    purpose: 'Send newsletters, feature updates, and health tips',
    retention: 'Until you unsubscribe',
    thirdParties: ['Email service provider']
  },
  {
    type: 'research',
    title: 'Medical Research',
    description: 'Use anonymized data to advance memory care research',
    required: false,
    dataCategories: ['Anonymized Medical Data', 'Conversation Patterns', 'Usage Patterns'],
    purpose: 'Contribute to Alzheimer\'s and dementia research',
    retention: '10 years (research standards)',
    thirdParties: ['Academic research institutions']
  },
  {
    type: 'third_party_sharing',
    title: 'Healthcare Provider Sharing',
    description: 'Share relevant data with your healthcare providers',
    required: false,
    dataCategories: ['Medical Information', 'Medication Data', 'Activity Patterns'],
    purpose: 'Coordinate care with your healthcare team',
    retention: 'As long as you receive care',
    thirdParties: ['Your designated healthcare providers']
  }
];

interface PrivacyConsentManagerProps {
  isOpen: boolean;
  onClose: () => void;
  onConsentComplete?: () => void;
}

export const PrivacyConsentManager: React.FC<PrivacyConsentManagerProps> = ({
  isOpen,
  onClose,
  onConsentComplete
}) => {
  const { user } = useAuthStore();
  const { currentTenant } = useTenantStore();
  const [consents, setConsents] = useState<Record<ConsentType, boolean>>({
    data_processing: false,
    analytics: false,
    marketing: false,
    research: false,
    third_party_sharing: false,
    international_transfer: false
  });
  const [expandedItem, setExpandedItem] = useState<ConsentType | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showDataRights, setShowDataRights] = useState(false);

  useEffect(() => {
    // Load existing consents
    loadExistingConsents();
  }, [user?.id]);

  const loadExistingConsents = () => {
    // In a real implementation, load from database
    const savedConsents = localStorage.getItem('privacy_consents');
    if (savedConsents) {
      try {
        const parsed = JSON.parse(savedConsents);
        const consentMap: Record<ConsentType, boolean> = {
          data_processing: false,
          analytics: false,
          marketing: false,
          research: false,
          third_party_sharing: false,
          international_transfer: false
        };
        
        parsed.forEach((consent: any) => {
          if (consent.granted && consent.userId === user?.id) {
            consentMap[consent.consentType as ConsentType] = true;
          }
        });
        
        setConsents(consentMap);
      } catch (error) {
        console.error('Error loading consents:', error);
      }
    }
  };

  const handleConsentChange = (type: ConsentType, granted: boolean) => {
    setConsents(prev => ({ ...prev, [type]: granted }));
  };

  const handleSubmit = async () => {
    if (!user?.id) return;

    // Check required consents
    if (!consents.data_processing) {
      toast.error('Essential data processing consent is required to use the app');
      return;
    }

    try {
      setIsSubmitting(true);

      // Record all consent decisions
      for (const [type, granted] of Object.entries(consents)) {
        const consentItem = CONSENT_ITEMS.find(item => item.type === type);
        if (consentItem) {
          await securityService.recordConsent(
            user.id,
            type as ConsentType,
            consentItem.purpose,
            granted,
            currentTenant?.id
          );
        }
      }

      toast.success('Privacy preferences saved successfully');
      onConsentComplete?.();
      onClose();
    } catch (error) {
      console.error('Error saving consents:', error);
      toast.error('Failed to save privacy preferences');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDataRequest = async (requestType: DataSubjectRequestType) => {
    if (!user?.id) return;

    try {
      const requestDetails = getRequestDetails(requestType);
      const requestId = await securityService.createDataSubjectRequest(
        user.id,
        requestType,
        requestDetails,
        currentTenant?.id
      );

      toast.success(`${getRequestTypeLabel(requestType)} request submitted. Request ID: ${requestId.slice(0, 8)}`);
    } catch (error) {
      console.error('Error submitting data request:', error);
      toast.error('Failed to submit data request');
    }
  };

  const getRequestDetails = (requestType: DataSubjectRequestType): string => {
    switch (requestType) {
      case 'access': return 'Request to access all personal data stored in the system';
      case 'portability': return 'Request to export personal data in a portable format';
      case 'erasure': return 'Request to delete all personal data from the system';
      case 'rectification': return 'Request to correct inaccurate personal data';
      default: return `Data subject request of type: ${requestType}`;
    }
  };

  const getRequestTypeLabel = (requestType: DataSubjectRequestType): string => {
    switch (requestType) {
      case 'access': return 'Data Access';
      case 'portability': return 'Data Export';
      case 'erasure': return 'Data Deletion';
      case 'rectification': return 'Data Correction';
      default: return requestType;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <Shield className="w-6 h-6 text-primary-600" />
            <h2 className="text-xl font-semibold text-gray-900">Privacy & Data Consent</h2>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDataRights(!showDataRights)}
            >
              Your Data Rights
            </Button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              aria-label="Close"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-200px)]">
          {showDataRights ? (
            /* Data Rights Panel */
            <div className="p-6">
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Your Data Rights</h3>
                <p className="text-gray-600">
                  Under privacy laws like GDPR and CCPA, you have the following rights regarding your personal data:
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Eye className="w-5 h-5 text-blue-600" />
                    <h4 className="font-medium text-gray-900">Access Your Data</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-4">
                    Request a copy of all personal data we have about you.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    fullWidth
                    onClick={() => handleDataRequest('access')}
                  >
                    Request Data Access
                  </Button>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Download className="w-5 h-5 text-green-600" />
                    <h4 className="font-medium text-gray-900">Export Your Data</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-4">
                    Download your data in a portable format.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    fullWidth
                    onClick={() => handleDataRequest('portability')}
                  >
                    Export Data
                  </Button>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <FileText className="w-5 h-5 text-yellow-600" />
                    <h4 className="font-medium text-gray-900">Correct Your Data</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-4">
                    Request correction of inaccurate information.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    fullWidth
                    onClick={() => handleDataRequest('rectification')}
                  >
                    Request Correction
                  </Button>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Trash2 className="w-5 h-5 text-red-600" />
                    <h4 className="font-medium text-gray-900">Delete Your Data</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-4">
                    Request deletion of your personal data.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    fullWidth
                    onClick={() => handleDataRequest('erasure')}
                    className="text-red-600 border-red-300 hover:bg-red-50"
                  >
                    Request Deletion
                  </Button>
                </Card>
              </div>

              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-start gap-3">
                  <Info className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-900 mb-1">Processing Time</h4>
                    <p className="text-sm text-blue-800">
                      We will respond to your request within 30 days. For complex requests, 
                      we may extend this period by up to 60 days with notification.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Consent Management Panel */
            <div className="p-6">
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Data Processing Consent</h3>
                <p className="text-gray-600">
                  Please review and choose your privacy preferences. You can change these settings at any time.
                </p>
              </div>

              <div className="space-y-4">
                {CONSENT_ITEMS.map((item) => (
                  <Card key={item.type} className="overflow-hidden">
                    <div className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="font-medium text-gray-900">{item.title}</h4>
                            {item.required && (
                              <span className="px-2 py-1 bg-red-100 text-red-700 text-xs font-medium rounded-full">
                                Required
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mb-3">{item.description}</p>
                          
                          <div className="flex items-center gap-4">
                            <label className="flex items-center gap-2 cursor-pointer">
                              <input
                                type="checkbox"
                                checked={consents[item.type]}
                                onChange={(e) => handleConsentChange(item.type, e.target.checked)}
                                disabled={item.required && consents[item.type]}
                                className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                              />
                              <span className="text-sm font-medium text-gray-900">
                                {consents[item.type] ? 'Granted' : 'Not granted'}
                              </span>
                            </label>
                            
                            <button
                              onClick={() => setExpandedItem(expandedItem === item.type ? null : item.type)}
                              className="text-sm text-primary-600 hover:text-primary-700"
                            >
                              {expandedItem === item.type ? 'Hide details' : 'Show details'}
                            </button>
                          </div>
                        </div>

                        <div className="ml-4">
                          {consents[item.type] ? (
                            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                              <Check className="w-4 h-4 text-green-600" />
                            </div>
                          ) : (
                            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                              <X className="w-4 h-4 text-gray-400" />
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Expanded Details */}
                      <AnimatePresence>
                        {expandedItem === item.type && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            className="mt-4 pt-4 border-t border-gray-200"
                          >
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                              <div>
                                <h5 className="font-medium text-gray-900 mb-2">Data Categories</h5>
                                <ul className="space-y-1">
                                  {item.dataCategories.map((category, index) => (
                                    <li key={index} className="text-gray-600">• {category}</li>
                                  ))}
                                </ul>
                              </div>
                              
                              <div>
                                <h5 className="font-medium text-gray-900 mb-2">Purpose</h5>
                                <p className="text-gray-600">{item.purpose}</p>
                              </div>
                              
                              <div>
                                <h5 className="font-medium text-gray-900 mb-2">Retention Period</h5>
                                <p className="text-gray-600">{item.retention}</p>
                              </div>
                              
                              {item.thirdParties && item.thirdParties.length > 0 && (
                                <div>
                                  <h5 className="font-medium text-gray-900 mb-2">Third Parties</h5>
                                  <ul className="space-y-1">
                                    {item.thirdParties.map((party, index) => (
                                      <li key={index} className="text-gray-600">• {party}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </Card>
                ))}
              </div>

              {/* HIPAA Notice */}
              <div className="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-amber-900 mb-1">HIPAA Compliance</h4>
                    <p className="text-sm text-amber-800">
                      This app handles Protected Health Information (PHI) and complies with HIPAA regulations. 
                      Your medical data is encrypted and access is logged for security purposes.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        {!showDataRights && (
          <div className="p-6 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Last updated: {new Date().toLocaleDateString()}
              </div>
              
              <div className="flex gap-3">
                <Button variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleSubmit}
                  disabled={!consents.data_processing || isSubmitting}
                >
                  {isSubmitting ? 'Saving...' : 'Save Preferences'}
                </Button>
              </div>
            </div>
          </div>
        )}
      </motion.div>
    </div>
  );
};
