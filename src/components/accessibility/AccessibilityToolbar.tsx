import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Eye, 
  Volume2, 
  VolumeX, 
  Mic, 
  MicOff, 
  Type, 
  Contrast, 
  Settings,
  X,
  HelpCircle,
  Keyboard,
  MousePointer
} from 'lucide-react';
import { Button } from '../ui/Button';
import { useAccessibility, useVoiceNavigation, useTextToSpeech, useKeyboardNavigation } from '../../contexts/AccessibilityContext';

interface AccessibilityToolbarProps {
  className?: string;
}

export const AccessibilityToolbar: React.FC<AccessibilityToolbarProps> = ({ className = '' }) => {
  const { settings, updateSettings } = useAccessibility();
  const { isListening, startListening, stopListening, isSupported: voiceSupported } = useVoiceNavigation();
  const { speak, stop, isSpeaking, isSupported: speechSupported } = useTextToSpeech();
  const [isExpanded, setIsExpanded] = useState(false);
  const [showHelp, setShowHelp] = useState(false);

  useKeyboardNavigation();

  const toggleFontSize = () => {
    const sizes = ['small', 'medium', 'large', 'extra-large'] as const;
    const currentIndex = sizes.indexOf(settings.fontSize);
    const nextIndex = (currentIndex + 1) % sizes.length;
    updateSettings({ fontSize: sizes[nextIndex] });
  };

  const toggleHighContrast = () => {
    updateSettings({ highContrast: !settings.highContrast });
  };

  const toggleVoiceNavigation = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  const toggleTextToSpeech = () => {
    if (isSpeaking) {
      stop();
    } else {
      speak('Text to speech is now enabled. I will read important information aloud.');
    }
  };

  const toggleSimplifiedInterface = () => {
    updateSettings({ simplifiedInterface: !settings.simplifiedInterface });
  };

  const toggleLargeButtons = () => {
    updateSettings({ largeButtons: !settings.largeButtons });
  };

  const getFontSizeLabel = () => {
    const labels = {
      'small': 'A',
      'medium': 'A',
      'large': 'A',
      'extra-large': 'A'
    };
    return labels[settings.fontSize];
  };

  const getFontSizeClass = () => {
    const classes = {
      'small': 'text-sm',
      'medium': 'text-base',
      'large': 'text-lg',
      'extra-large': 'text-xl'
    };
    return classes[settings.fontSize];
  };

  const helpContent = [
    {
      title: 'Font Size',
      description: 'Click the "A" button to cycle through font sizes: Small → Medium → Large → Extra Large',
      shortcut: 'Alt + F'
    },
    {
      title: 'High Contrast',
      description: 'Toggle high contrast mode for better visibility',
      shortcut: 'Alt + C'
    },
    {
      title: 'Voice Navigation',
      description: 'Say commands like "go home", "photos", "chat", "medications", or "help"',
      shortcut: 'Alt + V'
    },
    {
      title: 'Text to Speech',
      description: 'Enable to have important information read aloud',
      shortcut: 'Alt + S'
    },
    {
      title: 'Keyboard Navigation',
      description: 'Use Alt + 1-5 to navigate between sections, Alt + H for help',
      shortcut: 'Alt + 1-5'
    }
  ];

  return (
    <>
      {/* Accessibility Toolbar */}
      <div className={`fixed top-4 right-4 z-50 ${className}`}>
        <div className="bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden">
          {/* Toggle Button */}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="w-12 h-12 flex items-center justify-center bg-primary-600 text-white hover:bg-primary-700 transition-colors"
            aria-label="Toggle accessibility toolbar"
            data-tooltip="Accessibility Tools"
          >
            <Eye className="w-5 h-5" />
          </button>

          {/* Expanded Toolbar */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="bg-white border-t border-gray-200"
              >
                <div className="p-3 space-y-2">
                  {/* Font Size */}
                  <button
                    onClick={toggleFontSize}
                    className={`w-full flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors ${
                      settings.fontSize !== 'medium' ? 'bg-primary-50 text-primary-700' : 'text-gray-700'
                    }`}
                    aria-label={`Font size: ${settings.fontSize}`}
                    data-tooltip="Change font size"
                  >
                    <Type className="w-4 h-4" />
                    <span className={`font-bold ${getFontSizeClass()}`}>
                      {getFontSizeLabel()}
                    </span>
                  </button>

                  {/* High Contrast */}
                  <button
                    onClick={toggleHighContrast}
                    className={`w-full flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors ${
                      settings.highContrast ? 'bg-primary-50 text-primary-700' : 'text-gray-700'
                    }`}
                    aria-label={`High contrast: ${settings.highContrast ? 'on' : 'off'}`}
                    data-tooltip="Toggle high contrast"
                  >
                    <Contrast className="w-4 h-4" />
                    <span className="text-sm">Contrast</span>
                  </button>

                  {/* Voice Navigation */}
                  {voiceSupported && (
                    <button
                      onClick={toggleVoiceNavigation}
                      className={`w-full flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors ${
                        isListening ? 'bg-red-50 text-red-700' : 'text-gray-700'
                      }`}
                      aria-label={`Voice navigation: ${isListening ? 'listening' : 'off'}`}
                      data-tooltip="Voice commands"
                    >
                      {isListening ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
                      <span className="text-sm">Voice</span>
                    </button>
                  )}

                  {/* Text to Speech */}
                  {speechSupported && (
                    <button
                      onClick={toggleTextToSpeech}
                      className={`w-full flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors ${
                        isSpeaking ? 'bg-green-50 text-green-700' : 'text-gray-700'
                      }`}
                      aria-label={`Text to speech: ${isSpeaking ? 'speaking' : 'off'}`}
                      data-tooltip="Text to speech"
                    >
                      {isSpeaking ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
                      <span className="text-sm">Speech</span>
                    </button>
                  )}

                  {/* Large Buttons */}
                  <button
                    onClick={toggleLargeButtons}
                    className={`w-full flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors ${
                      settings.largeButtons ? 'bg-primary-50 text-primary-700' : 'text-gray-700'
                    }`}
                    aria-label={`Large buttons: ${settings.largeButtons ? 'on' : 'off'}`}
                    data-tooltip="Large touch targets"
                  >
                    <MousePointer className="w-4 h-4" />
                    <span className="text-sm">Large</span>
                  </button>

                  {/* Simplified Interface */}
                  <button
                    onClick={toggleSimplifiedInterface}
                    className={`w-full flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors ${
                      settings.simplifiedInterface ? 'bg-primary-50 text-primary-700' : 'text-gray-700'
                    }`}
                    aria-label={`Simplified interface: ${settings.simplifiedInterface ? 'on' : 'off'}`}
                    data-tooltip="Simplified interface"
                  >
                    <Settings className="w-4 h-4" />
                    <span className="text-sm">Simple</span>
                  </button>

                  {/* Help */}
                  <button
                    onClick={() => setShowHelp(true)}
                    className="w-full flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors text-gray-700"
                    aria-label="Show accessibility help"
                    data-tooltip="Accessibility help"
                  >
                    <HelpCircle className="w-4 h-4" />
                    <span className="text-sm">Help</span>
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Voice Listening Indicator */}
      {isListening && (
        <div className="voice-listening">
          <div className="flex items-center gap-2">
            <Mic className="w-4 h-4" />
            <span>Listening...</span>
          </div>
        </div>
      )}

      {/* Help Modal */}
      <AnimatePresence>
        {showHelp && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-hidden"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <div className="flex items-center gap-3">
                  <Eye className="w-6 h-6 text-primary-600" />
                  <h2 className="text-xl font-semibold text-gray-900">Accessibility Help</h2>
                </div>
                <button
                  onClick={() => setShowHelp(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  aria-label="Close help"
                >
                  <X className="w-5 h-5 text-gray-500" />
                </button>
              </div>

              {/* Content */}
              <div className="p-6 overflow-y-auto max-h-[60vh]">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Accessibility Features</h3>
                    <div className="space-y-4">
                      {helpContent.map((item, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-gray-900">{item.title}</h4>
                            <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                              {item.shortcut}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600">{item.description}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Voice Commands</h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                        <div><strong>"Go home"</strong> - Navigate to home page</div>
                        <div><strong>"Photos"</strong> - Open photo library</div>
                        <div><strong>"Chat"</strong> - Start AI conversation</div>
                        <div><strong>"Medications"</strong> - View medications</div>
                        <div><strong>"Profile"</strong> - Open profile settings</div>
                        <div><strong>"Help"</strong> - Show this help dialog</div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Keyboard Shortcuts</h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                        <div><kbd className="bg-white px-2 py-1 rounded border">Alt + 1</kbd> Home</div>
                        <div><kbd className="bg-white px-2 py-1 rounded border">Alt + 2</kbd> Photos</div>
                        <div><kbd className="bg-white px-2 py-1 rounded border">Alt + 3</kbd> Chat</div>
                        <div><kbd className="bg-white px-2 py-1 rounded border">Alt + 4</kbd> Medications</div>
                        <div><kbd className="bg-white px-2 py-1 rounded border">Alt + 5</kbd> Profile</div>
                        <div><kbd className="bg-white px-2 py-1 rounded border">Alt + H</kbd> Help</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="p-6 border-t border-gray-200">
                <Button
                  variant="primary"
                  onClick={() => setShowHelp(false)}
                  fullWidth
                >
                  Got it, thanks!
                </Button>
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>

      {/* Skip Links */}
      <a href="#main-content" className="skip-link">
        Skip to main content
      </a>

      {/* Live Region for Announcements */}
      <div
        id="live-region"
        className="live-region"
        aria-live="polite"
        aria-atomic="true"
      />

      {/* Error Announcements */}
      <div
        id="error-region"
        className="error-announcement"
        aria-live="assertive"
        aria-atomic="true"
      />
    </>
  );
};
