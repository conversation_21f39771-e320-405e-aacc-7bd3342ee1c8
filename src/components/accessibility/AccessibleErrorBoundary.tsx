import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, HelpCircle, Phone } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
}

export class AccessibleErrorBoundary extends Component<Props, State> {
  private maxRetries = 3;
  private errorAnnouncement: HTMLDivElement | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // Announce error to screen readers
    this.announceError('An error occurred. Please try refreshing the page or contact support if the problem persists.');

    // Call custom error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log error for debugging
    console.error('Error caught by boundary:', error, errorInfo);
  }

  componentDidMount() {
    // Create error announcement element
    this.errorAnnouncement = document.getElementById('error-region') as HTMLDivElement;
  }

  announceError = (message: string) => {
    if (this.errorAnnouncement) {
      this.errorAnnouncement.textContent = message;
      // Clear after announcement
      setTimeout(() => {
        if (this.errorAnnouncement) {
          this.errorAnnouncement.textContent = '';
        }
      }, 5000);
    }
  };

  handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1
      }));
      this.announceError('Retrying...');
    } else {
      this.announceError('Maximum retry attempts reached. Please refresh the page or contact support.');
    }
  };

  handleRefresh = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleContactSupport = () => {
    window.open('mailto:<EMAIL>?subject=Error Report&body=' + 
      encodeURIComponent(`Error: ${this.state.error?.message}\n\nStack: ${this.state.error?.stack}`));
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default accessible error UI
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <Card className="max-w-2xl w-full p-8 text-center">
            {/* Error Icon */}
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>

            {/* Error Message */}
            <div className="mb-8">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">
                Oops! Something went wrong
              </h1>
              <p className="text-gray-600 text-lg mb-4">
                We're sorry, but something unexpected happened. Don't worry - your data is safe.
              </p>
              
              {/* Simple error description for users */}
              <div className="bg-gray-100 rounded-lg p-4 mb-6">
                <p className="text-sm text-gray-700">
                  <strong>What happened:</strong> The app encountered an error and couldn't continue.
                </p>
                <p className="text-sm text-gray-700 mt-2">
                  <strong>What you can do:</strong> Try refreshing the page or going back to the home screen.
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              {/* Primary Actions */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {this.state.retryCount < this.maxRetries && (
                  <Button
                    variant="primary"
                    icon={RefreshCw}
                    onClick={this.handleRetry}
                    fullWidth
                  >
                    Try Again ({this.maxRetries - this.state.retryCount} attempts left)
                  </Button>
                )}
                
                <Button
                  variant="outline"
                  icon={RefreshCw}
                  onClick={this.handleRefresh}
                  fullWidth
                >
                  Refresh Page
                </Button>
              </div>

              {/* Secondary Actions */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  variant="ghost"
                  icon={Home}
                  onClick={this.handleGoHome}
                  fullWidth
                >
                  Go to Home
                </Button>
                
                <Button
                  variant="ghost"
                  icon={Phone}
                  onClick={this.handleContactSupport}
                  fullWidth
                >
                  Contact Support
                </Button>
              </div>
            </div>

            {/* Help Information */}
            <div className="mt-8 p-4 bg-blue-50 rounded-lg">
              <div className="flex items-start gap-3">
                <HelpCircle className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                <div className="text-left">
                  <h3 className="font-medium text-blue-900 mb-2">Need Help?</h3>
                  <div className="text-sm text-blue-800 space-y-1">
                    <p>• Try refreshing the page first</p>
                    <p>• Check your internet connection</p>
                    <p>• Contact support if the problem continues</p>
                    <p>• Your photos and data are safe</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Technical Details (Collapsible) */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-8 text-left">
                <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
                  Technical Details (for developers)
                </summary>
                <div className="mt-4 p-4 bg-gray-100 rounded-lg">
                  <div className="text-xs font-mono text-gray-800 space-y-2">
                    <div>
                      <strong>Error:</strong> {this.state.error.message}
                    </div>
                    <div>
                      <strong>Stack:</strong>
                      <pre className="mt-1 whitespace-pre-wrap">{this.state.error.stack}</pre>
                    </div>
                    {this.state.errorInfo && (
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="mt-1 whitespace-pre-wrap">{this.state.errorInfo.componentStack}</pre>
                      </div>
                    )}
                  </div>
                </div>
              </details>
            )}

            {/* Accessibility Information */}
            <div className="mt-6 text-xs text-gray-500">
              <p>
                This error has been announced to screen readers. 
                Use Tab to navigate between buttons or press Alt+H for help.
              </p>
            </div>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook for programmatic error handling
export const useErrorHandler = () => {
  const handleError = (error: Error, context?: string) => {
    // Announce error to screen readers
    const errorRegion = document.getElementById('error-region');
    if (errorRegion) {
      errorRegion.textContent = `Error: ${error.message}. ${context || 'Please try again.'}`;
      setTimeout(() => {
        errorRegion.textContent = '';
      }, 5000);
    }

    // Log error
    console.error('Error handled:', error, context);

    // Could integrate with error reporting service here
  };

  const handleAsyncError = async (asyncFn: () => Promise<any>, context?: string) => {
    try {
      return await asyncFn();
    } catch (error) {
      handleError(error as Error, context);
      throw error;
    }
  };

  return { handleError, handleAsyncError };
};

// Component for displaying inline errors accessibly
interface AccessibleErrorMessageProps {
  error: string | null;
  className?: string;
  id?: string;
}

export const AccessibleErrorMessage: React.FC<AccessibleErrorMessageProps> = ({
  error,
  className = '',
  id
}) => {
  if (!error) return null;

  return (
    <div
      id={id}
      role="alert"
      aria-live="polite"
      className={`flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-800 ${className}`}
    >
      <AlertTriangle className="w-4 h-4 flex-shrink-0" />
      <span className="text-sm">{error}</span>
    </div>
  );
};

// Component for success messages
interface AccessibleSuccessMessageProps {
  message: string | null;
  className?: string;
  id?: string;
}

export const AccessibleSuccessMessage: React.FC<AccessibleSuccessMessageProps> = ({
  message,
  className = '',
  id
}) => {
  if (!message) return null;

  return (
    <div
      id={id}
      role="alert"
      aria-live="polite"
      className={`flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg text-green-800 ${className}`}
    >
      <div className="w-4 h-4 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0">
        <div className="w-2 h-2 bg-white rounded-full" />
      </div>
      <span className="text-sm">{message}</span>
    </div>
  );
};
