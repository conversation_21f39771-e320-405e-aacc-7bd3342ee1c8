import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  HelpCircle, 
  X, 
  ChevronRight, 
  Search, 
  Book, 
  Video, 
  Phone,
  MessageCircle,
  ArrowLeft
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { useAccessibility, useTextToSpeech } from '../../contexts/AccessibilityContext';

interface HelpTopic {
  id: string;
  title: string;
  description: string;
  category: 'getting-started' | 'photos' | 'chat' | 'medications' | 'family' | 'accessibility';
  content: string;
  steps?: string[];
  videoUrl?: string;
  relatedTopics?: string[];
}

const HELP_TOPICS: HelpTopic[] = [
  {
    id: 'getting-started',
    title: 'Getting Started with MemoCare',
    description: 'Learn the basics of using the MemoCare app',
    category: 'getting-started',
    content: 'Welcome to MemoCare! This app helps you stay connected with your memories and family. You can share photos, have conversations with our AI companion, manage medications, and connect with your caregivers.',
    steps: [
      'Start by uploading some photos in the Photos section',
      'Try having a conversation about your photos in the Chat section',
      'Set up your medications and reminders',
      'Invite family members to join your family group',
      'Customize your profile and preferences'
    ],
    relatedTopics: ['uploading-photos', 'ai-chat', 'family-invites']
  },
  {
    id: 'uploading-photos',
    title: 'How to Upload Photos',
    description: 'Learn how to add photos to your memory collection',
    category: 'photos',
    content: 'Photos are the heart of MemoCare. They help trigger memories and provide context for conversations with our AI companion.',
    steps: [
      'Go to the Photos section using the camera icon at the bottom',
      'Tap the "+" button to add new photos',
      'Choose to take a new photo or select from your gallery',
      'Add a description or let our AI analyze the photo automatically',
      'Your photos will be organized and ready for conversations'
    ],
    relatedTopics: ['ai-chat', 'photo-organization']
  },
  {
    id: 'ai-chat',
    title: 'Talking with Your AI Companion',
    description: 'Learn how to have meaningful conversations about your memories',
    category: 'chat',
    content: 'Our AI companion is designed to help you reminisce about your photos and memories in a natural, caring way.',
    steps: [
      'Go to the Chat section using the message icon',
      'Start by saying hello or asking about a specific photo',
      'The AI will ask questions to help you share memories',
      'You can ask about people, places, or events in your photos',
      'The AI remembers your conversations for future chats'
    ],
    relatedTopics: ['uploading-photos', 'memory-prompts']
  },
  {
    id: 'medication-reminders',
    title: 'Setting Up Medication Reminders',
    description: 'Never miss a dose with smart medication tracking',
    category: 'medications',
    content: 'MemoCare helps you stay on top of your medication schedule with gentle reminders and tracking.',
    steps: [
      'Go to the Medications section using the pill icon',
      'Tap "Add Medication" to create a new reminder',
      'Enter the medication name, dosage, and schedule',
      'Set reminder times that work for your routine',
      'Mark medications as taken when you receive reminders'
    ],
    relatedTopics: ['caregiver-alerts', 'health-tracking']
  },
  {
    id: 'family-invites',
    title: 'Inviting Family Members',
    description: 'Connect with family and caregivers',
    category: 'family',
    content: 'MemoCare is designed for families. Invite your loved ones to share in your memory journey.',
    steps: [
      'Go to your Profile section',
      'Select "Family & Caregivers"',
      'Tap "Add Relationship" to invite someone',
      'Enter their email and choose their role (caregiver, family member, etc.)',
      'Set their permissions for viewing photos and medical information'
    ],
    relatedTopics: ['privacy-settings', 'caregiver-dashboard']
  },
  {
    id: 'accessibility-features',
    title: 'Using Accessibility Features',
    description: 'Customize the app for your needs',
    category: 'accessibility',
    content: 'MemoCare includes many features to make the app easier to use for everyone.',
    steps: [
      'Look for the eye icon in the top-right corner',
      'Tap it to open the accessibility toolbar',
      'Adjust font size by tapping the "A" button',
      'Enable high contrast mode for better visibility',
      'Try voice commands by enabling the microphone',
      'Use text-to-speech to have content read aloud'
    ],
    relatedTopics: ['voice-commands', 'keyboard-shortcuts']
  }
];

interface HelpSystemProps {
  isOpen: boolean;
  onClose: () => void;
  initialTopic?: string;
}

export const HelpSystem: React.FC<HelpSystemProps> = ({
  isOpen,
  onClose,
  initialTopic
}) => {
  const { settings } = useAccessibility();
  const { speak } = useTextToSpeech();
  const [currentTopic, setCurrentTopic] = useState<HelpTopic | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredTopics, setFilteredTopics] = useState(HELP_TOPICS);

  useEffect(() => {
    if (initialTopic) {
      const topic = HELP_TOPICS.find(t => t.id === initialTopic);
      if (topic) setCurrentTopic(topic);
    }
  }, [initialTopic]);

  useEffect(() => {
    if (searchTerm) {
      const filtered = HELP_TOPICS.filter(topic =>
        topic.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        topic.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        topic.content.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredTopics(filtered);
    } else {
      setFilteredTopics(HELP_TOPICS);
    }
  }, [searchTerm]);

  const handleTopicSelect = (topic: HelpTopic) => {
    setCurrentTopic(topic);
    if (settings.screenReader) {
      speak(`Opening help topic: ${topic.title}`);
    }
  };

  const handleBack = () => {
    setCurrentTopic(null);
    if (settings.screenReader) {
      speak('Returning to help topics list');
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'getting-started': return Book;
      case 'photos': return '📷';
      case 'chat': return MessageCircle;
      case 'medications': return '💊';
      case 'family': return '👥';
      case 'accessibility': return '♿';
      default: return HelpCircle;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'getting-started': return 'bg-blue-100 text-blue-700';
      case 'photos': return 'bg-green-100 text-green-700';
      case 'chat': return 'bg-purple-100 text-purple-700';
      case 'medications': return 'bg-red-100 text-red-700';
      case 'family': return 'bg-yellow-100 text-yellow-700';
      case 'accessibility': return 'bg-indigo-100 text-indigo-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            {currentTopic && (
              <button
                onClick={handleBack}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                aria-label="Back to help topics"
              >
                <ArrowLeft className="w-5 h-5 text-gray-500" />
              </button>
            )}
            <HelpCircle className="w-6 h-6 text-primary-600" />
            <h2 className="text-xl font-semibold text-gray-900">
              {currentTopic ? currentTopic.title : 'Help & Support'}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            aria-label="Close help"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-200px)]">
          {currentTopic ? (
            /* Topic Detail View */
            <div className="p-6">
              <div className="space-y-6">
                {/* Topic Header */}
                <div>
                  <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium mb-3 ${getCategoryColor(currentTopic.category)}`}>
                    <span>{currentTopic.category.replace('-', ' ')}</span>
                  </div>
                  <p className="text-gray-600 text-lg">{currentTopic.description}</p>
                </div>

                {/* Content */}
                <div className="prose max-w-none">
                  <p className="text-gray-700 leading-relaxed">{currentTopic.content}</p>
                </div>

                {/* Steps */}
                {currentTopic.steps && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Step-by-Step Guide</h3>
                    <div className="space-y-3">
                      {currentTopic.steps.map((step, index) => (
                        <div key={index} className="flex gap-4">
                          <div className="w-8 h-8 bg-primary-100 text-primary-700 rounded-full flex items-center justify-center text-sm font-semibold flex-shrink-0">
                            {index + 1}
                          </div>
                          <p className="text-gray-700 pt-1">{step}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Related Topics */}
                {currentTopic.relatedTopics && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Topics</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {currentTopic.relatedTopics.map(topicId => {
                        const relatedTopic = HELP_TOPICS.find(t => t.id === topicId);
                        if (!relatedTopic) return null;
                        
                        return (
                          <button
                            key={topicId}
                            onClick={() => handleTopicSelect(relatedTopic)}
                            className="text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                          >
                            <div className="font-medium text-gray-900">{relatedTopic.title}</div>
                            <div className="text-sm text-gray-600 mt-1">{relatedTopic.description}</div>
                          </button>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            /* Topics List View */
            <div className="p-6">
              {/* Search */}
              <div className="mb-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search help topics..."
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Quick Actions */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                <Card className="p-4 text-center hover:shadow-md transition-shadow cursor-pointer">
                  <Video className="w-8 h-8 text-primary-600 mx-auto mb-2" />
                  <h3 className="font-medium text-gray-900">Video Tutorials</h3>
                  <p className="text-sm text-gray-600 mt-1">Watch step-by-step guides</p>
                </Card>
                
                <Card className="p-4 text-center hover:shadow-md transition-shadow cursor-pointer">
                  <Phone className="w-8 h-8 text-primary-600 mx-auto mb-2" />
                  <h3 className="font-medium text-gray-900">Call Support</h3>
                  <p className="text-sm text-gray-600 mt-1">Speak with our team</p>
                </Card>
                
                <Card className="p-4 text-center hover:shadow-md transition-shadow cursor-pointer">
                  <MessageCircle className="w-8 h-8 text-primary-600 mx-auto mb-2" />
                  <h3 className="font-medium text-gray-900">Live Chat</h3>
                  <p className="text-sm text-gray-600 mt-1">Get instant help</p>
                </Card>
              </div>

              {/* Help Topics */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Help Topics</h3>
                <div className="space-y-3">
                  {filteredTopics.map((topic) => (
                    <button
                      key={topic.id}
                      onClick={() => handleTopicSelect(topic)}
                      className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors group"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <div className={`px-2 py-1 rounded text-xs font-medium ${getCategoryColor(topic.category)}`}>
                              {topic.category.replace('-', ' ')}
                            </div>
                          </div>
                          <h4 className="font-medium text-gray-900 group-hover:text-primary-700 transition-colors">
                            {topic.title}
                          </h4>
                          <p className="text-sm text-gray-600 mt-1">{topic.description}</p>
                        </div>
                        <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-primary-600 transition-colors" />
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {filteredTopics.length === 0 && (
                <div className="text-center py-8">
                  <HelpCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="font-medium text-gray-900 mb-2">No topics found</h3>
                  <p className="text-gray-600">Try searching with different keywords</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Need more help? Contact our support team at{' '}
              <a href="mailto:<EMAIL>" className="text-primary-600 hover:text-primary-700">
                <EMAIL>
              </a>
            </div>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};
