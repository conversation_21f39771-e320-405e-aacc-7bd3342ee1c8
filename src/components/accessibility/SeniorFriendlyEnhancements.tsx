import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Clock, 
  Calendar, 
  Sun, 
  Moon, 
  CloudRain, 
  Thermometer,
  Heart,
  Phone,
  MessageCircle,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { useAccessibility } from '../../contexts/AccessibilityContext';

interface SeniorFriendlyEnhancementsProps {
  className?: string;
}

export const SeniorFriendlyEnhancements: React.FC<SeniorFriendlyEnhancementsProps> = ({
  className = ''
}) => {
  const { settings } = useAccessibility();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [weather, setWeather] = useState<any>(null);
  const [showTimeReminder, setShowTimeReminder] = useState(false);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Show time reminder every hour for users with memory challenges
  useEffect(() => {
    if (settings.clearInstructions) {
      const reminderTimer = setInterval(() => {
        setShowTimeReminder(true);
        setTimeout(() => setShowTimeReminder(false), 5000);
      }, 3600000); // Every hour

      return () => clearInterval(reminderTimer);
    }
  }, [settings.clearInstructions]);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString([], { 
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getTimeOfDayGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  const getTimeOfDayIcon = () => {
    const hour = currentTime.getHours();
    if (hour >= 6 && hour < 18) return Sun;
    return Moon;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Time and Date Display */}
      <Card className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-3 mb-2">
              {React.createElement(getTimeOfDayIcon(), { 
                className: "w-6 h-6 text-blue-600" 
              })}
              <h2 className="text-2xl font-bold text-gray-900">
                {getTimeOfDayGreeting()}!
              </h2>
            </div>
            <div className="space-y-1">
              <div className="text-3xl font-bold text-blue-900">
                {formatTime(currentTime)}
              </div>
              <div className="text-lg text-blue-700">
                {formatDate(currentTime)}
              </div>
            </div>
          </div>
          
          <div className="text-right">
            <Clock className="w-12 h-12 text-blue-400 mx-auto mb-2" />
            <div className="text-sm text-blue-600">
              Current Time
            </div>
          </div>
        </div>
      </Card>

      {/* Time Reminder Notification */}
      <AnimatePresence>
        {showTimeReminder && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="fixed top-20 left-1/2 transform -translate-x-1/2 z-40"
          >
            <Card className="p-4 bg-yellow-50 border-yellow-200 shadow-lg">
              <div className="flex items-center gap-3">
                <Clock className="w-6 h-6 text-yellow-600" />
                <div>
                  <div className="font-medium text-yellow-900">
                    Time Check: {formatTime(currentTime)}
                  </div>
                  <div className="text-sm text-yellow-700">
                    {formatDate(currentTime)}
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Quick Actions for Seniors */}
      {settings.simplifiedInterface && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              variant="outline"
              icon={Phone}
              className="h-16 text-lg"
              fullWidth
            >
              Call Family
            </Button>
            
            <Button
              variant="outline"
              icon={MessageCircle}
              className="h-16 text-lg"
              fullWidth
            >
              Send Message
            </Button>
            
            <Button
              variant="outline"
              icon={Heart}
              className="h-16 text-lg"
              fullWidth
            >
              Emergency
            </Button>
            
            <Button
              variant="outline"
              icon={Calendar}
              className="h-16 text-lg"
              fullWidth
            >
              Today's Schedule
            </Button>
          </div>
        </Card>
      )}

      {/* Helpful Reminders */}
      {settings.clearInstructions && (
        <Card className="p-6 bg-green-50 border-green-200">
          <div className="flex items-start gap-3">
            <Info className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />
            <div>
              <h3 className="font-medium text-green-900 mb-2">Helpful Tips</h3>
              <div className="space-y-2 text-sm text-green-800">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>Tap any button once to select it</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>Use the eye icon (👁️) for accessibility options</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>Say "help" for voice assistance</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>Your family can see your photos and activities</span>
                </div>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Safety Reminders */}
      <Card className="p-6 bg-amber-50 border-amber-200">
        <div className="flex items-start gap-3">
          <AlertCircle className="w-6 h-6 text-amber-600 flex-shrink-0 mt-1" />
          <div>
            <h3 className="font-medium text-amber-900 mb-2">Safety Reminders</h3>
            <div className="space-y-2 text-sm text-amber-800">
              <div>• Keep your phone charged and nearby</div>
              <div>• Take medications as prescribed</div>
              <div>• Stay hydrated throughout the day</div>
              <div>• Contact family if you need help</div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

// Component for large, clear buttons
interface SeniorFriendlyButtonProps {
  children: React.ReactNode;
  onClick: () => void;
  icon?: React.ComponentType<any>;
  variant?: 'primary' | 'secondary' | 'emergency';
  disabled?: boolean;
  className?: string;
}

export const SeniorFriendlyButton: React.FC<SeniorFriendlyButtonProps> = ({
  children,
  onClick,
  icon: Icon,
  variant = 'primary',
  disabled = false,
  className = ''
}) => {
  const { settings } = useAccessibility();
  
  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600';
      case 'secondary':
        return 'bg-gray-100 hover:bg-gray-200 text-gray-900 border-gray-300';
      case 'emergency':
        return 'bg-red-600 hover:bg-red-700 text-white border-red-600';
      default:
        return 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600';
    }
  };

  const buttonSize = settings.largeButtons ? 'h-20 px-8 text-xl' : 'h-16 px-6 text-lg';

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`
        ${buttonSize}
        ${getVariantStyles()}
        border-2 rounded-xl font-semibold
        flex items-center justify-center gap-3
        transition-all duration-200
        focus:ring-4 focus:ring-blue-300 focus:outline-none
        disabled:opacity-50 disabled:cursor-not-allowed
        shadow-lg hover:shadow-xl
        ${settings.highContrast ? 'border-4' : ''}
        ${className}
      `}
      aria-label={typeof children === 'string' ? children : undefined}
    >
      {Icon && <Icon className="w-6 h-6" />}
      <span>{children}</span>
    </button>
  );
};

// Component for clear, readable text
interface SeniorFriendlyTextProps {
  children: React.ReactNode;
  size?: 'small' | 'medium' | 'large' | 'extra-large';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  color?: 'gray' | 'black' | 'blue' | 'green' | 'red';
  className?: string;
}

export const SeniorFriendlyText: React.FC<SeniorFriendlyTextProps> = ({
  children,
  size = 'medium',
  weight = 'normal',
  color = 'gray',
  className = ''
}) => {
  const { settings } = useAccessibility();
  
  const getSizeClass = () => {
    const baseSize = {
      'small': 'text-sm',
      'medium': 'text-base',
      'large': 'text-lg',
      'extra-large': 'text-xl'
    }[size];
    
    // Increase size if large fonts are enabled
    if (settings.fontSize === 'large' || settings.fontSize === 'extra-large') {
      return {
        'small': 'text-base',
        'medium': 'text-lg',
        'large': 'text-xl',
        'extra-large': 'text-2xl'
      }[size];
    }
    
    return baseSize;
  };

  const getWeightClass = () => {
    return {
      'normal': 'font-normal',
      'medium': 'font-medium',
      'semibold': 'font-semibold',
      'bold': 'font-bold'
    }[weight];
  };

  const getColorClass = () => {
    return {
      'gray': 'text-gray-900',
      'black': 'text-black',
      'blue': 'text-blue-900',
      'green': 'text-green-900',
      'red': 'text-red-900'
    }[color];
  };

  return (
    <span className={`
      ${getSizeClass()}
      ${getWeightClass()}
      ${getColorClass()}
      ${settings.highContrast ? 'font-bold' : ''}
      leading-relaxed
      ${className}
    `}>
      {children}
    </span>
  );
};
