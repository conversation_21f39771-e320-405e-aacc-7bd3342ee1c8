import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { Tenant, TenantUser, TenantUserPermissions } from '../types/tenant';
import { tenantService } from '../services/tenantService';

interface TenantStore {
  // Current tenant state
  currentTenant: Tenant | null;
  currentUserRole: string | null;
  currentUserPermissions: TenantUserPermissions | null;
  tenantUsers: TenantUser[];
  
  // Loading states
  isLoading: boolean;
  isLoadingUsers: boolean;
  
  // Actions
  setCurrentTenant: (tenant: Tenant | null) => void;
  setCurrentUserRole: (role: string | null) => void;
  setCurrentUserPermissions: (permissions: TenantUserPermissions | null) => void;
  setTenantUsers: (users: TenantUser[]) => void;
  
  // Async actions
  loadTenantUsers: (tenantId: string) => Promise<void>;
  checkUserPermission: (permission: keyof TenantUserPermissions) => boolean;
  refreshCurrentTenant: () => Promise<void>;
  
  // Utility actions
  clearTenantData: () => void;
  updateTenantSettings: (settings: any) => Promise<void>;
}

export const useTenantStore = create<TenantStore>()(
  persist(
    (set, get) => ({
      // Initial state
      currentTenant: null,
      currentUserRole: null,
      currentUserPermissions: null,
      tenantUsers: [],
      isLoading: false,
      isLoadingUsers: false,

      // Basic setters
      setCurrentTenant: (tenant) => {
        set({ currentTenant: tenant });
        
        // Clear related data when tenant changes
        if (!tenant) {
          set({ 
            currentUserRole: null, 
            currentUserPermissions: null, 
            tenantUsers: [] 
          });
        }
      },

      setCurrentUserRole: (role) => set({ currentUserRole: role }),
      
      setCurrentUserPermissions: (permissions) => set({ currentUserPermissions: permissions }),
      
      setTenantUsers: (users) => set({ tenantUsers: users }),

      // Load tenant users
      loadTenantUsers: async (tenantId: string) => {
        try {
          set({ isLoadingUsers: true });
          const users = await tenantService.getTenantUsers(tenantId);
          set({ tenantUsers: users });
        } catch (error) {
          console.error('Error loading tenant users:', error);
          set({ tenantUsers: [] });
        } finally {
          set({ isLoadingUsers: false });
        }
      },

      // Check if current user has a specific permission
      checkUserPermission: (permission: keyof TenantUserPermissions): boolean => {
        const { currentUserPermissions } = get();
        return currentUserPermissions?.[permission] === true;
      },

      // Refresh current tenant data
      refreshCurrentTenant: async () => {
        const { currentTenant } = get();
        if (!currentTenant?.id) return;

        try {
          set({ isLoading: true });
          const refreshedTenant = await tenantService.getTenant(currentTenant.id);
          if (refreshedTenant) {
            set({ currentTenant: refreshedTenant });
          }
        } catch (error) {
          console.error('Error refreshing tenant:', error);
        } finally {
          set({ isLoading: false });
        }
      },

      // Update tenant settings
      updateTenantSettings: async (settings: any) => {
        const { currentTenant } = get();
        if (!currentTenant?.id) throw new Error('No current tenant');

        try {
          await tenantService.updateTenantSettings(currentTenant.id, settings, 'current-user-id');
          
          // Update local state
          set({
            currentTenant: {
              ...currentTenant,
              settings: { ...currentTenant.settings, ...settings },
              updatedAt: new Date()
            }
          });
        } catch (error) {
          console.error('Error updating tenant settings:', error);
          throw error;
        }
      },

      // Clear all tenant data
      clearTenantData: () => {
        set({
          currentTenant: null,
          currentUserRole: null,
          currentUserPermissions: null,
          tenantUsers: [],
          isLoading: false,
          isLoadingUsers: false
        });
      },
    }),
    {
      name: 'tenant-store',
      partialize: (state) => ({
        // Only persist essential data
        currentTenant: state.currentTenant,
        currentUserRole: state.currentUserRole,
        currentUserPermissions: state.currentUserPermissions,
      }),
    }
  )
);

// Helper hooks for common operations
export const useTenantPermissions = () => {
  const checkUserPermission = useTenantStore(state => state.checkUserPermission);
  const currentUserPermissions = useTenantStore(state => state.currentUserPermissions);
  
  return {
    checkUserPermission,
    currentUserPermissions,
    
    // Common permission checks
    canViewPhotos: checkUserPermission('canViewPhotos'),
    canUploadPhotos: checkUserPermission('canUploadPhotos'),
    canDeletePhotos: checkUserPermission('canDeletePhotos'),
    canViewConversations: checkUserPermission('canViewConversations'),
    canParticipateInChat: checkUserPermission('canParticipateInChat'),
    canViewMedications: checkUserPermission('canViewMedications'),
    canManageMedications: checkUserPermission('canManageMedications'),
    canInviteUsers: checkUserPermission('canInviteUsers'),
    canRemoveUsers: checkUserPermission('canRemoveUsers'),
    canModifyUserRoles: checkUserPermission('canModifyUserRoles'),
    canViewUserActivity: checkUserPermission('canViewUserActivity'),
    canModifyTenantSettings: checkUserPermission('canModifyTenantSettings'),
    canViewAnalytics: checkUserPermission('canViewAnalytics'),
    canExportData: checkUserPermission('canExportData'),
    canManageSubscription: checkUserPermission('canManageSubscription'),
    canReceiveEmergencyAlerts: checkUserPermission('canReceiveEmergencyAlerts'),
    canTriggerEmergencyAlerts: checkUserPermission('canTriggerEmergencyAlerts'),
    canAccessEmergencyContacts: checkUserPermission('canAccessEmergencyContacts'),
  };
};

export const useTenantSettings = () => {
  const currentTenant = useTenantStore(state => state.currentTenant);
  const updateTenantSettings = useTenantStore(state => state.updateTenantSettings);
  
  return {
    settings: currentTenant?.settings,
    updateSettings: updateTenantSettings,
    
    // Feature flags
    isFeatureEnabled: (feature: keyof typeof currentTenant.settings.features) => {
      return currentTenant?.settings?.features?.[feature] === true;
    },
    
    // AI settings
    aiSettings: currentTenant?.settings?.aiSettings,
    isAIEnabled: currentTenant?.settings?.aiSettings?.enabled === true,
    
    // Notification settings
    notificationSettings: currentTenant?.settings?.notifications,
    
    // Media settings
    mediaSettings: currentTenant?.settings?.mediaSettings,
    
    // Caregiver settings
    caregiverSettings: currentTenant?.settings?.caregiverSettings,
  };
};

export const useTenantUsers = () => {
  const tenantUsers = useTenantStore(state => state.tenantUsers);
  const isLoadingUsers = useTenantStore(state => state.isLoadingUsers);
  const loadTenantUsers = useTenantStore(state => state.loadTenantUsers);
  const currentTenant = useTenantStore(state => state.currentTenant);
  
  return {
    tenantUsers,
    isLoadingUsers,
    loadTenantUsers,
    
    // Helper functions
    getUserByRole: (role: string) => tenantUsers.filter(user => user.role === role),
    getOwners: () => tenantUsers.filter(user => user.role === 'owner'),
    getAdmins: () => tenantUsers.filter(user => user.role === 'admin'),
    getCaregivers: () => tenantUsers.filter(user => user.role === 'caregiver'),
    getPatients: () => tenantUsers.filter(user => user.role === 'patient'),
    getGuests: () => tenantUsers.filter(user => user.role === 'guest'),
    
    // Refresh users for current tenant
    refreshUsers: () => {
      if (currentTenant?.id) {
        loadTenantUsers(currentTenant.id);
      }
    },
  };
};
