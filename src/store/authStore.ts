import { create } from "zustand";
import { persist } from "zustand/middleware";
import { supabase, auth } from "../lib/supabase";
import type { User as SupabaseUser } from "@supabase/supabase-js";

export interface User {
  id: string;
  name: string;
  email: string;
  role: "patient" | "caregiver" | "admin";
  tenantId: string;
  language: "en" | "fr" | "ar";
  avatar?: string;
  preferences: {
    voiceEnabled: boolean;
    fontSize: "small" | "medium" | "large";
    highContrast: boolean;
    notifications: boolean;
    conversationStyle: "casual" | "formal" | "encouraging";
    reminderVolume: number;
  };
  caregiverIds?: string[];
  patientIds?: string[];
}

export interface Tenant {
  id: string;
  name: string;
  plan: "free" | "premium" | "professional";
  settings: {
    allowedLanguages: string[];
    maxUsers: number;
    features: string[];
  };
  createdAt: Date;
}

interface AuthState {
  user: User | null;
  tenant: Tenant | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  signUp: (
    email: string,
    password: string,
    userData: Partial<User>
  ) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  login: (user: User) => void; // For direct login (admin portal)
  createDemoUser: (email: string) => User;
  signOut: () => Promise<void>;
  updateUser: (updates: Partial<User>) => Promise<void>;
  updateTenant: (updates: Partial<Tenant>) => void;
  initialize: () => Promise<void>;
  createUserProfile: (
    supabaseUser: SupabaseUser,
    userData: Partial<User>
  ) => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      tenant: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      signUp: async (
        email: string,
        password: string,
        userData: Partial<User>
      ) => {
        set({ isLoading: true, error: null });
        try {
          // Sign up with Supabase Auth
          const { data, error } = await auth.signUp(email, password, {
            email,
            full_name: userData.name,
            user_metadata: {
              name: userData.name,
              role: userData.role,
              language: userData.language || "en",
            },
          });

          if (error) throw error;

          // If user is immediately confirmed (email confirmation disabled), create profile
          if (data.user && data.user.email_confirmed_at) {
            await get().createUserProfile(data.user, userData);
          }

          set({ isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          throw error;
        }
      },

      signIn: async (email: string, password: string) => {
        set({ isLoading: true, error: null });

        // Handle demo accounts
        if (email.endsWith(".demo") && password === "demodemo") {
          const demoUser = get().createDemoUser(email);
          set({ user: demoUser, isAuthenticated: true, isLoading: false });
          return;
        }

        try {
          const { data, error } = await auth.signIn(email, password);
          if (error) throw error;

          if (data.user) {
            // Try to get existing user profile
            const { data: userProfile, error: profileError } = await supabase
              .from("users")
              .select("*")
              .eq("id", data.user.id)
              .single();

            if (profileError && profileError.code === "PGRST116") {
              // User profile doesn't exist, create it from user metadata
              const userData = {
                name:
                  data.user.user_metadata?.name ||
                  data.user.email?.split("@")[0] ||
                  "User",
                role: data.user.user_metadata?.role || "patient",
                language: data.user.user_metadata?.language || "en",
              };
              await get().createUserProfile(data.user, userData);

              // Retry getting the profile
              const { data: newProfile, error: newProfileError } =
                await supabase
                  .from("users")
                  .select("*")
                  .eq("id", data.user.id)
                  .single();

              if (newProfileError) throw newProfileError;

              const user: User = {
                id: newProfile.id,
                name: newProfile.name,
                email: newProfile.email,
                role: newProfile.role,
                tenantId: newProfile.tenant_id,
                language: newProfile.language,
                avatar: newProfile.avatar_url,
                preferences: newProfile.preferences as User["preferences"],
              };

              set({ user, isAuthenticated: true, isLoading: false });
            } else if (profileError) {
              throw profileError;
            } else {
              const user: User = {
                id: userProfile.id,
                name: userProfile.name,
                email: userProfile.email,
                role: userProfile.role,
                tenantId: userProfile.tenant_id,
                language: userProfile.language,
                avatar: userProfile.avatar_url,
                preferences: userProfile.preferences as User["preferences"],
              };

              set({ user, isAuthenticated: true, isLoading: false });
            }
          }
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          throw error;
        }
      },

      login: (user: User) => {
        set({ user, isAuthenticated: true, isLoading: false });
      },

      signOut: async () => {
        set({ isLoading: true });
        try {
          const { error } = await auth.signOut();
          if (error) throw error;

          set({
            user: null,
            tenant: null,
            isAuthenticated: false,
            isLoading: false,
          });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          throw error;
        }
      },

      updateUser: async (updates: Partial<User>) => {
        const { user } = get();
        if (!user) return;

        set({ isLoading: true });
        try {
          const { data, error } = await supabase
            .from("users")
            .update({
              name: updates.name,
              language: updates.language,
              preferences: updates.preferences,
              avatar_url: updates.avatar,
            })
            .eq("id", user.id)
            .select()
            .single();

          if (error) throw error;

          const updatedUser: User = { ...user, ...updates };
          set({ user: updatedUser, isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          throw error;
        }
      },

      updateTenant: (updates) =>
        set((state) => ({
          tenant: state.tenant ? { ...state.tenant, ...updates } : null,
        })),

      initialize: async () => {
        set({ isLoading: true });
        try {
          const {
            data: { user: supabaseUser },
          } = await supabase.auth.getUser();

          if (supabaseUser) {
            const { data: userProfile, error } = await supabase
              .from("users")
              .select("*")
              .eq("id", supabaseUser.id)
              .single();

            if (!error && userProfile) {
              const user: User = {
                id: userProfile.id,
                name: userProfile.name,
                email: userProfile.email,
                role: userProfile.role,
                tenantId: userProfile.tenant_id,
                language: userProfile.language,
                avatar: userProfile.avatar_url,
                preferences: userProfile.preferences as User["preferences"],
              };

              set({ user, isAuthenticated: true, isLoading: false });
              return;
            } else if (error && error.code === "PGRST116") {
              // Profile doesn't exist, create it
              const userData = {
                name:
                  supabaseUser.user_metadata?.name ||
                  supabaseUser.email?.split("@")[0] ||
                  "User",
                role: supabaseUser.user_metadata?.role || "patient",
                language: supabaseUser.user_metadata?.language || "en",
              };

              await get().createUserProfile(supabaseUser, userData);

              // Retry getting the profile
              const { data: newProfile, error: newProfileError } =
                await supabase
                  .from("users")
                  .select("*")
                  .eq("id", supabaseUser.id)
                  .single();

              if (!newProfileError && newProfile) {
                const user: User = {
                  id: newProfile.id,
                  name: newProfile.name,
                  email: newProfile.email,
                  role: newProfile.role,
                  tenantId: newProfile.tenant_id,
                  language: newProfile.language,
                  avatar: newProfile.avatar_url,
                  preferences: newProfile.preferences as User["preferences"],
                };

                set({ user, isAuthenticated: true, isLoading: false });
                return;
              }
            }
          }

          set({ isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      createUserProfile: async (
        supabaseUser: SupabaseUser,
        userData: Partial<User>
      ) => {
        try {
          // Use the database function to create user profile with tenant
          const { data, error } = await supabase.rpc("create_user_profile", {
            user_id: supabaseUser.id,
            user_email: supabaseUser.email!,
            user_name: userData.name!,
            user_role: userData.role || "patient",
            user_language: userData.language || "en",
          });

          if (error) throw error;

          return data;
        } catch (error) {
          console.error("Failed to create user profile:", error);
          throw error;
        }
      },

      createDemoUser: (email: string): User => {
        const demoUsers: Record<string, User> = {
          "<EMAIL>": {
            id: "demo-facility-admin-001",
            name: "Sarah Johnson",
            email: "<EMAIL>",
            role: "admin",
            tenantId: "sunny-meadows-facility",
            language: "en",
            avatar: undefined,
            preferences: {
              voiceEnabled: true,
              fontSize: "medium",
              highContrast: false,
              notifications: true,
              conversationStyle: "formal",
              reminderVolume: 0.7,
            },
          },
          "<EMAIL>": {
            id: "demo-care-staff-001",
            name: "Michael Rodriguez",
            email: "<EMAIL>",
            role: "caregiver",
            tenantId: "sunny-meadows-facility",
            language: "en",
            avatar: undefined,
            preferences: {
              voiceEnabled: true,
              fontSize: "medium",
              highContrast: false,
              notifications: true,
              conversationStyle: "encouraging",
              reminderVolume: 0.8,
            },
          },
          "<EMAIL>": {
            id: "demo-patient-001",
            name: "Margaret Thompson",
            email: "<EMAIL>",
            role: "patient",
            tenantId: "demo-family-001",
            language: "en",
            avatar: undefined,
            preferences: {
              voiceEnabled: true,
              fontSize: "large",
              highContrast: false,
              notifications: true,
              conversationStyle: "encouraging",
              reminderVolume: 0.9,
            },
          },
          "<EMAIL>": {
            id: "demo-caregiver-001",
            name: "Jennifer Thompson",
            email: "<EMAIL>",
            role: "caregiver",
            tenantId: "demo-family-001",
            language: "en",
            avatar: undefined,
            preferences: {
              voiceEnabled: true,
              fontSize: "medium",
              highContrast: false,
              notifications: true,
              conversationStyle: "caring",
              reminderVolume: 0.7,
            },
            patientIds: ["demo-patient-001"],
          },
          "<EMAIL>": {
            id: "demo-super-admin-001",
            name: "System Administrator",
            email: "<EMAIL>",
            role: "admin",
            tenantId: "system-admin",
            language: "en",
            avatar: undefined,
            preferences: {
              voiceEnabled: true,
              fontSize: "medium",
              highContrast: false,
              notifications: true,
              conversationStyle: "formal",
              reminderVolume: 0.5,
            },
          },
        };

        return demoUsers[email] || demoUsers["<EMAIL>"];
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        user: state.user,
        tenant: state.tenant,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
