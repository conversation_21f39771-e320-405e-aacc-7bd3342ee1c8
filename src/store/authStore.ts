import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'patient' | 'caregiver' | 'admin';
  tenantId: string;
  language: 'en' | 'fr' | 'ar';
  avatar?: string;
  preferences: {
    voiceEnabled: boolean;
    fontSize: 'small' | 'medium' | 'large';
    highContrast: boolean;
    notifications: boolean;
  };
  caregiverIds?: string[];
  patientIds?: string[];
}

export interface Tenant {
  id: string;
  name: string;
  plan: 'free' | 'premium' | 'professional';
  settings: {
    allowedLanguages: string[];
    maxUsers: number;
    features: string[];
  };
  createdAt: Date;
}

interface AuthState {
  user: User | null;
  tenant: Tenant | null;
  isAuthenticated: boolean;
  login: (user: User, tenant: Tenant) => void;
  logout: () => void;
  updateUser: (updates: Partial<User>) => void;
  updateTenant: (updates: Partial<Tenant>) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      tenant: null,
      isAuthenticated: false,
      login: (user, tenant) => set({ user, tenant, isAuthenticated: true }),
      logout: () => set({ user: null, tenant: null, isAuthenticated: false }),
      updateUser: (updates) =>
        set((state) => ({
          user: state.user ? { ...state.user, ...updates } : null,
        })),
      updateTenant: (updates) =>
        set((state) => ({
          tenant: state.tenant ? { ...state.tenant, ...updates } : null,
        })),
    }),
    {
      name: 'auth-storage',
    }
  )
);