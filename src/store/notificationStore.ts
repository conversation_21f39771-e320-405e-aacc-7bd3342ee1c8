import { create } from 'zustand';

export interface NotificationPermission {
  granted: boolean;
  requested: boolean;
}

interface NotificationState {
  permission: NotificationPermission;
  requestPermission: () => Promise<boolean>;
  sendNotification: (title: string, options?: NotificationOptions) => void;
  scheduleReminder: (title: string, body: string, scheduledTime: Date) => void;
}

export const useNotificationStore = create<NotificationState>((set, get) => ({
  permission: {
    granted: Notification.permission === 'granted',
    requested: Notification.permission !== 'default',
  },

  requestPermission: async () => {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return false;
    }

    const permission = await Notification.requestPermission();
    const granted = permission === 'granted';
    
    set({
      permission: {
        granted,
        requested: true,
      },
    });

    return granted;
  },

  sendNotification: (title: string, options?: NotificationOptions) => {
    const { permission } = get();
    
    if (!permission.granted) {
      console.warn('Notification permission not granted');
      return;
    }

    new Notification(title, {
      icon: '/pwa-192x192.png',
      badge: '/pwa-192x192.png',
      ...options,
    });
  },

  scheduleReminder: (title: string, body: string, scheduledTime: Date) => {
    const { sendNotification } = get();
    const now = new Date();
    const delay = scheduledTime.getTime() - now.getTime();

    if (delay > 0) {
      setTimeout(() => {
        sendNotification(title, {
          body,
          tag: 'medication-reminder',
          requireInteraction: true,
        });
      }, delay);
    }
  },
}));