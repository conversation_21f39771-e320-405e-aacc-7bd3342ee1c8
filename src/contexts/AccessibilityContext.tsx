import React, { createContext, useContext, useEffect, useState } from 'react';
import type { AccessibilitySettings } from '../types/userProfile';
import { DEFAULT_ACCESSIBILITY_SETTINGS } from '../types/userProfile';

interface AccessibilityContextType {
  settings: AccessibilitySettings;
  updateSettings: (newSettings: Partial<AccessibilitySettings>) => void;
  applySettings: () => void;
  resetSettings: () => void;
  isLoading: boolean;
}

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined);

interface AccessibilityProviderProps {
  children: React.ReactNode;
  initialSettings?: AccessibilitySettings;
}

export const AccessibilityProvider: React.FC<AccessibilityProviderProps> = ({
  children,
  initialSettings
}) => {
  const [settings, setSettings] = useState<AccessibilitySettings>(
    initialSettings || DEFAULT_ACCESSIBILITY_SETTINGS
  );
  const [isLoading, setIsLoading] = useState(false);

  // Apply accessibility settings to the DOM
  const applySettings = () => {
    const root = document.documentElement;
    
    // Font size
    const fontSizeMap = {
      'small': '14px',
      'medium': '16px',
      'large': '18px',
      'extra-large': '22px'
    };
    root.style.setProperty('--base-font-size', fontSizeMap[settings.fontSize]);
    
    // High contrast
    if (settings.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }
    
    // Color blind support
    if (settings.colorBlindSupport) {
      root.classList.add('color-blind-support');
    } else {
      root.classList.remove('color-blind-support');
    }
    
    // Reduced motion
    if (settings.reducedMotion) {
      root.classList.add('reduced-motion');
    } else {
      root.classList.remove('reduced-motion');
    }
    
    // Large buttons
    if (settings.largeButtons) {
      root.classList.add('large-buttons');
    } else {
      root.classList.remove('large-buttons');
    }
    
    // Simplified interface
    if (settings.simplifiedInterface) {
      root.classList.add('simplified-interface');
    } else {
      root.classList.remove('simplified-interface');
    }
    
    // Slow animations
    if (settings.slowAnimations) {
      root.classList.add('slow-animations');
    } else {
      root.classList.remove('slow-animations');
    }
    
    // Clear instructions
    if (settings.clearInstructions) {
      root.classList.add('clear-instructions');
    } else {
      root.classList.remove('clear-instructions');
    }
    
    // Store settings in localStorage
    localStorage.setItem('accessibility-settings', JSON.stringify(settings));
  };

  // Update settings
  const updateSettings = (newSettings: Partial<AccessibilitySettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  // Reset to defaults
  const resetSettings = () => {
    setSettings(DEFAULT_ACCESSIBILITY_SETTINGS);
  };

  // Apply settings when they change
  useEffect(() => {
    applySettings();
  }, [settings]);

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('accessibility-settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings({ ...DEFAULT_ACCESSIBILITY_SETTINGS, ...parsed });
      } catch (error) {
        console.error('Error loading accessibility settings:', error);
      }
    }
  }, []);

  const value: AccessibilityContextType = {
    settings,
    updateSettings,
    applySettings,
    resetSettings,
    isLoading
  };

  return (
    <AccessibilityContext.Provider value={value}>
      {children}
    </AccessibilityContext.Provider>
  );
};

export const useAccessibility = () => {
  const context = useContext(AccessibilityContext);
  if (context === undefined) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider');
  }
  return context;
};

// Hook for voice navigation
export const useVoiceNavigation = () => {
  const { settings } = useAccessibility();
  const [isListening, setIsListening] = useState(false);
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);

  useEffect(() => {
    if (settings.voiceNavigation && 'webkitSpeechRecognition' in window) {
      const speechRecognition = new (window as any).webkitSpeechRecognition();
      speechRecognition.continuous = true;
      speechRecognition.interimResults = false;
      speechRecognition.lang = 'en-US';
      
      speechRecognition.onresult = (event: any) => {
        const command = event.results[event.results.length - 1][0].transcript.toLowerCase();
        handleVoiceCommand(command);
      };
      
      speechRecognition.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error);
        setIsListening(false);
      };
      
      setRecognition(speechRecognition);
    }
  }, [settings.voiceNavigation]);

  const startListening = () => {
    if (recognition && !isListening) {
      recognition.start();
      setIsListening(true);
    }
  };

  const stopListening = () => {
    if (recognition && isListening) {
      recognition.stop();
      setIsListening(false);
    }
  };

  const handleVoiceCommand = (command: string) => {
    // Basic voice navigation commands
    if (command.includes('go home') || command.includes('home page')) {
      window.location.hash = '#home';
    } else if (command.includes('photos') || command.includes('pictures')) {
      window.location.hash = '#photos';
    } else if (command.includes('chat') || command.includes('talk')) {
      window.location.hash = '#chat';
    } else if (command.includes('medications') || command.includes('medicine')) {
      window.location.hash = '#medications';
    } else if (command.includes('profile') || command.includes('settings')) {
      window.location.hash = '#profile';
    } else if (command.includes('help')) {
      // Show help modal
      const helpEvent = new CustomEvent('show-help');
      window.dispatchEvent(helpEvent);
    }
  };

  return {
    isListening,
    startListening,
    stopListening,
    isSupported: 'webkitSpeechRecognition' in window
  };
};

// Hook for text-to-speech
export const useTextToSpeech = () => {
  const { settings } = useAccessibility();
  const [isSpeaking, setIsSpeaking] = useState(false);

  const speak = (text: string, options?: { rate?: number; pitch?: number; volume?: number }) => {
    if (!settings.screenReader || !('speechSynthesis' in window)) return;

    // Cancel any ongoing speech
    window.speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = options?.rate || 0.8;
    utterance.pitch = options?.pitch || 1;
    utterance.volume = options?.volume || 0.8;

    utterance.onstart = () => setIsSpeaking(true);
    utterance.onend = () => setIsSpeaking(false);
    utterance.onerror = () => setIsSpeaking(false);

    window.speechSynthesis.speak(utterance);
  };

  const stop = () => {
    window.speechSynthesis.cancel();
    setIsSpeaking(false);
  };

  return {
    speak,
    stop,
    isSpeaking,
    isSupported: 'speechSynthesis' in window
  };
};

// Hook for keyboard navigation
export const useKeyboardNavigation = () => {
  const { settings } = useAccessibility();

  useEffect(() => {
    if (!settings.gestureAlternatives) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Enhanced keyboard navigation
      if (event.altKey) {
        switch (event.key) {
          case '1':
            event.preventDefault();
            window.location.hash = '#home';
            break;
          case '2':
            event.preventDefault();
            window.location.hash = '#photos';
            break;
          case '3':
            event.preventDefault();
            window.location.hash = '#chat';
            break;
          case '4':
            event.preventDefault();
            window.location.hash = '#medications';
            break;
          case '5':
            event.preventDefault();
            window.location.hash = '#profile';
            break;
          case 'h':
            event.preventDefault();
            const helpEvent = new CustomEvent('show-help');
            window.dispatchEvent(helpEvent);
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [settings.gestureAlternatives]);
};
