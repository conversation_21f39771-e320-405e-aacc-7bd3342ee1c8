import { supabase } from '../lib/supabase';
import type { User, Tenant } from '../types';

export interface DemoUser {
  email: string;
  password: string;
  name: string;
  role: 'patient' | 'caregiver' | 'admin';
  age?: number;
  phone?: string;
  emergencyContact?: string;
}

export interface DemoTenant {
  name: string;
  type: 'family' | 'facility' | 'organization';
  description: string;
  users: DemoUser[];
}

// Demo credentials for testing
export const DEMO_CREDENTIALS = {
  SUPER_ADMIN: {
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Super Administrator',
    role: 'admin' as const
  },
  PATIENT: {
    email: '<EMAIL>',
    password: 'demodemo',
    name: '<PERSON>',
    role: 'patient' as const
  },
  CAREGIVER: {
    email: '<EMAIL>',
    password: 'demodemo',
    name: '<PERSON>',
    role: 'caregiver' as const
  }
};

// Demo tenants with users
export const DEMO_TENANTS: DemoTenant[] = [
  {
    name: '<PERSON>',
    type: 'family',
    description: 'A loving family caring for their father with early-stage dementia',
    users: [
      {
        email: '<EMAIL>',
        password: 'demodemo',
        name: '<PERSON>',
        role: 'patient',
        age: 78,
        phone: '******-0101',
        emergencyContact: '<PERSON>'
      },
      {
        email: '<EMAIL>',
        password: 'demodemo',
        name: 'Mary Johnson',
        role: 'caregiver',
        age: 52,
        phone: '******-0102',
        emergencyContact: 'Emergency Services'
      },
      {
        email: '<EMAIL>',
        password: 'demodemo',
        name: 'Sarah Smith',
        role: 'caregiver',
        age: 45,
        phone: '******-0103',
        emergencyContact: 'Mary Johnson'
      }
    ]
  },
  {
    name: 'Sunshine Care Facility',
    type: 'facility',
    description: 'A memory care facility with 50 residents',
    users: [
      {
        email: '<EMAIL>',
        password: 'demodemo',
        name: 'Robert Wilson',
        role: 'patient',
        age: 82,
        phone: '******-0201'
      },
      {
        email: '<EMAIL>',
        password: 'demodemo',
        name: 'Patricia Davis',
        role: 'caregiver',
        age: 38,
        phone: '******-0202'
      },
      {
        email: '<EMAIL>',
        password: 'demodemo',
        name: 'Dr. Carlos Martinez',
        role: 'caregiver',
        age: 45,
        phone: '******-0203'
      }
    ]
  },
  {
    name: 'Johnson Family',
    type: 'family',
    description: 'Multi-generational family with grandmother needing memory support',
    users: [
      {
        email: '<EMAIL>',
        password: 'demodemo',
        name: 'Eleanor Johnson',
        role: 'patient',
        age: 85,
        phone: '******-0301'
      },
      {
        email: '<EMAIL>',
        password: 'demodemo',
        name: 'Lisa Johnson-Brown',
        role: 'caregiver',
        age: 58,
        phone: '******-0302'
      }
    ]
  }
];

class DemoDataGenerator {
  /**
   * Generate all demo data
   */
  async generateAllDemoData(): Promise<void> {
    try {
      console.log('🚀 Starting demo data generation...');
      
      // Create super admin
      await this.createSuperAdmin();
      
      // Create demo tenants and users
      for (const tenantData of DEMO_TENANTS) {
        await this.createDemoTenant(tenantData);
      }
      
      // Create standalone demo users
      await this.createStandaloneDemoUsers();
      
      // Generate sample data
      await this.generateSamplePhotos();
      await this.generateSampleMedications();
      await this.generateSampleConversations();
      await this.generateSampleAuditLogs();
      
      console.log('✅ Demo data generation completed successfully!');
    } catch (error) {
      console.error('❌ Demo data generation failed:', error);
      throw error;
    }
  }

  /**
   * Create super admin user
   */
  async createSuperAdmin(): Promise<void> {
    try {
      const { email, password, name, role } = DEMO_CREDENTIALS.SUPER_ADMIN;
      
      // Check if super admin already exists
      const { data: existingUser } = await supabase
        .from('users')
        .select('id')
        .eq('email', email)
        .single();
      
      if (existingUser) {
        console.log('Super admin already exists');
        return;
      }
      
      // Create super admin
      const { data: authUser, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
            role
          }
        }
      });
      
      if (authError) {
        console.warn('Auth signup failed (user may already exist):', authError.message);
        return;
      }
      
      // Insert into users table
      const { error: userError } = await supabase
        .from('users')
        .insert([{
          id: authUser.user?.id,
          email,
          name,
          role,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }]);
      
      if (userError) {
        console.warn('User table insert failed:', userError.message);
      } else {
        console.log('✅ Super admin created successfully');
      }
    } catch (error) {
      console.error('Failed to create super admin:', error);
    }
  }

  /**
   * Create demo tenant with users
   */
  async createDemoTenant(tenantData: DemoTenant): Promise<void> {
    try {
      console.log(`Creating demo tenant: ${tenantData.name}`);
      
      // Create tenant
      const { data: tenant, error: tenantError } = await supabase
        .from('tenants')
        .insert([{
          name: tenantData.name,
          type: tenantData.type,
          description: tenantData.description,
          status: 'active',
          settings: {
            dataRetentionDays: 365,
            allowDataExport: true,
            requireTwoFactor: false,
            features: {
              aiChat: true,
              photoSharing: true,
              medicationReminders: true,
              caregiverDashboard: true
            }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();
      
      if (tenantError) {
        console.warn(`Failed to create tenant ${tenantData.name}:`, tenantError.message);
        return;
      }
      
      // Create users for this tenant
      for (const userData of tenantData.users) {
        await this.createDemoUser(userData, tenant.id);
      }
      
      console.log(`✅ Created tenant: ${tenantData.name}`);
    } catch (error) {
      console.error(`Failed to create tenant ${tenantData.name}:`, error);
    }
  }

  /**
   * Create demo user
   */
  async createDemoUser(userData: DemoUser, tenantId?: string): Promise<void> {
    try {
      // Check if user already exists
      const { data: existingUser } = await supabase
        .from('users')
        .select('id')
        .eq('email', userData.email)
        .single();
      
      if (existingUser) {
        console.log(`User ${userData.email} already exists`);
        return;
      }
      
      // Create auth user
      const { data: authUser, error: authError } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            name: userData.name,
            role: userData.role
          }
        }
      });
      
      if (authError) {
        console.warn(`Auth signup failed for ${userData.email}:`, authError.message);
        return;
      }
      
      // Insert into users table
      const { error: userError } = await supabase
        .from('users')
        .insert([{
          id: authUser.user?.id,
          email: userData.email,
          name: userData.name,
          role: userData.role,
          phone: userData.phone,
          emergency_contact: userData.emergencyContact,
          date_of_birth: userData.age ? new Date(Date.now() - userData.age * 365 * 24 * 60 * 60 * 1000).toISOString() : null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }]);
      
      if (userError) {
        console.warn(`User table insert failed for ${userData.email}:`, userError.message);
        return;
      }
      
      // Add to tenant if specified
      if (tenantId && authUser.user?.id) {
        const { error: tenantUserError } = await supabase
          .from('tenant_users')
          .insert([{
            tenant_id: tenantId,
            user_id: authUser.user.id,
            role: userData.role === 'patient' ? 'member' : 'caregiver',
            status: 'active',
            joined_at: new Date().toISOString()
          }]);
        
        if (tenantUserError) {
          console.warn(`Failed to add user to tenant:`, tenantUserError.message);
        }
      }
      
      console.log(`✅ Created user: ${userData.name} (${userData.email})`);
    } catch (error) {
      console.error(`Failed to create user ${userData.email}:`, error);
    }
  }

  /**
   * Create standalone demo users
   */
  async createStandaloneDemoUsers(): Promise<void> {
    const standaloneUsers = [
      DEMO_CREDENTIALS.PATIENT,
      DEMO_CREDENTIALS.CAREGIVER
    ];
    
    for (const userData of standaloneUsers) {
      await this.createDemoUser(userData);
    }
  }

  /**
   * Generate sample photos
   */
  async generateSamplePhotos(): Promise<void> {
    console.log('Generating sample photos...');
    // This would generate sample photo records
    // For demo purposes, we'll skip actual file uploads
  }

  /**
   * Generate sample medications
   */
  async generateSampleMedications(): Promise<void> {
    console.log('Generating sample medications...');
    // This would generate sample medication records
  }

  /**
   * Generate sample conversations
   */
  async generateSampleConversations(): Promise<void> {
    console.log('Generating sample conversations...');
    // This would generate sample conversation records
  }

  /**
   * Generate sample audit logs
   */
  async generateSampleAuditLogs(): Promise<void> {
    console.log('Generating sample audit logs...');
    // This would generate sample audit log entries
  }

  /**
   * Clear all demo data
   */
  async clearDemoData(): Promise<void> {
    try {
      console.log('🧹 Clearing demo data...');
      
      // Clear in reverse order of dependencies
      await supabase.from('tenant_users').delete().neq('id', '00000000-0000-0000-0000-000000000000');
      await supabase.from('users').delete().like('email', '%@demo.com');
      await supabase.from('tenants').delete().neq('id', '00000000-0000-0000-0000-000000000000');
      
      console.log('✅ Demo data cleared successfully');
    } catch (error) {
      console.error('❌ Failed to clear demo data:', error);
      throw error;
    }
  }

  /**
   * Verify super admin access
   */
  async verifySuperAdminAccess(email: string, password: string): Promise<boolean> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error || !data.user) {
        return false;
      }
      
      // Check if user has admin role
      const { data: userData } = await supabase
        .from('users')
        .select('role')
        .eq('id', data.user.id)
        .single();
      
      return userData?.role === 'admin';
    } catch (error) {
      console.error('Failed to verify super admin access:', error);
      return false;
    }
  }
}

export const demoDataGenerator = new DemoDataGenerator();
