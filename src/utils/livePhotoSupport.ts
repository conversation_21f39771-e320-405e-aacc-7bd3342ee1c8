// Live Photo support utilities for iOS integration
export interface LivePhotoData {
  stillImage: File;
  videoComponent?: File;
  isLivePhoto: boolean;
  duration?: number;
}

export class LivePhotoProcessor {
  /**
   * Check if a file is a Live Photo
   */
  static isLivePhoto(file: File): boolean {
    // Live Photos typically have specific metadata or naming conventions
    const filename = file.name.toLowerCase();
    
    // Check for Live Photo indicators
    if (filename.includes('live') || filename.includes('mov')) {
      return true;
    }
    
    // Check MIME type for Live Photo video component
    if (file.type === 'video/quicktime' || file.type === 'video/mov') {
      return true;
    }
    
    return false;
  }

  /**
   * Process Live Photo files and extract components
   */
  static async processLivePhoto(files: File[]): Promise<LivePhotoData[]> {
    const processedPhotos: LivePhotoData[] = [];
    const fileMap = new Map<string, File[]>();
    
    // Group files by base name (Live Photos come as pairs)
    files.forEach(file => {
      const baseName = this.getBaseName(file.name);
      if (!fileMap.has(baseName)) {
        fileMap.set(baseName, []);
      }
      fileMap.get(baseName)!.push(file);
    });
    
    // Process each group
    for (const [baseName, groupFiles] of fileMap) {
      const stillImage = groupFiles.find(f => f.type.startsWith('image/'));
      const videoComponent = groupFiles.find(f => f.type.startsWith('video/'));
      
      if (stillImage) {
        const livePhotoData: LivePhotoData = {
          stillImage,
          videoComponent,
          isLivePhoto: !!videoComponent,
          duration: videoComponent ? await this.getVideoDuration(videoComponent) : undefined
        };
        
        processedPhotos.push(livePhotoData);
      }
    }
    
    return processedPhotos;
  }

  /**
   * Get base name for file grouping
   */
  private static getBaseName(filename: string): string {
    // Remove extension and Live Photo suffixes
    return filename
      .replace(/\.(jpg|jpeg|png|heic|mov|mp4)$/i, '')
      .replace(/_live$/i, '')
      .replace(/live$/i, '');
  }

  /**
   * Get video duration
   */
  private static async getVideoDuration(videoFile: File): Promise<number> {
    return new Promise((resolve) => {
      const video = document.createElement('video');
      video.preload = 'metadata';
      
      video.onloadedmetadata = () => {
        resolve(video.duration);
        URL.revokeObjectURL(video.src);
      };
      
      video.onerror = () => {
        resolve(0);
        URL.revokeObjectURL(video.src);
      };
      
      video.src = URL.createObjectURL(videoFile);
    });
  }

  /**
   * Create a preview for Live Photo
   */
  static async createLivePhotoPreview(livePhoto: LivePhotoData): Promise<string> {
    // For now, just return the still image preview
    // In the future, this could create an animated preview
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          resolve(e.target.result as string);
        } else {
          reject(new Error('Failed to create preview'));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(livePhoto.stillImage);
    });
  }

  /**
   * Extract metadata from Live Photo
   */
  static async extractLivePhotoMetadata(livePhoto: LivePhotoData): Promise<Record<string, any>> {
    const metadata: Record<string, any> = {
      isLivePhoto: livePhoto.isLivePhoto,
      hasVideoComponent: !!livePhoto.videoComponent,
      duration: livePhoto.duration
    };

    // Add file size information
    metadata.stillImageSize = livePhoto.stillImage.size;
    if (livePhoto.videoComponent) {
      metadata.videoComponentSize = livePhoto.videoComponent.size;
      metadata.totalSize = livePhoto.stillImage.size + livePhoto.videoComponent.size;
    }

    return metadata;
  }
}

// iOS Photos app integration utilities
export class iOSPhotosIntegration {
  /**
   * Check if device is iOS
   */
  static isIOSDevice(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  }

  /**
   * Check if device supports iOS Photos integration
   */
  static supportsPhotosIntegration(): boolean {
    return this.isIOSDevice() && 'showOpenFilePicker' in window;
  }

  /**
   * Open iOS Photos app for selection
   */
  static async openPhotosApp(options: {
    multiple?: boolean;
    acceptedTypes?: string[];
  } = {}): Promise<File[]> {
    const { multiple = true, acceptedTypes = ['image/*', 'video/*'] } = options;

    try {
      // Use File System Access API if available (newer iOS versions)
      if ('showOpenFilePicker' in window) {
        const fileHandles = await (window as any).showOpenFilePicker({
          multiple,
          types: [{
            description: 'Photos and Videos',
            accept: {
              'image/*': ['.jpg', '.jpeg', '.png', '.heic', '.heif'],
              'video/*': ['.mov', '.mp4']
            }
          }]
        });

        const files: File[] = [];
        for (const fileHandle of fileHandles) {
          const file = await fileHandle.getFile();
          files.push(file);
        }
        return files;
      }
    } catch (error) {
      console.warn('File System Access API not available, falling back to input');
    }

    // Fallback to traditional file input
    return new Promise((resolve, reject) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.multiple = multiple;
      input.accept = acceptedTypes.join(',');
      
      // iOS specific attributes
      input.setAttribute('capture', 'environment');
      input.setAttribute('webkitdirectory', 'false');

      input.onchange = (e) => {
        const target = e.target as HTMLInputElement;
        if (target.files) {
          resolve(Array.from(target.files));
        } else {
          resolve([]);
        }
      };

      input.oncancel = () => resolve([]);
      input.onerror = () => reject(new Error('Failed to open Photos app'));

      input.click();
    });
  }

  /**
   * Handle iOS specific file types
   */
  static async processIOSFiles(files: File[]): Promise<File[]> {
    const processedFiles: File[] = [];

    for (const file of files) {
      // Handle HEIC/HEIF conversion if needed
      if (file.type === 'image/heic' || file.type === 'image/heif') {
        try {
          const convertedFile = await this.convertHEICToJPEG(file);
          processedFiles.push(convertedFile);
        } catch (error) {
          console.warn('HEIC conversion failed, using original file:', error);
          processedFiles.push(file);
        }
      } else {
        processedFiles.push(file);
      }
    }

    return processedFiles;
  }

  /**
   * Convert HEIC to JPEG (placeholder - would need actual conversion library)
   */
  private static async convertHEICToJPEG(heicFile: File): Promise<File> {
    // This is a placeholder - in a real implementation, you would use
    // a library like heic2any or similar to convert HEIC to JPEG
    console.log('HEIC conversion would happen here for:', heicFile.name);
    
    // For now, just return the original file
    // In production, implement actual HEIC conversion
    return heicFile;
  }

  /**
   * Get iOS Photos app permissions
   */
  static async requestPhotosPermission(): Promise<boolean> {
    try {
      // Check if we can access the Photos app
      if ('permissions' in navigator) {
        const permission = await (navigator as any).permissions.query({
          name: 'camera'
        });
        return permission.state === 'granted';
      }
      return true; // Assume granted if we can't check
    } catch (error) {
      console.warn('Could not check Photos permission:', error);
      return true;
    }
  }
}

// Batch import utilities
export class BatchImportProcessor {
  /**
   * Process multiple files in batches to avoid memory issues
   */
  static async processBatch(
    files: File[],
    batchSize: number = 5,
    onProgress?: (processed: number, total: number) => void
  ): Promise<File[]> {
    const processedFiles: File[] = [];
    
    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize);
      
      // Process batch
      const batchResults = await Promise.all(
        batch.map(async (file) => {
          try {
            // Process individual file (resize, convert, etc.)
            return await this.processFile(file);
          } catch (error) {
            console.error('Error processing file:', file.name, error);
            return file; // Return original if processing fails
          }
        })
      );
      
      processedFiles.push(...batchResults);
      
      // Report progress
      if (onProgress) {
        onProgress(processedFiles.length, files.length);
      }
      
      // Small delay to prevent UI blocking
      await new Promise(resolve => setTimeout(resolve, 10));
    }
    
    return processedFiles;
  }

  /**
   * Process individual file
   */
  private static async processFile(file: File): Promise<File> {
    // Add any file processing logic here
    // For example: resize images, compress, etc.
    return file;
  }

  /**
   * Validate file before processing
   */
  static validateFile(file: File, options: {
    maxSize?: number;
    allowedTypes?: string[];
  } = {}): { valid: boolean; error?: string } {
    const { maxSize = 50 * 1024 * 1024, allowedTypes = ['image/*', 'video/*'] } = options;

    // Check file size
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File too large: ${(file.size / 1024 / 1024).toFixed(1)}MB (max ${maxSize / 1024 / 1024}MB)`
      };
    }

    // Check file type
    const isAllowed = allowedTypes.some(type => {
      if (type.endsWith('/*')) {
        return file.type.startsWith(type.replace('/*', '/'));
      }
      return file.type === type;
    });

    if (!isAllowed) {
      return {
        valid: false,
        error: `File type not supported: ${file.type}`
      };
    }

    return { valid: true };
  }
}
