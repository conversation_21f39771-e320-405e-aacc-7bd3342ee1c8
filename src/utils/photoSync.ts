import { supabase } from '../lib/supabase';
import type { Photo } from '../types';

export interface SyncResult {
  success: boolean;
  uploaded: number;
  downloaded: number;
  errors: string[];
}

export interface DeviceInfo {
  id: string;
  name: string;
  type: 'mobile' | 'tablet' | 'desktop';
  lastSeen: Date;
  photoCount: number;
}

export class PhotoSyncService {
  private static instance: PhotoSyncService;
  private syncQueue: File[] = [];
  private isOnline: boolean = navigator.onLine;
  private syncInProgress: boolean = false;

  static getInstance(): PhotoSyncService {
    if (!PhotoSyncService.instance) {
      PhotoSyncService.instance = new PhotoSyncService();
    }
    return PhotoSyncService.instance;
  }

  constructor() {
    // Monitor online status
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.processSyncQueue();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  /**
   * Sync photos between devices
   */
  async syncPhotos(userId: string, tenantId: string): Promise<SyncResult> {
    if (!this.isOnline) {
      return {
        success: false,
        uploaded: 0,
        downloaded: 0,
        errors: ['No internet connection']
      };
    }

    if (this.syncInProgress) {
      return {
        success: false,
        uploaded: 0,
        downloaded: 0,
        errors: ['Sync already in progress']
      };
    }

    this.syncInProgress = true;
    const result: SyncResult = {
      success: true,
      uploaded: 0,
      downloaded: 0,
      errors: []
    };

    try {
      // Get latest photos from server
      const { data: serverPhotos, error } = await supabase
        .from('photos')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        result.errors.push(`Failed to fetch server photos: ${error.message}`);
        result.success = false;
        return result;
      }

      result.downloaded = serverPhotos?.length || 0;

      // Process any queued uploads
      if (this.syncQueue.length > 0) {
        const uploadResult = await this.uploadQueuedPhotos(userId, tenantId);
        result.uploaded = uploadResult.uploaded;
        result.errors.push(...uploadResult.errors);
      }

      // Update device sync timestamp
      await this.updateDeviceInfo(userId);

    } catch (error) {
      result.success = false;
      result.errors.push(`Sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      this.syncInProgress = false;
    }

    return result;
  }

  /**
   * Add photos to sync queue for offline upload
   */
  queueForSync(files: File[]): void {
    this.syncQueue.push(...files);
    
    // Try to sync immediately if online
    if (this.isOnline && !this.syncInProgress) {
      this.processSyncQueue();
    }
  }

  /**
   * Process queued photos when online
   */
  private async processSyncQueue(): Promise<void> {
    if (this.syncQueue.length === 0 || this.syncInProgress) {
      return;
    }

    // Get user info from auth store
    const userStr = localStorage.getItem('auth-user');
    if (!userStr) return;

    try {
      const user = JSON.parse(userStr);
      await this.uploadQueuedPhotos(user.id, user.tenantId);
    } catch (error) {
      console.error('Failed to process sync queue:', error);
    }
  }

  /**
   * Upload queued photos
   */
  private async uploadQueuedPhotos(userId: string, tenantId: string): Promise<{ uploaded: number; errors: string[] }> {
    const result = { uploaded: 0, errors: [] };
    const filesToUpload = [...this.syncQueue];
    this.syncQueue = []; // Clear queue

    for (const file of filesToUpload) {
      try {
        // Upload file to Supabase storage
        const fileExt = file.name.split('.').pop();
        const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
        const filePath = `${userId}/${fileName}`;

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('photos')
          .upload(filePath, file);

        if (uploadError) {
          result.errors.push(`Failed to upload ${file.name}: ${uploadError.message}`);
          continue;
        }

        // Get public URL
        const { data: urlData } = supabase.storage
          .from('photos')
          .getPublicUrl(filePath);

        // Save photo metadata to database
        const { error: dbError } = await supabase
          .from('photos')
          .insert([{
            user_id: userId,
            tenant_id: tenantId,
            filename: file.name,
            url: urlData.publicUrl,
            file_size: file.size,
            mime_type: file.type,
            metadata: {
              uploadedFrom: this.getDeviceInfo(),
              originalName: file.name,
              fileSize: file.size
            }
          }]);

        if (dbError) {
          result.errors.push(`Failed to save ${file.name} metadata: ${dbError.message}`);
          continue;
        }

        result.uploaded++;
      } catch (error) {
        result.errors.push(`Error uploading ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return result;
  }

  /**
   * Get current device info
   */
  private getDeviceInfo(): DeviceInfo {
    const userAgent = navigator.userAgent;
    let deviceType: 'mobile' | 'tablet' | 'desktop' = 'desktop';

    if (/Mobile|Android|iPhone|iPod/.test(userAgent)) {
      deviceType = 'mobile';
    } else if (/iPad|Tablet/.test(userAgent)) {
      deviceType = 'tablet';
    }

    return {
      id: this.getDeviceId(),
      name: this.getDeviceName(),
      type: deviceType,
      lastSeen: new Date(),
      photoCount: 0 // Will be updated by sync
    };
  }

  /**
   * Get or create device ID
   */
  private getDeviceId(): string {
    let deviceId = localStorage.getItem('device-id');
    if (!deviceId) {
      deviceId = `device-${Date.now()}-${Math.random().toString(36).substring(2)}`;
      localStorage.setItem('device-id', deviceId);
    }
    return deviceId;
  }

  /**
   * Get device name
   */
  private getDeviceName(): string {
    const userAgent = navigator.userAgent;
    
    if (/iPhone/.test(userAgent)) return 'iPhone';
    if (/iPad/.test(userAgent)) return 'iPad';
    if (/Android/.test(userAgent)) return 'Android Device';
    if (/Mac/.test(userAgent)) return 'Mac';
    if (/Windows/.test(userAgent)) return 'Windows PC';
    
    return 'Unknown Device';
  }

  /**
   * Update device info in database
   */
  private async updateDeviceInfo(userId: string): Promise<void> {
    const deviceInfo = this.getDeviceInfo();
    
    try {
      await supabase
        .from('user_devices')
        .upsert([{
          user_id: userId,
          device_id: deviceInfo.id,
          device_name: deviceInfo.name,
          device_type: deviceInfo.type,
          last_seen: new Date().toISOString(),
          user_agent: navigator.userAgent
        }]);
    } catch (error) {
      console.error('Failed to update device info:', error);
    }
  }

  /**
   * Get sync status
   */
  getSyncStatus(): { isOnline: boolean; queueLength: number; syncInProgress: boolean } {
    return {
      isOnline: this.isOnline,
      queueLength: this.syncQueue.length,
      syncInProgress: this.syncInProgress
    };
  }

  /**
   * Clear sync queue
   */
  clearSyncQueue(): void {
    this.syncQueue = [];
  }

  /**
   * Get devices for user
   */
  async getUserDevices(userId: string): Promise<DeviceInfo[]> {
    try {
      const { data, error } = await supabase
        .from('user_devices')
        .select('*')
        .eq('user_id', userId)
        .order('last_seen', { ascending: false });

      if (error) {
        console.error('Failed to fetch user devices:', error);
        return [];
      }

      return data?.map(device => ({
        id: device.device_id,
        name: device.device_name,
        type: device.device_type,
        lastSeen: new Date(device.last_seen),
        photoCount: 0 // Would need separate query to get photo count per device
      })) || [];
    } catch (error) {
      console.error('Error fetching user devices:', error);
      return [];
    }
  }
}

// Export singleton instance
export const photoSyncService = PhotoSyncService.getInstance();
