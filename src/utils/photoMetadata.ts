// Photo metadata extraction utilities
// Note: ExifReader import is optional - if it fails, we'll use basic metadata only
let ExifReader: any = null;

try {
  ExifReader = require("exifreader");
} catch (error) {
  console.warn(
    "ExifReader not available, using basic metadata extraction only"
  );
}

export interface PhotoMetadata {
  dateTime?: string;
  location?: {
    latitude?: number;
    longitude?: number;
    address?: string;
  };
  camera?: {
    make?: string;
    model?: string;
  };
  dimensions?: {
    width?: number;
    height?: number;
  };
  orientation?: number;
  keywords?: string[];
  people?: string[];
  event?: string;
  description?: string;
}

export class PhotoMetadataExtractor {
  /**
   * Extract EXIF data from image file
   */
  static async extractExifData(file: File): Promise<Partial<PhotoMetadata>> {
    if (!ExifReader) {
      console.warn("ExifReader not available, skipping EXIF extraction");
      return {};
    }

    try {
      const arrayBuffer = await file.arrayBuffer();
      const tags = ExifReader.load(arrayBuffer);

      const metadata: Partial<PhotoMetadata> = {};

      // Extract date/time
      if (tags.DateTime?.description) {
        metadata.dateTime = tags.DateTime.description;
      } else if (tags.DateTimeOriginal?.description) {
        metadata.dateTime = tags.DateTimeOriginal.description;
      }

      // Extract GPS location
      if (tags.GPSLatitude && tags.GPSLongitude) {
        metadata.location = {
          latitude: this.convertDMSToDD(
            tags.GPSLatitude.description,
            tags.GPSLatitudeRef?.description
          ),
          longitude: this.convertDMSToDD(
            tags.GPSLongitude.description,
            tags.GPSLongitudeRef?.description
          ),
        };
      }

      // Extract camera info
      if (tags.Make?.description || tags.Model?.description) {
        metadata.camera = {
          make: tags.Make?.description,
          model: tags.Model?.description,
        };
      }

      // Extract dimensions
      if (tags.ImageWidth?.description && tags.ImageHeight?.description) {
        metadata.dimensions = {
          width: parseInt(tags.ImageWidth.description),
          height: parseInt(tags.ImageHeight.description),
        };
      }

      // Extract orientation
      if (tags.Orientation?.description) {
        metadata.orientation = parseInt(tags.Orientation.description);
      }

      return metadata;
    } catch (error) {
      console.warn("Failed to extract EXIF data:", error);
      return {};
    }
  }

  /**
   * Convert DMS (Degrees, Minutes, Seconds) to Decimal Degrees
   */
  private static convertDMSToDD(dms: string, ref: string): number {
    try {
      const parts = dms.split(",").map((part) => parseFloat(part.trim()));
      let dd = parts[0] + parts[1] / 60 + parts[2] / 3600;

      if (ref === "S" || ref === "W") {
        dd = dd * -1;
      }

      return dd;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Analyze filename for keywords and context
   */
  static analyzeFilename(filename: string): Partial<PhotoMetadata> {
    const metadata: Partial<PhotoMetadata> = {};
    const lowerFilename = filename.toLowerCase();

    // Common event keywords
    const eventKeywords = {
      birthday: "Birthday",
      wedding: "Wedding",
      christmas: "Christmas",
      holiday: "Holiday",
      vacation: "Vacation",
      graduation: "Graduation",
      anniversary: "Anniversary",
      party: "Party",
      dinner: "Dinner",
      lunch: "Lunch",
      breakfast: "Breakfast",
      picnic: "Picnic",
      bbq: "BBQ",
      barbecue: "BBQ",
      garden: "Gardening",
      beach: "Beach",
      park: "Park",
      museum: "Museum",
      concert: "Concert",
      festival: "Festival",
    };

    // Check for event keywords
    for (const [keyword, event] of Object.entries(eventKeywords)) {
      if (lowerFilename.includes(keyword)) {
        metadata.event = event;
        break;
      }
    }

    // Extract potential people names (basic pattern matching)
    const peopleKeywords = [
      "family",
      "mom",
      "dad",
      "grandma",
      "grandpa",
      "kids",
      "children",
    ];
    const foundPeople: string[] = [];

    for (const person of peopleKeywords) {
      if (lowerFilename.includes(person)) {
        foundPeople.push(person.charAt(0).toUpperCase() + person.slice(1));
      }
    }

    if (foundPeople.length > 0) {
      metadata.people = foundPeople;
    }

    // Generate keywords from filename
    const keywords = lowerFilename
      .replace(/\.[^/.]+$/, "") // Remove extension
      .split(/[_\-\s]+/) // Split on common separators
      .filter((word) => word.length > 2) // Filter short words
      .slice(0, 5); // Limit to 5 keywords

    if (keywords.length > 0) {
      metadata.keywords = keywords;
    }

    return metadata;
  }

  /**
   * Get location name from coordinates using reverse geocoding
   */
  static async getLocationName(
    latitude: number,
    longitude: number
  ): Promise<string | undefined> {
    try {
      // Using a free geocoding service (you might want to use a more reliable one in production)
      const response = await fetch(
        `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`
      );

      if (response.ok) {
        const data = await response.json();
        return data.city || data.locality || data.countryName;
      }
    } catch (error) {
      console.warn("Failed to get location name:", error);
    }

    return undefined;
  }

  /**
   * Extract comprehensive metadata from a photo file
   */
  static async extractMetadata(file: File): Promise<PhotoMetadata> {
    const metadata: PhotoMetadata = {};

    // Extract EXIF data
    const exifData = await this.extractExifData(file);
    Object.assign(metadata, exifData);

    // Analyze filename
    const filenameData = this.analyzeFilename(file.name);
    Object.assign(metadata, filenameData);

    // Get location name if coordinates are available
    if (metadata.location?.latitude && metadata.location?.longitude) {
      const locationName = await this.getLocationName(
        metadata.location.latitude,
        metadata.location.longitude
      );
      if (locationName) {
        metadata.location.address = locationName;
      }
    }

    // Get image dimensions if not from EXIF
    if (!metadata.dimensions) {
      try {
        const dimensions = await this.getImageDimensions(file);
        metadata.dimensions = dimensions;
      } catch (error) {
        console.warn("Failed to get image dimensions:", error);
      }
    }

    return metadata;
  }

  /**
   * Get image dimensions by loading the image
   */
  private static getImageDimensions(
    file: File
  ): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const url = URL.createObjectURL(file);

      img.onload = () => {
        URL.revokeObjectURL(url);
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight,
        });
      };

      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error("Failed to load image"));
      };

      img.src = url;
    });
  }

  /**
   * Generate a smart description based on metadata
   */
  static generateDescription(metadata: PhotoMetadata): string {
    const parts: string[] = [];

    if (metadata.event) {
      parts.push(`A photo from a ${metadata.event.toLowerCase()}`);
    } else {
      parts.push("A photo");
    }

    if (metadata.people && metadata.people.length > 0) {
      if (metadata.people.length === 1) {
        parts.push(`featuring ${metadata.people[0].toLowerCase()}`);
      } else {
        parts.push(`featuring ${metadata.people.join(", ").toLowerCase()}`);
      }
    }

    if (metadata.location?.address) {
      parts.push(`taken at ${metadata.location.address}`);
    }

    if (metadata.dateTime) {
      try {
        const date = new Date(metadata.dateTime);
        parts.push(`on ${date.toLocaleDateString()}`);
      } catch (error) {
        // Ignore date parsing errors
      }
    }

    return parts.join(" ") + ".";
  }
}
