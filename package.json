{"name": "memory-companion-pwa", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "dev:mobile": "vite --host 0.0.0.0", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@supabase/supabase-js": "^2.50.0", "@types/uuid": "^10.0.0", "date-fns": "^3.0.0", "exifreader": "^4.31.1", "framer-motion": "^10.16.16", "i18next": "^23.7.6", "i18next-browser-languagedetector": "^7.2.0", "lucide-react": "^0.344.0", "openai": "^5.6.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-i18next": "^13.5.0", "react-router-dom": "^6.20.1", "uuid": "^11.1.0", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vite-plugin-pwa": "^0.20.0"}}